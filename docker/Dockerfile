##
## Compile the project's source code
##
FROM amazoncorretto:21 AS build

ARG GITHUB_ACTOR=github-ci
ARG GITHUB_TOKEN

WORKDIR /app

COPY gradle gradle
COPY build.gradle settings.gradle gradlew ./
COPY config config
COPY src src

# Compile only the main code, don't bother with tests
RUN ./gradlew --no-daemon build -x test -x checkstyleMain -x checkstyleTest



##
## Download DataDog Java Agent
##
FROM alpine AS datadog

ARG DD_AGENT_VERSION=1.35.2
ARG DD_AGENT_CHECKSUM=********************************

ADD "https://repo1.maven.org/maven2/com/datadoghq/dd-java-agent/${DD_AGENT_VERSION}/dd-java-agent-${DD_AGENT_VERSION}.jar" /tmp/dd-java-agent.jar
RUN echo "$DD_AGENT_CHECKSUM  /tmp/dd-java-agent.jar" | md5sum -c



##
## Build the final image
##
FROM amazoncorretto:21

# Copy the source code
COPY --from=build /app/build/libs/*-SNAPSHOT.jar app.jar
COPY --from=datadog /tmp/dd-java-agent.jar dd-java-agent.jar

# Expose actuator port, grpc port and http port
EXPOSE 9001 9090 8700

ENTRYPOINT java $JVM_OPTS -javaagent:dd-java-agent.jar -jar app.jar
