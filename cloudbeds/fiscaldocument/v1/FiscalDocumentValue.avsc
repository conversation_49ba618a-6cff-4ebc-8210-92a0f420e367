{"fields": [{"name": "id", "type": "long"}, {"name": "number", "type": ["null", "string"]}, {"name": "property_id", "type": "long"}, {"name": "source_id", "type": "long"}, {"name": "user_id", "type": "long"}, {"name": "source_kind", "type": "com.cloudbeds.FiscalDocumentService.SourceKind"}, {"name": "kind", "type": "com.cloudbeds.FiscalDocumentService.FiscalDocumentKind"}, {"name": "status", "type": "com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus"}, {"name": "url", "type": ["null", "string"]}, {"name": "fail_reason", "type": ["null", "string"]}, {"name": "currency", "type": ["null", "string"]}, {"name": "amount", "type": ["null", "long"]}, {"name": "balance", "type": ["null", "long"]}, {"name": "linked_document_ids", "type": ["null", {"type": "array", "items": "long"}]}, {"name": "transaction_ids", "type": ["null", {"type": "array", "items": "long"}]}, {"name": "created_at", "type": {"type": "long", "connect.version": 1, "connect.name": "org.apache.kafka.connect.data.Timestamp", "logicalType": "timestamp-millis"}}, {"name": "updated_at", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "org.apache.kafka.connect.data.Timestamp", "logicalType": "timestamp-millis"}]}, {"name": "invoice_date", "type": ["null", {"connect.name": "org.apache.kafka.connect.data.Date", "connect.version": 1, "logicalType": "date", "type": "int"}]}, {"name": "file_path", "type": ["null", "string"]}, {"name": "external_id", "type": ["null", "string"]}, {"name": "origin", "type": ["null", "string"]}, {"name": "method", "type": ["null", {"type": "enum", "name": "Method", "symbols": ["ADJUSTMENT", "VOID"]}], "default": null}, {"name": "government_integration", "type": ["null", {"type": "record", "name": "GovernmentIntegration", "fields": [{"name": "number", "type": ["null", "string"]}, {"name": "series", "type": ["null", "string"]}, {"name": "status", "type": ["null", "string"]}, {"name": "qr", "type": ["null", {"type": "record", "name": "Qr", "fields": [{"name": "url", "type": ["null", "string"]}, {"name": "text", "type": ["null", "string"]}]}]}, {"name": "url", "type": ["null", "string"]}, {"name": "officialId", "type": ["null", "string"]}, {"name": "externalId", "type": ["null", "string"]}, {"name": "rectifyingInvoiceType", "type": ["null", "string"]}]}]}, {"name": "recipients", "type": ["null", {"type": "array", "items": "com.cloudbeds.FiscalDocumentService.Recipient"}]}, {"name": "version", "type": ["null", "int"], "default": null}], "name": "FiscalDocumentValue", "namespace": "com.cloudbeds.FiscalDocumentService", "type": "record"}