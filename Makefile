.DEFAULT_GOAL := help
SHELL       := bash
SOURCE_FOLDER  := .
PROJECT_NAME   := $(notdir $(CURDIR))

COLOR_START     = \e[91m\e[1m
COLOR_END       = \e[0m
SAY             = @printf "$(COLOR_START)%s\n$(COLOR_END)"
HOSTNAME	    = host.docker.internal:9090
ENV_PREFIX = stage
ISLAND = us2
ENVIRONMENT = ${ENV_PREFIX}-${ISLAND}
USERNAME = $(shell hostname)
KAFKA_CONSUMER_GROUP_ID=${ISLAND}.fiscal-document-service-${USERNAME}

DOCKER_AWS_HOST ?= fiscal-document-aws

ifneq (,$(wildcard .env))
include .env
export $(shell sed 's/=.*//' .env)
endif

##@ Local Environment
.PHONY: setup-env
setup-env: ## Create .env file from .env.example and fill values from AWS SSM
ifeq (, $(shell which envsubst))
	$(error "No envsubst in $(PATH), consider doing apt-get install gettext-base (https://command-not-found.com/envsubst)")
endif
	KAFKA_SERVER='$(shell $(MAKE) aws-ssm-get name=/eks/$(ENVIRONMENT)/confluent-cloud/clusters/urls field=bootstrap | sed 's,^SASL_SSL://,,')' \
	CONFLUENT_API_KEY='$(shell $(MAKE) aws-ssm-get name=/eks/${ENVIRONMENT}/accounting-service/confluent-cloud-access-keys field=api_id)' \
	CONFLUENT_API_SECRET='$(shell $(MAKE) aws-ssm-get name=/eks/${ENVIRONMENT}/accounting-service/confluent-cloud-access-keys field=api_secret)' \
	CONFLUENT_SCHEMA_REGISTRY_URL='$(shell $(MAKE) aws-ssm-get name=/eks/${ENVIRONMENT}/confluent-cloud/clusters/urls field=schema_registry)' \
	CONFLUENT_SCHEMA_KEY='$(shell $(MAKE) aws-ssm-get name=/eks/${ENVIRONMENT}/confluent-cloud/schema-registry/api-key field=id)' \
	CONFLUENT_SCHEMA_SECRET='$(shell $(MAKE) aws-ssm-get name=/eks/${ENVIRONMENT}/confluent-cloud/schema-registry/api-key field=secret)' \
	CONSUMER_GROUP_FISCAL_DOCUMENT_EVENTS=$(KAFKA_CONSUMER_GROUP_ID)-fiscal-documents-local-001 \
	CONSUMER_GROUP_INVOICES=$(KAFKA_CONSUMER_GROUP_ID)-invoices-local-001 \
	CONSUMER_GROUP_INVOICE_SETUP=$(KAFKA_CONSUMER_GROUP_ID)-invoice-setup-local-001 \
	CONSUMER_GROUP_FISCAL_DOCUMENTS_ENS=$(KAFKA_CONSUMER_GROUP_ID)-fiscal-documents-ens-001 \
	SENDGRID_API_KEY=$(shell aws ssm get-parameter --name /fiscal-document/sendgrid/api-key --with-decryption --output json | jq -r '.Parameter.Value') \
	REDIS_HOST=$(shell aws ssm get-parameter --name /eks/$(ENVIRONMENT)/fiscal-document-service/redis/host --with-decryption --output json 2>/dev/null | jq -r '.Parameter.Value' || echo 'localhost') \
	REDIS_PORT=$(shell aws ssm get-parameter --name /eks/$(ENVIRONMENT)/fiscal-document-service/redis/port --with-decryption --output json 2>/dev/null | jq -r '.Parameter.Value' || echo '6379')
	envsubst < .env.example > .env

	@$(eval KONG_OIDC_ORGANIZATION_URL=$(shell aws ssm get-parameter --name "/mfd/${ENV_PREFIX}/identity/signin_organization_url" --with-decryption --query 'Parameter.Value'))
	@$(eval KONG_OIDC_SERVER_ID=$(shell aws ssm get-parameter --name "/mfd/${ENV_PREFIX}/identity/auth/server_id" --with-decryption --query 'Parameter.Value'))
	@$(eval KONG_OIDC_CLIENT_ID=$(shell aws ssm get-parameter --name "/mfd/${ENV_PREFIX}/identity/client/id" --with-decryption --query 'Parameter.Value'))
	@$(eval KONG_OIDC_CLIENT_SECRET=$(shell aws ssm get-parameter --name "/mfd/${ENV_PREFIX}/identity/client/secret" --with-decryption --query 'Parameter.Value'))
	@$(eval KONG_OIDC_SESSION_SECRET=$(shell aws ssm get-parameter --name "/eks/${ENVIRONMENT}/shared/kong/oidc/mfd-idp/session" --with-decryption --query 'Parameter.Value'))

	@KONG_OIDC_DISCOVERY=${KONG_OIDC_ORGANIZATION_URL}/oauth2/${KONG_OIDC_SERVER_ID}/.well-known/oauth-authorization-server \
	KONG_OIDC_INSTROSPECTION_ENDPOINT=${KONG_OIDC_ORGANIZATION_URL}/oauth2/${KONG_OIDC_SERVER_ID}/v1/introspect \
	KONG_OIDC_CLIENT_ID=${KONG_OIDC_CLIENT_ID} \
	KONG_OIDC_CLIENT_SECRET=${KONG_OIDC_CLIENT_SECRET} \
	KONG_OIDC_SESSION_SECRET=${KONG_OIDC_SESSION_SECRET} \
	APP_PORT=$(shell grep ^APP_PORT .env.example | cut -d '=' -f2) \
	envsubst < local/kong/kong.example.yaml > local/kong/kong.yaml

##@ Application
.PHONY: start
start: ## Start the Java service directly with local profile
	@if [ ! -f .env ]; then \
		echo "Error: .env file not found. Run 'make setup-env' first to create it."; \
		exit 1; \
	fi
	@export $$(cat .env | grep -v '^#' | grep -v '^$$' | xargs) && java -jar build/libs/fiscal-document-service-0.0.1-SNAPSHOT.jar -Dspring.profiles.active=local

.PHONY: migrations-run
migrations-run: ## Run database migrations using liquibase container (detached)
	docker compose run -d --rm liquibase
	@echo "Waiting for liquibase to execute migrations"
	@while [ "$$\(docker compose ps -a | grep \"liquibase_run\"\)" ]; do \
		printf "."; \
		sleep 1; \
	done

##@ Docker Compose
.PHONY: docker-build
docker-build: ## Build and start all Docker Compose services (with --build)
	@docker compose up --build -d

.PHONY: docker-start
docker-start: ## Start all Docker Compose services (db, redis, kong, distributed-id-service, app, etc.)
	@docker compose up -d

.PHONY: docker-delete
docker-delete: ## Remove all Docker containers, volumes, and images not being used by other containers
	@if docker compose down --rmi all --volumes; then \
		echo "Docker images, volumes and its dependencies were deleted"; \
	else \
		docker compose down --volumes; \
		echo "Docker containers, volumes, but not images, were deleted"; \
	fi

.PHONY: docker-recreate
docker-recreate: ## Recreate db and liquibase containers from scratch
	docker-compose stop db liquibase
	docker-compose rm -f db liquibase
	docker-compose build --no-cache db liquibase
	docker-compose up -d db liquibase
	docker-compose up --force-recreate liquibase

##@ Utilities
.PHONY: aws-ssm-get
aws-ssm-get: ## Helper: Lookup AWS SSM parameter (usage: make aws-ssm-get name=... field=...)
	@aws ssm get-parameter --name '$(name)' --with-decryption --output json | jq -r '.Parameter.Value | fromjson | .$(field)'

##@ Help
.PHONY: help
help: ## Display this help
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)