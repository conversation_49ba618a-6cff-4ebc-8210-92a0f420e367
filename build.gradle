plugins {
    id 'checkstyle'
    id 'org.springframework.boot' version '3.3.5'
    id 'org.asciidoctor.jvm.convert' version '3.3.2'
    id 'idea'
    id 'java'
    id 'jacoco'
    id 'io.freefair.lombok' version '8.11'
    id 'org.openapi.generator' version '7.0.0'
}
apply plugin: 'io.spring.dependency-management'

group = 'com.cloudbeds'
version = '0.0.1-SNAPSHOT'

checkstyle {
    toolVersion('10.19.0')
}

checkstyleMain.source = "src/main/java"
checkstyleTest.source = "src/test/java"


// Ensure OpenAPI generation happens before any compilation
tasks.withType(JavaCompile) {
    dependsOn tasks.openApiGenerate
}

// Explicit dependencies for key tasks
compileJava.dependsOn tasks.openApiGenerate
compileTestJava.dependsOn tasks.openApiGenerate
test.dependsOn tasks.openApiGenerate
processResources.dependsOn tasks.openApiGenerate

jacoco {
    toolVersion = "0.8.12"
}

def preCheckinTasks = [
        'test',
        'jar',
        'jacocoTestCoverageVerification',
        'checkstyleMain',
        'checkstyleTest'
]

task('preCheckin', dependsOn: preCheckinTasks)
preCheckinTasks.inject(null) { target, task ->
    if (target != null) tasks[task].mustRunAfter target
    tasks[task]
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
        vendor = JvmVendorSpec.AMAZON
    }
}

compileJava {
    options.encoding = 'UTF-8'
    options.compilerArgs << "-Xlint:unchecked"
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
    maven {
        name = "GitHubPackages"
        url = uri("https://maven.pkg.github.com/cloudbeds/*")
        credentials {
            username = project.findProperty("GitHubPackagesUsername") ?: System.getenv("GITHUB_ACTOR")
            password = project.findProperty("GitHubPackagesToken") ?: System.getenv("GITHUB_TOKEN")
        }
    }
    maven {
        url = 'https://packages.confluent.io/maven/'
    }
}

jar {
    manifest {
        attributes('Main-Class': 'com.cloudbeds.exampleservice.ExampleServiceApplication')
    }
}

ext {
    set('snippetsDir', file("build/generated-snippets"))
}

dependencies {
    // Spring
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-mustache'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    // Cloudbeds
    implementation 'com.cloudbeds:dynamic-querying:0.2.10'
    implementation 'com.cloudbeds:java-shared:0.0.20'

    // Logger
    implementation 'net.logstash.logback:logstash-logback-encoder:7.2'

    // Swagger: Spring Doc
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.1.0'
    implementation 'io.swagger.core.v3:swagger-core-jakarta:2.2.21'

    // PDF
    implementation 'io.github.openhtmltopdf:openhtmltopdf-pdfbox:1.1.24' exclude group: 'commons-logging'

    //DB
    implementation 'org.postgresql:postgresql:42.7.5'
    implementation 'org.liquibase:liquibase-core:4.31.0'

    // Distibuted Ids
    implementation 'com.cloudbeds:distributed-id-spring-boot-starter:1.0.4'

    // Sendgrid - Emails
    implementation 'com.sendgrid:sendgrid-java:4.10.3'

    //gRPC
    implementation 'net.devh:grpc-spring-boot-starter:3.1.0.RELEASE' exclude group: 'io.grpc'
    implementation 'com.cloudbeds:protos-java:0.398.0'

    //micrometer
    implementation 'io.micrometer:micrometer-registry-datadog:latest.release'

    //Mapstruct
    implementation 'org.mapstruct:mapstruct:1.6.3'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.6.3'

    // Kafka
    implementation "org.springframework.kafka:spring-kafka"
    implementation "org.apache.kafka:kafka-clients"
    implementation 'io.confluent:kafka-avro-serializer:7.8.0'
    implementation 'io.confluent:kafka-schema-registry-client:7.8.0'
    implementation 'com.cloudbeds:avros-java:0.0.43'

    // AWS
    implementation 'io.awspring.cloud:spring-cloud-aws-starter:3.3.0'
    implementation 'software.amazon.awssdk:sts:2.31.14'
    implementation 'io.awspring.cloud:spring-cloud-aws-s3:3.3.0'

    //QR code
    implementation 'com.google.zxing:core:3.5.3'
    implementation 'com.google.zxing:javase:3.5.3'

    compileOnly 'org.apache.tomcat:tomcat-annotations-api:11.0.3'
    compileOnly 'org.hibernate.orm:hibernate-jpamodelgen:6.3.1.Final'

    implementation 'com.launchdarkly:launchdarkly-java-server-sdk:7.5.0'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    annotationProcessor 'org.hibernate.orm:hibernate-jpamodelgen'

    //test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.grpc:grpc-testing:1.7.0'
    testImplementation "org.testcontainers:testcontainers:1.20.4"
    testImplementation "org.testcontainers:junit-jupiter:1.20.4"
    testImplementation 'org.testcontainers:postgresql:1.20.4'
    testImplementation "io.grpc:grpc-testing:1.70.0"
    testImplementation 'org.testcontainers:kafka:1.17.3'
    testImplementation 'com.redis:testcontainers-redis:2.2.2'
    testImplementation 'io.specto:hoverfly-java-junit5:0.20.2'
}

tasks.named('asciidoctor') {
    inputs.dir snippetsDir
    dependsOn test
}


tasks.named('test', Test) {
    outputs.dir snippetsDir
    useJUnitPlatform()
}

test {
    useJUnitPlatform {
        includeEngines("junit-jupiter")
    }

    finalizedBy jacocoTestReport
}

jacocoTestReport {
    dependsOn test
    reports {
        xml.required = true
        csv.required = true
        html.required = true
    }
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: [
            ])
        }))
    }
}



openApiGenerate {
    generatorName = "spring"
    library = "spring-boot"
    inputSpec = project.file("src/main/resources/static/fiscal-document/openapi.yaml").toString()
    outputDir = project.file("${buildDir}/generate-resources/main").toString()
    apiPackage = 'com.cloudbeds.fiscaldocument.controller.api'
    modelPackage = 'com.cloudbeds.fiscaldocument.controller.model'
    configOptions.set(Map.of(
            "dateLibrary", "java8",
            "documentationProvider", "springdoc",
            "interfaceOnly", "true",
            "skipDefaultInterface", "true",
            "openApiNullable", "false",
            "useSpringBoot3", "true",
            "serializableModel", "true",
            "useTags", "true",
            "booleanGetterPrefix", "is",
            "hideGenerationTimestamp", "true"
    ))
    skipValidateSpec = false
    enablePostProcessFile = false
}

sourceSets {
    main {
        java {
            srcDir "${buildDir}/generate-resources/main/src/main/java"
        }
    }
    test {
        java {
            srcDir "${buildDir}/generate-resources/main/src/main/java"
        }
    }
}
