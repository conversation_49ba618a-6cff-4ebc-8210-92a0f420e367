{"type": "record", "name": "record", "namespace": "org.apache.flink.avro.generated", "fields": [{"name": "id", "type": "long"}, {"name": "property_id", "type": ["null", "long"], "default": null}, {"name": "group_profile_id", "type": ["null", "long"], "default": null}, {"name": "reservation_id", "type": ["null", "long"], "default": null}, {"name": "invoice_setup_id", "type": ["null", "long"], "default": null}, {"name": "invoice_number", "type": ["null", "long"], "default": null}, {"name": "hotel_details", "type": ["null", "string"], "default": null}, {"name": "type", "type": ["null", "string"], "default": null}, {"name": "group_profile", "type": ["null", "string"], "default": null}, {"name": "reservation", "type": ["null", "string"], "default": null}, {"name": "transactions", "type": ["null", "string"], "default": null}, {"name": "generate_date", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}, {"name": "pdf_url", "type": ["null", "string"], "default": null}, {"name": "email", "type": ["null", "string"], "default": null}, {"name": "sent", "type": [{"type": "int", "connect.default": 0, "connect.type": "int16"}, "null"], "default": 0}, {"name": "credit_note_date", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}, {"name": "credit_note_url", "type": ["null", "string"], "default": null}, {"name": "credit_note_reason", "type": ["null", "string"], "default": null}, {"name": "credit_note_number", "type": ["null", "long"], "default": null}, {"name": "credit_note_setup_id", "type": ["null", "long"], "default": null}, {"name": "status", "type": ["null", "string"], "default": null}, {"name": "created_at", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}, {"name": "updated_at", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}]}