{"type": "record", "name": "Value", "namespace": "com.cloudbeds.cdc.mfd.acessa.invoice_setup", "fields": [{"name": "id", "type": "long"}, {"name": "property_id", "type": "long"}, {"name": "title", "type": "string"}, {"name": "prefix", "type": "string"}, {"name": "suffix", "type": "string"}, {"name": "start_number", "type": ["null", "long"], "default": null}, {"name": "tax_id1", "type": "string"}, {"name": "tax_id2", "type": "string"}, {"name": "cpf", "type": "string"}, {"name": "due_date", "type": ["null", "int"], "default": null}, {"name": "arr_dep_date", "type": {"type": "int", "connect.default": 0, "connect.type": "int16"}, "default": 0}, {"name": "charge_breakdown", "type": {"type": "int", "connect.default": 0, "connect.type": "int16"}, "default": 0}, {"name": "custom_text", "type": "string"}, {"name": "generate_at_once", "type": {"type": "int", "connect.default": 0, "connect.type": "int16"}, "default": 0}, {"name": "when_generates", "type": {"type": "string", "connect.version": 1, "connect.parameters": {"allowed": "on_reservation_created,manually,at_check_out"}, "connect.default": "manually", "connect.name": "io.debezium.data.Enum"}, "default": "manually"}, {"name": "set_on", "type": {"type": "long", "connect.version": 1, "connect.name": "org.apache.kafka.connect.data.Timestamp", "logicalType": "timestamp-millis"}}, {"name": "expired_on", "type": ["null", {"type": "long", "connect.version": 1, "connect.name": "org.apache.kafka.connect.data.Timestamp", "logicalType": "timestamp-millis"}], "default": null}, {"name": "logo_id", "type": "long"}, {"name": "guest_lang", "type": {"type": "int", "connect.default": 0, "connect.type": "int16"}, "default": 0}, {"name": "invoice_lang", "type": ["null", "string"], "default": null}, {"name": "room_number", "type": {"type": "int", "connect.default": 1, "connect.type": "int16"}, "default": 1}, {"name": "tax_specifics", "type": {"type": "int", "connect.default": 0, "connect.type": "int16"}, "default": 0}, {"name": "is_compact", "type": [{"type": "int", "connect.default": 0, "connect.type": "int16"}, "null"], "default": 0}, {"name": "show_legal_company_name", "type": {"type": "int", "connect.default": 0, "connect.type": "int16"}, "default": 0}, {"name": "legal_company_name", "type": "string"}, {"name": "credit_note_same_settings", "type": [{"type": "int", "connect.default": 1, "connect.type": "int16"}, "null"], "default": 1}, {"name": "credit_note_title", "type": ["null", "string"], "default": null}, {"name": "credit_note_prefix", "type": ["null", "string"], "default": null}, {"name": "credit_note_suffix", "type": ["null", "string"], "default": null}, {"name": "credit_note_start_number", "type": ["null", "long"], "default": null}, {"name": "use_document_number", "type": [{"type": "int", "connect.default": 0, "connect.type": "int16"}, "null"], "default": 0}, {"name": "show_revenue_breakdown", "type": [{"type": "int", "connect.default": 0, "connect.type": "int16"}, "null"], "default": 0}, {"name": "__op", "type": ["null", "string"], "default": null}, {"name": "__table", "type": ["null", "string"], "default": null}, {"name": "__source_ts_ms", "type": ["null", "long"], "default": null}, {"name": "__deleted", "type": ["null", "string"], "default": null}, {"name": "__island", "type": ["null", "string"], "default": null}], "connect.name": "com.cloudbeds.cdc.mfd.acessa.invoice_setup.Value"}