{"source_id": 1, "property_id": 1, "source_kind": "RESERVATION", "type": "MFD_INVOICE_EVENT", "mfd_invoice_event": {"id": 1, "property_id": 1, "group_profile_id": null, "reservation_id": 1, "invoice_setup_id": 1, "invoice_number": 1, "type": "reservation", "transactions": "[{\"id\":\"1\"},{\"id\":\"2\"},{\"id\":\"3\"}]", "recipients": "[{\"id\":\"24891819335865\",\"firstName\":\"deposit\",\"type\":\"COMPANY\",\"address\":{\"address1\":\"\",\"address2\":\"\",\"city\":\"Walkerport\",\"state\":\"Minnesota\",\"zipCode\":\"245865\",\"country\":\"US\"},\"tax\":{\"id\":\"654673\",\"companyName\":\"deposit\"}}]", "generate_date": "2024-03-10T01:22:53Z", "pdf_url": "https://someurl.com/660c4a8c6c718.pdf", "email": null, "sent": false, "credit_note_date": "2024-03-10T01:22:53Z", "credit_note_url": "https://someurl.com/note-660c4a8c6c718.pdf", "credit_note_reason": "reason", "credit_note_number": 2, "credit_note_setup_id": 5, "status": "open", "user_id": null, "amount": 1000, "balance": 800, "currency": "USD", "external_settings_id": 1}, "invoice_create_event": null, "credit_note_create_event": null}