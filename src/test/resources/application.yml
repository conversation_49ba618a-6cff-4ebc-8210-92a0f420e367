spring:
  application:
    name: fiscal-document-service
  liquibase:
    enabled: true

  jpa:
    open-in-view: false
    show-sql: true
    properties:
      hibernate:
        format-sql: true
  kafka:
    consumer:
      key-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      properties:
        auto.offset.reset: earliest
        specific.avro.reader: true
        metadata.max.age.ms: 50
    producer:
      key-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      value-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      batch-size: 10
      properties:
        linger.ms: 30
    properties:
      schema.registry:
        url: mock://testUrl
  main:
    lazy-initialization: false

grpc:
  server:
    port: -1
  services:
    accounting:
      host: accounting-service.us2.cloudbeds-test.com
      port: 443
      shutdownTimeoutMs: 5000
    organization:
      host: organization-service.cloudbeds-test.com
      port: 443
      shutdownTimeoutMs: 5000
    group-profile:
      host: mfd-grpc.cloudbeds-test.com
      port: 443
      shutdownTimeoutMs: 5000
    booking:
      host: mfd-grpc.cloudbeds-test.com
      port: 443
      shutdownTimeoutMs: 5000
    guest:
      host: guest.cloudbeds-test.com
      port: 443
      shutdownTimeoutMs: 5000
    guest-requirements:
      host: guest-requirements.cloudbeds-test.com
      port: 443
      shutdownTimeoutMs: 5000
    marketplace:
      host: guest-requirements.cloudbeds-test.com
      port: 443
      shutdownTimeoutMs: 5000

cloudbeds-shared:
  default-authentication-config:
    enabled: false
  authentication-filter:
    enabled: true
  security-expressions:
    enabled: true

cloudbeds:
  mappings-service-url: http://test.cloudbeds.test/mapping/

application:
  aws:
    s3:
      bucket-name: "bucket-name"
      mfd-bucket-name: ""
    region: "us-west-1"
    endpoint: "http://localhost:5555"
  feature-management:
    launch-darkly:
      sdk-key: test123

sendgrid:
  api_key: test
  sender: <EMAIL>

topics:
  invoice_setup: us1.cdc.mfd.acessa.invoice_setup
  fiscal_document_events: us1.service.fiscal_document_events
  invoices: us1.cdc.mfd.acessa.invoice
  fiscal_documents: us1.service.fiscal_documents
consumer:
  invoice_setup:
    group_id: invoice_setup
    enabled: true
  fiscal_document_events:
    group_id: fiscal_document_events
    enabled: true
    poll_records: 100
  fiscal_documents_ens:
    group_id: fiscal_documents_ens
    enabled: true
  invoices:
    group_id: invoice
    enabled: true
  default:
    backoff:
      ms: 500

distributedid:
  enabled: true

sync:
  documents:
    interval-ms: 100
    initial-delay-ms: 10000000

kafka:
  producers:
    enabled: true

logging:
  level:
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

ens:
  url: "http://ens.test/"
