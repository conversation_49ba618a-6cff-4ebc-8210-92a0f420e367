package com.cloudbeds.fiscaldocument.ens;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.ens.enums.FiscalDocumentEnsEventType;
import com.cloudbeds.fiscaldocument.ens.events.FiscalDocumentEnsEvent;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.myfrontdesk.config.RestTemplateConfiguration;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.BasicResponse;
import com.cloudbeds.fiscaldocument.support.features.FeatureFlags;
import com.cloudbeds.fiscaldocument.support.features.ProviderInterface;
import com.cloudbeds.fiscaldocument.utils.DateTimeService;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestTemplate;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = { EnsService.class, RestTemplateConfiguration.class })
public class EnsServiceTest {
    @MockBean
    private RestTemplate restTemplate;

    @MockBean
    private ProviderInterface providerInterface;

    @MockBean
    private DateTimeService dateTimeService;

    @Autowired
    private EnsService service;

    @Test
    public void testSendAccountingEventEmptyEventListNoRequestIsSent() {
        service.sendFiscalDocumentEvents(1L, new ArrayList<>());
        service.sendFiscalDocumentEvents(1L, null);

        Mockito.verify(
            restTemplate,
            Mockito.times(0)
        ).exchange(
            Mockito.anyString(),
            Mockito.eq(HttpMethod.POST),
            Mockito.any(),
            Mockito.eq(BasicResponse.class)
        );
    }

    @Test
    public void testSendAccountingEventFeatureDisabledNoRequestIsSent() {
        var eventList = new ArrayList<FiscalDocumentEnsEvent>();
        eventList.add(new FiscalDocumentEnsEvent());

        Mockito.when(providerInterface.evaluatePropertyFlag(1L, FeatureFlags.ENS_ENABLED)).thenReturn(false);
        service.sendFiscalDocumentEvents(1L, eventList);

        Mockito.verify(
            restTemplate,
            Mockito.times(0)
        ).exchange(
            Mockito.anyString(),
            Mockito.eq(HttpMethod.POST),
            Mockito.any(),
            Mockito.eq(BasicResponse.class)
        );
    }

    private static Stream<Arguments> listArguments() {
        return Stream.of(
            Arguments.of(
                "invoice create",
                DocumentKind.INVOICE.toString(),
                FiscalDocumentEnsEventType.EVENT_TYPE_CREATE
            ),
            Arguments.of(
                "invoice update",
                DocumentKind.INVOICE.toString(),
                FiscalDocumentEnsEventType.EVENT_TYPE_UPDATE
            ),
            Arguments.of(
                "credit note create",
                DocumentKind.CREDIT_NOTE.toString(),
                FiscalDocumentEnsEventType.EVENT_TYPE_CREATE
            ),
            Arguments.of(
                "credit note update",
                DocumentKind.CREDIT_NOTE.toString(),
                FiscalDocumentEnsEventType.EVENT_TYPE_UPDATE
            ),
            Arguments.of(
                "receipt create",
                DocumentKind.RECEIPT.toString(),
                FiscalDocumentEnsEventType.EVENT_TYPE_CREATE
            ),
            Arguments.of(
                "receipt update",
                DocumentKind.RECEIPT.toString(),
                FiscalDocumentEnsEventType.EVENT_TYPE_UPDATE
            )
        );
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("listArguments")
    void testSendAccountingEventRequestIsSent(
        String name,
        FiscalDocumentKind documentKind,
        FiscalDocumentEnsEventType eventType
    ) {

        var event = new FiscalDocumentEnsEvent();
        event.setFiscalDocumentId("110");
        event.setDocumentKind(documentKind);
        event.setPropertyId("5");
        event.setEventType(eventType);

        Mockito.when(providerInterface.evaluatePropertyFlag(5L, FeatureFlags.ENS_ENABLED)).thenReturn(true);
        Mockito.when(dateTimeService.getCurrentDatetime()).thenReturn(LocalDateTime.ofInstant(
            Instant.parse("2025-05-16T13:12:57Z"), ZoneId.of("UTC")
        ));

        var responseEntity = Mockito.mock(ResponseEntity.class);
        Mockito.when(responseEntity.getStatusCode()).thenReturn(HttpStatus.ACCEPTED);

        var expectedEventsList = new ArrayList<>();
        expectedEventsList.add(getEnsRequestBody(documentKind, eventType));

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        var expectedEnsRequestBody = new HttpEntity<>(expectedEventsList, headers);

        var ensUrl = service.getEnsUrl() + "events";
        Mockito.when(
            restTemplate.exchange(
                Mockito.eq(ensUrl),
                Mockito.eq(HttpMethod.POST),
                Mockito.eq(expectedEnsRequestBody),
                Mockito.eq(BasicResponse.class)
            )
        ).thenReturn(responseEntity);

        service.sendFiscalDocumentEvents(5L, List.of(event));
        Mockito.verify(
            restTemplate,
            Mockito.times(1)
        ).exchange(
            Mockito.anyString(),
            Mockito.eq(HttpMethod.POST),
            Mockito.eq(expectedEnsRequestBody),
            Mockito.eq(BasicResponse.class)
        );
    }

    private HashMap<String, Object> getEnsRequestBody(
        FiscalDocumentKind documentKind,
        FiscalDocumentEnsEventType eventType
    ) {
        var data = new HashMap<String, String>();
        var keys = new ArrayList<String>();

        keys.add("property/" + 5);

        data.put("propertyId", "5");
        data.put("documentKind", documentKind.toString());
        data.put("id", "110");
        data.put("event", eventType.getValue());

        var timestamp = Timestamp.valueOf(dateTimeService.getCurrentDatetime());
        var eventToSend = new HashMap<String, Object>();
        eventToSend.put("keys", keys);
        eventToSend.put("event", "fiscal_document/" + eventType.getValue());

        eventToSend.put("timestamp", timestamp.toInstant().getEpochSecond());
        eventToSend.put("data", data);

        return eventToSend;
    }

    @BeforeEach
    void setUp() {
        Mockito.reset();
    }
}
