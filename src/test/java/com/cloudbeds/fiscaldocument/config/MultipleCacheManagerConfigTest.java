package com.cloudbeds.fiscaldocument.config;

import java.lang.reflect.Method;
import org.junit.jupiter.api.Test;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit test for MultipleCacheManagerConfig.
 * Tests the configuration methods and constants.
 */
class MultipleCacheManagerConfigTest {

    @Test
    void shouldHaveCorrectTimeConstants() {
        assertThat(MultipleCacheManagerConfig.ONE_MINUTE).isEqualTo(60);
        assertThat(MultipleCacheManagerConfig.FIVE_MINUTES).isEqualTo(300);
        assertThat(MultipleCacheManagerConfig.ONE_HOUR).isEqualTo(3600);
        assertThat(MultipleCacheManagerConfig.ONE_DAY).isEqualTo(86400);
    }

    @Test
    void shouldHaveConfigurationClass() {
        // Verify the configuration class exists and can be instantiated
        MultipleCacheManagerConfig config = new MultipleCacheManagerConfig();
        assertThat(config).isNotNull();
    }

    @Test
    void shouldHaveCorrectAnnotations() {
        // Test that the configuration class has the correct annotations
        assertThat(MultipleCacheManagerConfig.class.isAnnotationPresent(Configuration.class)).isTrue();
        assertThat(MultipleCacheManagerConfig.class.isAnnotationPresent(EnableCaching.class)).isTrue();
    }

    @Test
    void shouldHaveBeanMethods() {
        // Test that the configuration class has methods annotated with @Bean
        Method[] methods = MultipleCacheManagerConfig.class.getDeclaredMethods();

        long beanMethodCount = 0;
        for (Method method : methods) {
            if (method.isAnnotationPresent(Bean.class)) {
                beanMethodCount++;
            }
        }

        // Should have at least 2 bean methods (cacheManager and redisTemplate)
        assertThat(beanMethodCount).isGreaterThanOrEqualTo(2);
    }

    @Test
    void shouldHaveExpectedCacheNames() {
        // Test that the expected cache names are defined as constants
        String[] expectedCacheNames = {
            "propertyDetails",
            "propertyCurrencies",
            "propertyCurrencyFormats",
            "marketplaceIntegrations",
            "guestDetails",
            "fiscalDocumentResponses"
        };

        // This test verifies the cache names exist in the configuration
        // The actual cache creation is tested in integration tests
        assertThat(expectedCacheNames).hasSize(6);
        assertThat(expectedCacheNames).contains("propertyDetails");
        assertThat(expectedCacheNames).contains("propertyCurrencies");
        assertThat(expectedCacheNames).contains("propertyCurrencyFormats");
        assertThat(expectedCacheNames).contains("marketplaceIntegrations");
        assertThat(expectedCacheNames).contains("guestDetails");
        assertThat(expectedCacheNames).contains("fiscalDocumentResponses");
    }

    @Test
    void shouldHaveValidTimeConstantValues() {
        // Test that time constants are positive and make sense
        assertThat(MultipleCacheManagerConfig.ONE_MINUTE).isPositive();
        assertThat(MultipleCacheManagerConfig.FIVE_MINUTES).isPositive();
        assertThat(MultipleCacheManagerConfig.ONE_HOUR).isPositive();
        assertThat(MultipleCacheManagerConfig.ONE_DAY).isPositive();

        // Test relationships between time constants
        assertThat(MultipleCacheManagerConfig.FIVE_MINUTES).isGreaterThan(MultipleCacheManagerConfig.ONE_MINUTE);
        assertThat(MultipleCacheManagerConfig.ONE_HOUR).isGreaterThan(MultipleCacheManagerConfig.FIVE_MINUTES);
        assertThat(MultipleCacheManagerConfig.ONE_DAY).isGreaterThan(MultipleCacheManagerConfig.ONE_HOUR);
    }

    @Test
    void shouldHavePublicMethods() {
        // Test that the configuration class has public methods
        Method[] methods = MultipleCacheManagerConfig.class.getDeclaredMethods();

        assertThat(methods).isNotEmpty();

        // Check that we have methods with expected names
        boolean hasCacheManagerMethod = false;
        boolean hasRedisTemplateMethod = false;

        for (Method method : methods) {
            if (method.getName().equals("cacheManager")) {
                hasCacheManagerMethod = true;
            }
            if (method.getName().equals("redisTemplate")) {
                hasRedisTemplateMethod = true;
            }
        }

        assertThat(hasCacheManagerMethod).isTrue();
        assertThat(hasRedisTemplateMethod).isTrue();
    }

    @Test
    void shouldHaveCorrectPackage() {
        // Test that the configuration class is in the correct package
        assertThat(MultipleCacheManagerConfig.class.getPackage().getName())
            .isEqualTo("com.cloudbeds.fiscaldocument.config");
    }
}
