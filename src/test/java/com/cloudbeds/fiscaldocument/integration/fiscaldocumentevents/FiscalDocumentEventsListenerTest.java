package com.cloudbeds.fiscaldocument.integration.fiscaldocumentevents;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import com.cloudbeds.accounting.v1.InternalCode;
import com.cloudbeds.accounting.v1.Source;
import com.cloudbeds.accounting.v1.Transaction;
import com.cloudbeds.fiscaldocument.FiscalDocumentApplication;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentTransaction;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.grpc.accounting.AccountingService;
import com.cloudbeds.fiscaldocument.grpc.mfd.GroupProfileServiceClient;
import com.cloudbeds.fiscaldocument.grpc.mfd.PropertyCurrencyServiceClient;
import com.cloudbeds.fiscaldocument.grpc.organization.PropertyServiceClient;
import com.cloudbeds.fiscaldocument.integration.BaseIntegrationTest;
import com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil;
import com.cloudbeds.fiscaldocument.listeners.FiscalDocumentEventListener;
import com.cloudbeds.fiscaldocument.repositories.DocumentSequenceRepository;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentRecipientRepository;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentRepository;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentTransactionRepository;
import com.cloudbeds.fiscaldocument.services.BookingService;
import com.cloudbeds.fiscaldocument.services.CurrencyService;
import com.cloudbeds.fiscaldocument.services.EmailService;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentRecipientService;
import com.cloudbeds.fiscaldocument.services.S3Service;
import com.cloudbeds.organization.v1.Property;
import com.fasterxml.jackson.core.type.TypeReference;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@Slf4j
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = {FiscalDocumentApplication.class},
    properties = {
        "topics.fiscal_document_events=service.fiscal_document.fiscal_document_events_test",
        "sync.documents.initial-delay-ms=100"

    }
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FiscalDocumentEventsListenerTest extends BaseIntegrationTest {
    @Value(value = "${topics.fiscal_document_events}")
    private String topic;

    @Autowired
    private FiscalDocumentRepository fiscalDocumentRepository;

    @Autowired
    private DocumentSequenceRepository documentSequenceRepository;

    @Autowired
    private FiscalDocumentTransactionRepository fiscalDocumentTransactionRepository;

    @Autowired
    private FiscalDocumentRecipientRepository fiscalDocumentRecipientRepository;
    @SpyBean
    private FiscalDocumentEventListener fiscalDocumentEventListener;

    @MockBean
    private AccountingService accountingService;

    @MockBean
    private PropertyServiceClient propertyServiceClient;

    @MockBean
    private PropertyCurrencyServiceClient propertyCurrencyServiceClient;

    @MockBean
    private CurrencyService currencyService;

    @MockBean
    private S3Service s3Service;

    @MockBean
    private EmailService emailService;

    @MockBean
    private BookingService bookingService;

    @MockBean
    private GroupProfileServiceClient groupProfileServiceClient;

    @MockBean
    private FiscalDocumentRecipientService fiscalDocumentRecipientService;

    @BeforeEach
    public void setUp() {
        setupProperty(1L, false);
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("processMessagesSource")
    public void processMessages(
        String name,
        String valuePath,
        List<FiscalDocument> initialDocuments,
        List<FiscalDocument> expectedDocuments,
        List<Transaction> transactions,
        Property property,
        Long expectedNumber
    ) throws Exception {
        doReturn(List.of(DEFAULT_PROPERTY)).when(propertyServiceClient).listProperties(
            List.of(1L)
        );

        doReturn(null).when(s3Service).uploadContent(any(), any(), any());
        doReturn(transactions)
            .when(accountingService)
            .getPostedTransactions(any(), any(), any(), any());

        doReturn(DEFAULT_RESERVATION).when(bookingService).getBooking(any(), any());
        doReturn(List.of()).when(bookingService).getBookings(any(), any());
        doReturn(property).when(propertyServiceClient).getProperty(any());
        doReturn(List.of(DEFAULT_GROUP_PROFILE)).when(groupProfileServiceClient).listGroups(any(), any());


        doReturn(Map.of("MQ==", List.of(1L), "Mg==", List.of(2L)))
            .when(accountingService).getTransactionIdsFromMfdTransactions(
                List.of("MQ==", "Mg==")
            );

        fiscalDocumentRepository.saveAll(initialDocuments);
        documentConfigService.createConfig(
            CB_PROPERTY_ID, DocumentKind.RECTIFY_INVOICE, true,
            documentSequenceRepository.findAll().stream().findAny().orElseThrow()
        );
        var messages = getEvents(valuePath);

        produceMessagesAndWait(messages);

        var documents = fiscalDocumentRepository.findAll();

        var sequence = documentSequenceRepository.findAll().stream().findAny().orElseThrow();

        assertEquals(expectedNumber, sequence.getNumber(), "Error in sequence number");

        var expectedDocumentsMap = expectedDocuments.stream().collect(Collectors.toMap(
            FiscalDocument::getNumber,
            Function.identity()
        ));
        assertEquals(expectedDocuments.size(), documents.size());

        for (var document : documents) {
            var expectedDocument = expectedDocumentsMap.get(document.getNumber());
            Assertions.assertAll("Errors in document " + document.getNumber(),
                () -> assertEquals(
                    expectedDocument.getPropertyId(), document.getPropertyId(),
                    "Mismatch in document's property ID"),
                () -> assertEquals(
                    expectedDocument.getNumber(), document.getNumber(),
                    "Mismatch in document's number"),
                () -> assertEquals(
                    expectedDocument.getSequenceNumber(), document.getSequenceNumber(),
                    "Mismatch in document's sequence number"),
                () -> assertEquals(
                    expectedDocument.getInvoiceDate(),
                    document.getInvoiceDate(),
                    "Mismatch in document's invoice date"),
                () -> assertEquals(
                    expectedDocument.getKind(), document.getKind(),
                    "Mismatch in document's kind"),
                () -> assertEquals(
                    expectedDocument.getUrl(), document.getUrl(),
                    "Mismatch in document's URL"),
                () -> assertEquals(
                    expectedDocument.getExternalId(), document.getExternalId(),
                    "Mismatch in document's external ID"),
                () -> assertEquals(
                    expectedDocument.getOrigin(), document.getOrigin(),
                    "Mismatch in document's external source"),
                () -> assertEquals(
                    expectedDocument.getSourceKind(), document.getSourceKind(),
                    "Mismatch in document's source kind"),
                () -> assertEquals(
                    expectedDocument.getSourceId(), document.getSourceId(),
                    "Mismatch in document's source ID"),
                () -> assertEquals(
                    expectedDocument.getStatus(), document.getStatus(),
                    "Mismatch in document's status"),
                () -> assertEquals(
                    expectedDocument.getAmount(), document.getAmount(),
                    "Mismatch in document's amount"),
                () -> assertEquals(
                    expectedDocument.getBalance(), document.getBalance(),
                    "Mismatch in document's balance"),
                () -> assertEquals(
                    expectedDocument.getCurrency(), document.getCurrency(),
                    "Mismatch in document's currency")
            );

            assertEquals(
                expectedDocument.getTransactionList().stream()
                    .map(FiscalDocumentTransaction::getTransactionId).toList(),
                fiscalDocumentTransactionRepository.findAllByFiscalDocumentId(document.getId())
                    .stream().map(FiscalDocumentTransaction::getTransactionId).toList(),
                "Error in document " + document.getNumber()
            );

            var expectedRecipientIds = expectedDocument.getRecipients() == null
                ? List.of()
                : expectedDocument.getRecipients().stream()
                .map(FiscalDocumentRecipient::getRecipientId)
                .filter(Objects::nonNull)
                .toList();

            var actualRecipientIds = fiscalDocumentRecipientRepository.findAllByFiscalDocumentId(document.getId())
                .stream()
                .map(FiscalDocumentRecipient::getRecipientId)
                .toList();

            assertEquals(expectedRecipientIds, actualRecipientIds, "Error in document " + document.getNumber());

        }
    }

    public Stream<Arguments> processMessagesSource() throws IOException {
        return Stream.of(
            Arguments.of(
                "[CDC] create invoice",
                "/integration/invoice-events/create/value.json",
                List.of(),
                getFiscalDocuments("integration/invoice-events/create/expected.json"),
                new ArrayList<Transaction>(),
                DEFAULT_PROPERTY,
                3L
            ),
            Arguments.of(
                "[CDC] Update invoice status",
                "/integration/invoice-events/update-status/value.json",
                List.of(),
                getFiscalDocuments("integration/invoice-events/update-status/expected.json"),
                new ArrayList<Transaction>(),
                DEFAULT_PROPERTY,
                3L
            ),
            Arguments.of(
                "Create invoice from event",
                "/integration/invoice-events/create-invoice-event/value.json",
                getFiscalDocuments("integration/invoice-events/create-invoice-event/initial.json"),
                getFiscalDocuments("integration/invoice-events/create-invoice-event/expected.json"),
                new ArrayList<Transaction>(),
                DEFAULT_PROPERTY,
                1L
            ),
            Arguments.of(
                "Create invoice from event with invoiced transactions",
                "/integration/invoice-events/create-invoice-event-bad-transactions/value.json",
                getFiscalDocuments("integration/invoice-events/create-invoice-event-bad-transactions/initial.json"),
                getFiscalDocuments("integration/invoice-events/create-invoice-event-bad-transactions/expected.json"),
                new ArrayList<Transaction>(),
                DEFAULT_PROPERTY,
                1L
            ),
            Arguments.of(
                "Create credit note from event",
                "/integration/invoice-events/create-credit-note/value.json",
                getFiscalDocuments("integration/invoice-events/create-credit-note/initial.json"),
                getFiscalDocuments("integration/invoice-events/create-credit-note/expected.json"),
                new ArrayList<Transaction>(),
                DEFAULT_PROPERTY,
                1L
            ),
            Arguments.of(
                "Create credit note for adjustment",
                "/integration/invoice-events/create-credit-note-adjustment/value.json",
                getFiscalDocuments("integration/invoice-events/create-credit-note-adjustment/initial.json"),
                getFiscalDocuments("integration/invoice-events/create-credit-note-adjustment/expected.json"),
                new ArrayList<Transaction>(),
                DEFAULT_PROPERTY,
                1L
            ),
            Arguments.of(
                "Create credit note for adjustment with invalid transactions",
                "/integration/invoice-events/create-credit-note-adjustment-invalid/value.json",
                getFiscalDocuments("integration/invoice-events/create-credit-note-adjustment-invalid/initial.json"),
                getFiscalDocuments("integration/invoice-events/create-credit-note-adjustment-invalid/expected.json"),
                new ArrayList<Transaction>(),
                DEFAULT_PROPERTY,
                0L
            ),
            Arguments.of(
                "Create credit note for adjustment with void invoice",
                "/integration/invoice-events/create-credit-note-adjustment-void-invoice/value.json",
                getFiscalDocuments("integration/invoice-events/create-credit-note-adjustment-void-invoice/initial.json"),
                getFiscalDocuments("integration/invoice-events/create-credit-note-adjustment-void-invoice/expected.json"),
                new ArrayList<Transaction>(),
                DEFAULT_PROPERTY,
                0L
            ),
            Arguments.of(
                "Create credit note for adjustment with not adjustment transactions",
                "/integration/invoice-events/create-credit-note-adjustment-not-adjustment-transactions/value.json",
                getFiscalDocuments("integration/invoice-events/create-credit-note-adjustment-not-adjustment-transactions/initial.json"),
                getFiscalDocuments("integration/invoice-events/create-credit-note-adjustment-not-adjustment-transactions/expected.json"),
                List.of(
                    Transaction.newBuilder()
                        .setSourceId(1L)
                        .setSource(Source.SOURCE_RESERVATION)
                        .setInternalCode(InternalCode.newBuilder()
                            .setCode("1000")
                            .build()
                        )
                        .setCurrency("USD")
                        .setCurrencyScale(2)
                        .build()
                ),
                DEFAULT_PROPERTY,
                0L
            ),
            Arguments.of(
                "Create invoice from integration",
                "/integration/invoice-events/create-integration/value.json",
                getFiscalDocuments("integration/invoice-events/create-integration/initial.json"),
                getFiscalDocuments("integration/invoice-events/create-integration/expected.json"),
                new ArrayList<Transaction>(),
                DEFAULT_PROPERTY,
                0L
            ),
            Arguments.of(
                "Create invoice from integration with failed integration status",
                "/integration/invoice-events/create-integration-failed-integration/value.json",
                getFiscalDocuments("integration/invoice-events/create-integration-failed-integration/initial.json"),
                getFiscalDocuments("integration/invoice-events/create-integration-failed-integration/expected.json"),
                new ArrayList<Transaction>(),
                DEFAULT_PROPERTY,
                0L
            ),
            Arguments.of(
                "Rectify invoice from event",
                "/integration/invoice-events/create-rectify-invoice/value.json",
                getFiscalDocuments("integration/invoice-events/create-rectify-invoice/initial.json"),
                getFiscalDocuments("integration/invoice-events/create-rectify-invoice/expected.json"),
                new ArrayList<Transaction>(),
                SPAIN_PROPERTY,
                0L
            )
        );
    }

    private List<FiscalDocument> getFiscalDocuments(String path) throws IOException {
        return ResourceReaderUtil.getObjectFromJsonResource(path, new TypeReference<>() {
        });
    }

    private void produceMessagesAndWait(
        List<FiscalDocumentEventValue> messages
    ) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(messages.size());
        Mockito.doAnswer(invocation -> {
            Object result = invocation.callRealMethod();
            List<ConsumerRecord<GenericRecord, GenericRecord>> receivedMessages = invocation.getArgument(0);
            receivedMessages.forEach(message -> latch.countDown());
            return result;
        }).when(fiscalDocumentEventListener).listenMessages(any(), any());

        try (var producer = getFiscalDocumentEventProducer()) {
            messages.forEach(
                value -> {
                    var key = new FiscalDocumentEventKey(value.getPropertyId());
                    producer.send(new ProducerRecord<>(topic, key, value));
                }
            );
        }
        assertTrue(latch.await(10000, TimeUnit.SECONDS));
    }

    protected List<FiscalDocumentEventValue> getEvents(String path) throws IOException {
        return ResourceReaderUtil.getObjectFromJsonResource(path, new TypeReference<>() {
        });
    }
}
