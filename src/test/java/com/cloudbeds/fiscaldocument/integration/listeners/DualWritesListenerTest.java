package com.cloudbeds.fiscaldocument.integration.listeners;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import com.cloudbeds.fiscaldocument.FiscalDocumentApplication;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.integration.BaseIntegrationTest;
import com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil;
import com.cloudbeds.fiscaldocument.listeners.DualWritesListener;
import com.cloudbeds.fiscaldocument.myfrontdesk.PrivateApiMfdService;
import com.cloudbeds.fiscaldocument.myfrontdesk.config.MfdSdkProperties;
import com.cloudbeds.fiscaldocument.myfrontdesk.exception.MfdResponseException;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentRepository;
import com.cloudbeds.fiscaldocument.services.S3Service;
import com.fasterxml.jackson.core.type.TypeReference;
import io.specto.hoverfly.junit.core.Hoverfly;
import io.specto.hoverfly.junit.core.HoverflyConfig;
import io.specto.hoverfly.junit.core.HoverflyMode;
import io.specto.hoverfly.junit.core.SimulationSource;
import io.specto.hoverfly.junit.dsl.HoverflyDsl;
import io.specto.hoverfly.junit.dsl.ResponseCreators;
import io.specto.hoverfly.junit.dsl.StubServiceBuilder;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Stream;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.MediaType;
import org.springframework.kafka.support.Acknowledgment;

import static com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil.getObjectFromJsonResource;
import static io.specto.hoverfly.junit.core.SimulationSource.dsl;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = {FiscalDocumentApplication.class},
    properties = {
        "topics.fiscal_documents=fiscal_documents_dual_writes",
        "consumer.dual_writes.poll_records=1",
        "consumer.dual_writes.enabled=true",
        "consumer.fiscal_documents_ens.enabled=false"
    }
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class DualWritesListenerTest extends BaseIntegrationTest {
    @org.springframework.beans.factory.annotation.Value(value = "${topics.fiscal_documents}")
    private String topic;

    protected final KafkaProducer<
        FiscalDocumentKey,
        FiscalDocumentValue
        > producer = getFiscalDocumentProducer();

    @SpyBean
    private DualWritesListener dualWritesListener;

    @SpyBean
    private PrivateApiMfdService privateApiMfdService;

    @SpyBean
    private FiscalDocumentRepository fiscalDocumentRepository;

    @MockBean
    private S3Service s3Service;

    @Autowired
    protected MfdSdkProperties mfdSdkProperties;

    @ParameterizedTest(name = "{0}")
    @MethodSource("fiscalDocumentsPositiveScenarios")
    void testFiscalDocumentsListener(
        String scenarioName,
        List<FiscalDocument> fiscalDocuments,
        FiscalDocumentValue fiscalDocumentValue,
        boolean mappingServiceResponse,
        Boolean mfdServiceSuccess,
        String requestBody,
        boolean expectException,
        boolean postRequestSentToMfd
    ) throws InterruptedException, IOException {
        var hoverfly = new Hoverfly(HoverflyConfig.localConfigs().proxyLocalHost(), HoverflyMode.SIMULATE);
        hoverfly.start();

        hoverfly.simulate(
            getMappingServiceSimulation(mappingServiceResponse),
            getInvoiceSimulation(mfdServiceSuccess, requestBody)
        );

        Mockito.when(s3Service.getContent(any(), any())).thenReturn(new byte[] {1, 2, 3});

        fiscalDocuments.forEach(fiscalDocument ->
            fiscalDocument.setCreatedAt(LocalDateTime.of(2025, 6, 20, 11, 12, 13))
        );

        fiscalDocumentRepository.saveAll(fiscalDocuments);
        addMessagesAndWait(fiscalDocumentValue, expectException, postRequestSentToMfd);

        hoverfly.close();
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("fiscalDocumentsNegativeScenarios")
    void testFiscalDocumentsListenerNegativeCases(
        String scenarioName,
        List<FiscalDocument> fiscalDocuments,
        FiscalDocumentValue fiscalDocumentValue,
        boolean mappingServiceResponse,
        Boolean mfdServiceSuccess,
        String requestBody
    ) throws IOException {
        var hoverfly = new Hoverfly(HoverflyConfig.localConfigs().proxyLocalHost(), HoverflyMode.SIMULATE);
        hoverfly.start();

        hoverfly.simulate(
            getMappingServiceSimulation(mappingServiceResponse),
            getInvoiceSimulation(mfdServiceSuccess, requestBody)
        );

        Mockito.when(s3Service.getContent(any(), any())).thenReturn(new byte[] {1, 2, 3});

        fiscalDocuments.forEach(fiscalDocument ->
            fiscalDocument.setCreatedAt(LocalDateTime.of(2025, 6, 20, 11, 12, 13))
        );

        fiscalDocumentRepository.saveAll(fiscalDocuments);

        assertThrows(MfdResponseException.class, () -> dualWritesListener.listenMessages(
            List.of(new ConsumerRecord<>(
                topic,
                1,
                0,
                new FiscalDocumentKey(fiscalDocumentValue.getId()), fiscalDocumentValue)
            ),
            Mockito.mock(Acknowledgment.class)
        ));

        hoverfly.close();
    }

    private void addMessagesAndWait(
        FiscalDocumentValue message,
        boolean expectException,
        boolean postRequestSentToMfd
    ) throws InterruptedException  {
        CountDownLatch latch = new CountDownLatch(1);
        AtomicBoolean exceptionIsThrown = new AtomicBoolean(false);

        Mockito.doAnswer(invocation -> {
            try {
                var result = invocation.callRealMethod();
                latch.countDown();
                return result;
            } catch (Exception ex) {
                exceptionIsThrown.set(true);
                latch.countDown();
                throw ex;
            }
        }).when(dualWritesListener).listenMessages(any(), any());

        producer.send(new ProducerRecord<>(topic, new FiscalDocumentKey(message.getId()), message));

        assertTrue(latch.await(1000, TimeUnit.SECONDS));

        if (expectException) {
            assertTrue(exceptionIsThrown.get(), "Failed to assert that exception was thrown");
        } else {
            assertFalse(exceptionIsThrown.get(), "Exception was thrown");
        }

        Mockito.verify(privateApiMfdService, Mockito.times(postRequestSentToMfd ? 1 : 0)).postFiscalDocument(any());
    }

    private SimulationSource getMappingServiceSimulation(boolean returnSuccess) throws IOException {
        Long propertyId = 1L;
        var serviceUrl = new URL(mfdSdkProperties.getMappingsServiceUrl());
        var path = serviceUrl.getPath().endsWith("/")
            ? serviceUrl.getPath().substring(0, serviceUrl.getPath().length() - 1)
            : serviceUrl.getPath();

        var requestMatcherBuilder = HoverflyDsl.service(serviceUrl.getProtocol() + "://" + serviceUrl.getHost())
            .get(path + "/v1.0/mfd/property")
            .queryParam("id", propertyId);

        StubServiceBuilder stubServiceBuilder;

        if (returnSuccess) {
            stubServiceBuilder = requestMatcherBuilder.willReturn(
                ResponseCreators.success(
                    ResourceReaderUtil.getResourceAsString("integration/dual-writes/mapping-service-success.json"),
                    MediaType.APPLICATION_JSON_VALUE
                )
            );
        } else {
            stubServiceBuilder = requestMatcherBuilder.willReturn(ResponseCreators.notFound());
        }

        return dsl(stubServiceBuilder);
    }

    private SimulationSource getInvoiceSimulation(Boolean returnSuccess, String body) throws IOException {
        var requestMatcherBuilder = HoverflyDsl.service("http://api.cloudbeds-test.com")
            .post("/api/accounting/invoice")
            .body(body);

        StubServiceBuilder stubServiceBuilder;

        if (returnSuccess != null) {
            if (returnSuccess) {
                stubServiceBuilder = requestMatcherBuilder
                    .willReturn(
                        ResponseCreators.success(
                            ResourceReaderUtil.getResourceAsString("integration/dual-writes/post-invoice-success-true.json"),
                            MediaType.APPLICATION_JSON_VALUE
                        )
                    );
            } else {
                stubServiceBuilder = requestMatcherBuilder
                    .willReturn(
                        ResponseCreators.success(
                            ResourceReaderUtil.getResourceAsString("integration/dual-writes/post-invoice-success-false.json"),
                            MediaType.APPLICATION_JSON_VALUE
                        )
                    );
            }
        } else {
            stubServiceBuilder = requestMatcherBuilder.willReturn(ResponseCreators.notFound());
        }

        return dsl(stubServiceBuilder);
    }

    /**
     * Positive scenarios.
     *
     * @return scenarios
     */
    public Stream<Arguments> fiscalDocumentsPositiveScenarios() throws IOException {
        return Stream.of(
            Arguments.of(
                "record-absent-in-db",
                List.of(),
                getObjectFromJsonResource(
                    "integration/dual-writes/reservation-invoice/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                true,
                "",
                false,
                false
            ),
            Arguments.of(
                "record-without-number",
                List.of(),
                getObjectFromJsonResource(
                    "integration/dual-writes/record-without-number/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                true,
                "",
                false,
                false
            ),
            Arguments.of(
                "record-mfd-origin",
                List.of(),
                getObjectFromJsonResource(
                    "integration/dual-writes/record-mfd-origin/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                true,
                "",
                false,
                false
            ),
            Arguments.of(
                "record-receipt",
                List.of(),
                getObjectFromJsonResource(
                    "integration/dual-writes/record-receipt/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                true,
                "",
                false,
                false
            ),
            Arguments.of(
                "record-rectify-invoice",
                List.of(),
                getObjectFromJsonResource(
                    "integration/dual-writes/record-rectify-invoice/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                true,
                "",
                false,
                false
            ),
            Arguments.of(
                "record-adjustment",
                List.of(),
                getObjectFromJsonResource(
                    "integration/dual-writes/record-adjustment/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                true,
                "",
                false,
                false
            ),
            Arguments.of(
                "reservation-invoice-sent-to-mfd",
                getFiscalDocuments("integration/dual-writes/reservation-invoice/db_fiscal_documents.json"),
                getObjectFromJsonResource(
                    "integration/dual-writes/reservation-invoice/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                true,
                "id=1"
                + "&propertyId=1"
                + "&sourceId=33"
                + "&sourceKind=RESERVATION"
                + "&number=1"
                + "&status=OPEN"
                + "&kind=INVOICE"
                + "&userId=0"
                + "&fileBase64=AQID"
                + "&createdAt=2025-06-20+11%3A12%3A13",
                false,
                true
            ),
            Arguments.of(
                "reservation-credit-note-sent-to-mfd",
                getFiscalDocuments("integration/dual-writes/reservation-credit-note/db_fiscal_documents.json"),
                getObjectFromJsonResource(
                    "integration/dual-writes/reservation-credit-note/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                true,
                "id=2"
                    + "&propertyId=1"
                    + "&sourceId=33"
                    + "&sourceKind=RESERVATION"
                    + "&number=2"
                    + "&status=OPEN"
                    + "&kind=CREDIT_NOTE"
                    + "&userId=2"
                    + "&fileBase64=AQID"
                    + "&createdAt=2025-06-20+11%3A12%3A13"
                    + "&parentId=1",
                false,
                true
            ),
            Arguments.of(
                "group-profile-invoice-sent-to-mfd",
                getFiscalDocuments("integration/dual-writes/group-invoice/db_fiscal_documents.json"),
                getObjectFromJsonResource(
                    "integration/dual-writes/group-invoice/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                true,
                "id=1"
                    + "&propertyId=1"
                    + "&sourceId=33"
                    + "&sourceKind=GROUP_PROFILE"
                    + "&number=1"
                    + "&status=OPEN"
                    + "&kind=INVOICE"
                    + "&userId=0"
                    + "&fileBase64=AQID"
                    + "&createdAt=2025-06-20+11%3A12%3A13",
                false,
                true
            )
        );
    }

    /**
     * Negative scenarios.
     *
     * @return scenarios
     */
    public Stream<Arguments> fiscalDocumentsNegativeScenarios() throws IOException {
        return Stream.of(
            Arguments.of(
                "mapping service error",
                getFiscalDocuments("integration/dual-writes/mapping-service-fail/db_fiscal_documents.json"),
                getObjectFromJsonResource(
                    "integration/dual-writes/mapping-service-fail/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                false,
                true,
                ""
            ),
            Arguments.of(
                "mfd error",
                getFiscalDocuments("integration/dual-writes/mfd-fail/db_fiscal_documents.json"),
                getObjectFromJsonResource(
                    "integration/dual-writes/mfd-fail/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                null,
                "id=1"
                + "&propertyId=1"
                + "&sourceId=33"
                + "&sourceKind=RESERVATION"
                + "&number=1"
                + "&status=OPEN"
                + "&kind=INVOICE"
                + "&userId=0"
                + "&fileBase64=AQID"
                + "&createdAt=2025-06-20+11%3A12%3A13"
            ),
            Arguments.of(
                "mfd success = false",
                getFiscalDocuments("integration/dual-writes/mfd-success-false/db_fiscal_documents.json"),
                getObjectFromJsonResource(
                    "integration/dual-writes/mfd-success-false/fiscal_document_event.json",
                    FiscalDocumentValue.class
                ),
                true,
                false,
                "id=1"
                + "&propertyId=1"
                + "&sourceId=33"
                + "&sourceKind=RESERVATION"
                + "&number=1"
                + "&status=OPEN"
                + "&kind=INVOICE"
                + "&userId=0"
                + "&fileBase64=AQID"
                + "&createdAt=2025-06-20+11%3A12%3A13"
            )
        );
    }

    private List<FiscalDocument> getFiscalDocuments(String path) throws IOException {
        return ResourceReaderUtil.getObjectFromJsonResource(path, new TypeReference<>() {
        });
    }
}
