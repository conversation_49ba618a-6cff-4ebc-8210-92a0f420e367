package com.cloudbeds.fiscaldocument.integration.listeners;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import com.cloudbeds.fiscaldocument.FiscalDocumentApplication;
import com.cloudbeds.fiscaldocument.ens.EnsService;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.integration.BaseIntegrationTest;
import com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil;
import com.cloudbeds.fiscaldocument.listeners.SendDocumentEnsListener;
import com.cloudbeds.fiscaldocument.support.features.FeatureFlags;
import com.cloudbeds.fiscaldocument.utils.DateTimeService;
import com.fasterxml.jackson.core.type.TypeReference;
import io.specto.hoverfly.junit.core.Hoverfly;
import io.specto.hoverfly.junit.core.SimulationSource;
import io.specto.hoverfly.junit.dsl.HoverflyDsl;
import io.specto.hoverfly.junit.dsl.HttpBodyConverter;
import io.specto.hoverfly.junit.dsl.ResponseCreators;
import io.specto.hoverfly.junit.dsl.StubServiceBuilder;
import io.specto.hoverfly.junit5.HoverflyExtension;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.MediaType;

import static io.specto.hoverfly.junit.core.SimulationSource.dsl;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(HoverflyExtension.class)
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = {FiscalDocumentApplication.class},
    properties = {
        "sync.transactions.initial-delay-ms=1000"
    }
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class SendDocumentEnsListenerTest extends BaseIntegrationTest {
    @SpyBean
    private SendDocumentEnsListener sendDocumentEnsListener;

    @Autowired
    private EnsService ensService;

    @Value(value = "${topics.fiscal_documents}")
    private String topic;

    @MockBean
    private DateTimeService dateTimeService;

    private Long createTimestamp = 1747401177L;

    private Long updateTimestamp = 1747401180L;

    @Test
    public void testSendingEnsEventSuccess(
        Hoverfly hoverfly
    ) throws IOException, InterruptedException {
        hoverfly.simulate(getEnsLambdaSimulation(true));

        Mockito.when(
            dateTimeService.getCurrentDatetime()
        ).thenReturn(
            LocalDateTime.ofEpochSecond(
                createTimestamp,
                0,
                ZoneId.systemDefault().getRules().getOffset(LocalDateTime.now())
            ),
            LocalDateTime.ofEpochSecond(
                updateTimestamp,
                0,
                ZoneId.systemDefault().getRules().getOffset(LocalDateTime.now())
            )
        );
        Mockito.when(providerInterface.evaluatePropertyFlag(2L, FeatureFlags.ENS_ENABLED)).thenReturn(true, true);

        var messages = getEvents("/integration/document-ens-events/create-invoice-event/values.json");
        addMessagesAndWait(messages, false);
    }

    @Test
    public void testSendingEnsEventFail(
        Hoverfly hoverfly
    ) throws IOException, InterruptedException {
        hoverfly.simulate(getEnsLambdaSimulation(false));

        Mockito.when(providerInterface.evaluatePropertyFlag(2L, FeatureFlags.ENS_ENABLED)).thenReturn(true);
        Mockito.when(
            dateTimeService.getCurrentDatetime()
        ).thenReturn(
            LocalDateTime.ofEpochSecond(
                createTimestamp, 0, ZoneId.systemDefault().getRules().getOffset(LocalDateTime.now())
            )
        );

        var messages = getEvents("/integration/document-ens-events/create-invoice-event/values.json");
        addMessagesAndWait(messages, true);
    }

    private SimulationSource getEnsLambdaSimulation(boolean returnSuccess) throws IOException {
        var serviceUrl = new URL(ensService.getEnsUrl());

        var expectedBody = new ArrayList<HashMap<String, Object>>();
        expectedBody.add(getEvent("create", DocumentKind.INVOICE, 1L, createTimestamp));
        expectedBody.add(getEvent("update", DocumentKind.INVOICE, 2L, updateTimestamp));


        var requestMatcherBuilder = HoverflyDsl.service(serviceUrl.getProtocol() + "://" + serviceUrl.getHost())
            .post("/events").body(HttpBodyConverter.json(expectedBody));

        StubServiceBuilder stubServiceBuilder;

        if (returnSuccess) {
            stubServiceBuilder = requestMatcherBuilder.willReturn(
                ResponseCreators.success(
                    "",
                    MediaType.APPLICATION_JSON_VALUE
                )
            );
        } else {
            stubServiceBuilder = requestMatcherBuilder.willReturn(
                ResponseCreators.notFound()
            );
        }

        return dsl(stubServiceBuilder);
    }

    private HashMap<String, Object> getEvent(
        String eventType,
        DocumentKind documentKind,
        Long documentId,
        Long timestamp
    ) {
        var data = new HashMap<String, String>();
        var keys = new ArrayList<String>();
        keys.add("property/2");
        data.put("propertyId", "2");
        data.put("documentKind", documentKind.name());
        data.put("id", documentId.toString());
        data.put("event",  eventType);

        var params = new HashMap<String, Object>();
        params.put("keys", keys);

        var eventName = "fiscal_document/" + eventType;
        params.put("event", eventName);
        params.put("timestamp", timestamp);
        params.put("data", data);

        return params;
    }

    private void addMessagesAndWait(
        List<FiscalDocumentValue> messages,
        boolean shouldThrowException
    ) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(messages.size());
        AtomicBoolean exceptionIsThrown = new AtomicBoolean(false);
        AtomicReference<String> exceptionMessage = new AtomicReference<>();

        Mockito.doAnswer(invocation -> {
            try {
                var result = invocation.callRealMethod();
                List<ConsumerRecord<GenericRecord, GenericRecord>> receivedMessages = invocation.getArgument(0);
                receivedMessages.forEach(message -> latch.countDown());
                return result;
            } catch (Exception ex) {
                exceptionIsThrown.set(true);
                exceptionMessage.set(ex.getMessage());
                latch.countDown();
                throw ex;
            }
        }).when(sendDocumentEnsListener).listenMessages(any(), any());

        try (var producer = getFiscalDocumentProducer()) {
            messages.forEach(
                value -> {
                    var key = new FiscalDocumentKey(value.getPropertyId());
                    producer.send(new ProducerRecord<>(topic, key, value));
                }
            );
        }
        assertTrue(latch.await(10, TimeUnit.SECONDS));

        if (shouldThrowException) {
            assertTrue(exceptionIsThrown.get(), "Failed to assert that exception was thrown");
        } else {
            assertFalse(exceptionIsThrown.get(), "Exception was thrown: " + exceptionMessage.get());
        }
    }

    protected List<FiscalDocumentValue> getEvents(String path) throws IOException {
        return ResourceReaderUtil.getObjectFromJsonResource(path, new TypeReference<>() {
        });
    }
}
