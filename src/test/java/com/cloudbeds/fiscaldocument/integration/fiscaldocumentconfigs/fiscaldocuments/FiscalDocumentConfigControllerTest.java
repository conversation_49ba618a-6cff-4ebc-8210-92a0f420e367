package com.cloudbeds.fiscaldocument.integration.fiscaldocumentconfigs.fiscaldocuments;

import com.cloudbeds.fiscaldocument.FiscalDocumentApplication;
import com.cloudbeds.fiscaldocument.controller.model.ConfigsResponse;
import com.cloudbeds.fiscaldocument.controller.model.ConfigsUpdateRequest;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.grpc.organization.PropertyServiceClient;
import com.cloudbeds.fiscaldocument.integration.BaseIntegrationTest;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentRecipientService;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpStatus;

import static org.mockito.Mockito.doReturn;


@Slf4j
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = {FiscalDocumentApplication.class}
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FiscalDocumentConfigControllerTest extends BaseIntegrationTest {

    @MockBean
    private FiscalDocumentRecipientService fiscalDocumentRecipientService;
    @MockBean
    private PropertyServiceClient propertyServiceClient;

    @BeforeEach
    public void setUp() {
        setupProperty(CB_PROPERTY_ID, false);

        Mockito.reset(propertyServiceClient);
        doReturn(List.of(DEFAULT_PROPERTY))
            .when(propertyServiceClient)
            .listProperties(List.of(CB_PROPERTY_ID));

        doReturn(List.of(DEFAULT_PROPERTY.toBuilder().setId(2L).build()))
            .when(propertyServiceClient)
            .listProperties(List.of(2L));

        doReturn(List.of(SPAIN_PROPERTY))
            .when(propertyServiceClient)
            .listProperties(List.of(CB_SPAIN_PROPERTY_ID));
    }

    @Test
    void fiscalDocumentConfigTest() {

        var response = getRequest(
            "/configs",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<List<ConfigsResponse>>() {},
            Map.of()
        );

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(3, response.getBody().size());

        var configsByKind = response.getBody().stream()
            .collect(Collectors.toMap(
                ConfigsResponse::getDocumentKind,
                config -> config
            ));

        var invoiceConfig = configsByKind.get(FiscalDocumentKind.INVOICE);
        Assertions.assertEquals(CB_PROPERTY_ID.toString(), invoiceConfig.getPropertyId());
        Assertions.assertEquals(FiscalDocumentKind.INVOICE, invoiceConfig.getDocumentKind());
        Assertions.assertEquals(false, invoiceConfig.isShowDetailedTaxFee());
        Assertions.assertEquals(false, invoiceConfig.isChargeBreakdown());
        Assertions.assertEquals(false, invoiceConfig.isUseGuestLang());
        Assertions.assertEquals(0, invoiceConfig.getDueDays());
        Assertions.assertNull(invoiceConfig.getLang());
        Assertions.assertNull(invoiceConfig.getPrefix());
        Assertions.assertNull(invoiceConfig.getSuffix());
        Assertions.assertNull(invoiceConfig.getLegalCompanyName());
        Assertions.assertEquals(Map.of(), invoiceConfig.getTitle());
        Assertions.assertEquals(false, invoiceConfig.isShowLegalCompanyName());
        Assertions.assertEquals(false, invoiceConfig.isIncludeRoomNumber());
        Assertions.assertEquals(false, invoiceConfig.isUseDocumentNumber());
        Assertions.assertEquals(false, invoiceConfig.isIsCompact());
        Assertions.assertNull(invoiceConfig.getTaxId1());
        Assertions.assertNull(invoiceConfig.getTaxId2());
        Assertions.assertNull(invoiceConfig.getCpf());
        Assertions.assertEquals(Map.of(), invoiceConfig.getCustomText());

        var creditNoteConfig = configsByKind.get(FiscalDocumentKind.CREDIT_NOTE);
        Assertions.assertEquals(CB_PROPERTY_ID.toString(), creditNoteConfig.getPropertyId());
        Assertions.assertEquals(FiscalDocumentKind.CREDIT_NOTE, creditNoteConfig.getDocumentKind());
        Assertions.assertEquals(false, creditNoteConfig.isShowDetailedTaxFee());
        Assertions.assertEquals(false, creditNoteConfig.isChargeBreakdown());
        Assertions.assertEquals(false, creditNoteConfig.isUseGuestLang());
        Assertions.assertEquals(0, creditNoteConfig.getDueDays());
        Assertions.assertNull(creditNoteConfig.getLang());
        Assertions.assertNull(creditNoteConfig.getPrefix());
        Assertions.assertNull(creditNoteConfig.getSuffix());
        Assertions.assertNull(creditNoteConfig.getLegalCompanyName());
        Assertions.assertEquals(Map.of(), creditNoteConfig.getTitle());
        Assertions.assertEquals(false, creditNoteConfig.isShowLegalCompanyName());
        Assertions.assertEquals(false, creditNoteConfig.isIncludeRoomNumber());
        Assertions.assertEquals(false, creditNoteConfig.isUseDocumentNumber());
        Assertions.assertEquals(false, creditNoteConfig.isIsCompact());
        Assertions.assertNull(creditNoteConfig.getTaxId1());
        Assertions.assertNull(creditNoteConfig.getTaxId2());
        Assertions.assertNull(creditNoteConfig.getCpf());
        Assertions.assertEquals(Map.of(), creditNoteConfig.getCustomText());

        var receiptConfig = configsByKind.get(FiscalDocumentKind.RECEIPT);
        Assertions.assertEquals(FiscalDocumentKind.RECEIPT, receiptConfig.getDocumentKind());
        Assertions.assertEquals(false, receiptConfig.isShowDetailedTaxFee());
        Assertions.assertEquals(false, receiptConfig.isChargeBreakdown());
        Assertions.assertEquals(false, receiptConfig.isUseGuestLang());
        Assertions.assertEquals(0, receiptConfig.getDueDays());
        Assertions.assertNull(receiptConfig.getLang());
        Assertions.assertNull(receiptConfig.getPrefix());
        Assertions.assertNull(receiptConfig.getSuffix());
        Assertions.assertNull(receiptConfig.getLegalCompanyName());
        Assertions.assertEquals(Map.of(), receiptConfig.getTitle());
        Assertions.assertEquals(false, receiptConfig.isShowLegalCompanyName());
        Assertions.assertEquals(false, receiptConfig.isIncludeRoomNumber());
        Assertions.assertEquals(false, receiptConfig.isUseDocumentNumber());
        Assertions.assertEquals(true, receiptConfig.isIsCompact()); // default for newly created configs
        Assertions.assertNull(receiptConfig.getTaxId1());
        Assertions.assertNull(receiptConfig.getTaxId2());
        Assertions.assertNull(receiptConfig.getCpf());
        Assertions.assertEquals(Map.of(), receiptConfig.getCustomText());

        var updateRequest = new ConfigsUpdateRequest();
        updateRequest.setCpf("123.456.789-00");
        updateRequest.setShowDetailedTaxFee(true);
        updateRequest.setChargeBreakdown(true);
        updateRequest.setUseGuestLang(true);
        updateRequest.setDueDays(30);
        updateRequest.setLang("en");
        updateRequest.setPrefix("PRE");
        updateRequest.setSuffix("SUF");
        updateRequest.setLegalCompanyName("Company Name");
        updateRequest.setTitle(Map.of("en", "Mr."));
        updateRequest.setCustomText(Map.of("en", "Custom Text"));
        updateRequest.setShowLegalCompanyName(true);
        updateRequest.setIncludeRoomNumber(true);
        updateRequest.setUseDocumentNumber(true);
        updateRequest.setIsCompact(true);
        updateRequest.setTaxId1("1234567890");
        updateRequest.setTaxId2("0987654321");

        putRequest(
            "/configs/INVOICE",
            CB_PROPERTY_ID,
            updateRequest,
            ConfigsResponse.class
        );

        var updatedConfigs = getRequest(
            "/configs",
            CB_PROPERTY_ID,
            new ParameterizedTypeReference<List<ConfigsResponse>>() {},
            Map.of()
        );

        var updatedConfigsMap = updatedConfigs.getBody().stream()
            .collect(Collectors.toMap(
                ConfigsResponse::getDocumentKind,
                config -> config
            ));

        ConfigsResponse invoiceResponse = updatedConfigsMap.get(FiscalDocumentKind.INVOICE);
        Assertions.assertEquals(
            CB_PROPERTY_ID.toString(),
            invoiceResponse.getPropertyId());
        Assertions.assertEquals(
            FiscalDocumentKind.INVOICE,
            invoiceResponse.getDocumentKind()
        );

        Assertions.assertEquals(true, invoiceResponse.isShowDetailedTaxFee());
        Assertions.assertEquals(true, invoiceResponse.isChargeBreakdown());
        Assertions.assertEquals(true, invoiceResponse.isUseGuestLang());
        Assertions.assertEquals(30, invoiceResponse.getDueDays());
        Assertions.assertEquals("en", invoiceResponse.getLang());
        Assertions.assertEquals("PRE", invoiceResponse.getPrefix());
        Assertions.assertEquals("SUF", invoiceResponse.getSuffix());
        Assertions.assertEquals("Company Name", invoiceResponse.getLegalCompanyName());
        Assertions.assertEquals(Map.of("en", "Mr."), invoiceResponse.getTitle());
        Assertions.assertEquals(true, invoiceResponse.isShowLegalCompanyName());
        Assertions.assertEquals(true, invoiceResponse.isIncludeRoomNumber());
        Assertions.assertEquals(true, invoiceResponse.isUseDocumentNumber());
        Assertions.assertEquals(true, invoiceResponse.isIsCompact());
        Assertions.assertEquals("1234567890", invoiceResponse.getTaxId1());
        Assertions.assertEquals("0987654321", invoiceResponse.getTaxId2());
        Assertions.assertEquals("123.456.789-00", invoiceResponse.getCpf());
        Assertions.assertEquals(
            Map.of("en", "Custom Text"),
            invoiceResponse.getCustomText()
        );
    }

    @Test
    void createMissingConfigsTest() {
        long propertyId = 2L;
        var response = getRequest(
            "/configs",
            propertyId,
            new ParameterizedTypeReference<List<ConfigsResponse>>() {},
            Map.of()
        );

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(3, response.getBody().size());
    }


    @Test
    void createSpainMissingConfigsTest() {
        var response = getRequest(
            "/configs",
            CB_SPAIN_PROPERTY_ID,
            new ParameterizedTypeReference<List<ConfigsResponse>>() {},
            Map.of()
        );

        Assertions.assertEquals(HttpStatus.OK, response.getStatusCode());
        Assertions.assertEquals(3, response.getBody().size());
        Assertions.assertFalse(
            response.getBody().stream()
                .anyMatch(cr -> cr.getDocumentKind() == FiscalDocumentKind.CREDIT_NOTE)
        );
    }
}