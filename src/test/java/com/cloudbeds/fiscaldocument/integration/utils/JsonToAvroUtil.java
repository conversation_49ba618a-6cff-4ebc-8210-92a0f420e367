package com.cloudbeds.fiscaldocument.integration.utils;

import com.fasterxml.jackson.databind.JsonNode;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;

public class JsonToAvroUtil {

    /**
     * Converts a JsonNode to an Avro GenericRecord.
     *
     * @param jsonNode the JsonNode to convert
     * @param schema   the Avro schema
     * @return the converted GenericRecord
     */
    public static GenericData.Record jsonToAvro(JsonNode jsonNode, Schema schema) {
        var record = new GenericData.Record(schema);

        var it = jsonNode.fieldNames();
        while (it.hasNext()) {
            var name = it.next();
            var field = jsonNode.get(name);
            record.put(name, getFieldValue(field, schema.getField(name).schema()));
        }

        return record;
    }

    private static Object getFieldValue(JsonNode field, Schema schema) {
        return switch (schema.getType()) {
            case BOOLEAN -> field.asBoolean();
            case NULL -> null;
            case UNION -> {
                if (field.isNull()) {
                    yield null;
                } else {
                    var notNullType = schema.getTypes().stream()
                        .filter(s -> !s.getType().equals(Schema.Type.NULL)).findFirst().get();
                    yield getFieldValue(field, notNullType);
                }
            }
            case ENUM, STRING -> field.textValue();
            case INT -> field.asInt();
            case LONG -> field.asLong();
            case FLOAT, DOUBLE -> field.asDouble();
            case RECORD, ARRAY, MAP, FIXED, BYTES -> {
                throw new RuntimeException("Not implemented field");
            }
        };
    }
}
