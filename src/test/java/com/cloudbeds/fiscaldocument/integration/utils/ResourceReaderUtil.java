package com.cloudbeds.fiscaldocument.integration.utils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.IOException;
import java.nio.charset.Charset;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

public class ResourceReaderUtil {
    private static final DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    public static final ObjectMapper jsonMapper = Jackson2ObjectMapperBuilder.json().build()
            .registerModule(new JavaTimeModule())
            .configure(SerializationFeature.WRITE_DATE_TIMESTAMPS_AS_NANOSECONDS, false)
            .configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .configure(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS, false)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .setDateFormat(dateFormat)
            .enable(SerializationFeature.INDENT_OUTPUT);

    public static <T> T getObjectFromJsonString(String jsonString, Class<T> clazz) throws IOException {
        return jsonMapper.readValue(jsonString, clazz);
    }

    public static <T> T getObjectFromJsonResource(String path, Class<T> clazz) throws IOException {
        return jsonMapper.readValue(getResourceAsString(path), clazz);
    }

    public static <T> List<T> getObjectFromJsonResource(String path, TypeReference<List<T>> clazz) throws IOException {
        return jsonMapper.readValue(getResourceAsString(path), clazz);
    }

    public static String getResourceAsString(String path) throws IOException {
        return IOUtils.toString(new ClassPathResource(path).getInputStream(), Charset.defaultCharset());
    }

    public static JsonNode getResourceAsJsonNode(String path) throws IOException {
        return jsonMapper.readTree(getResourceAsString(path));
    }

    public static GenericRecord jsonToGenericRecord(String jsonString, String schemaPath) throws IOException {
        return jsonToGenericRecord(jsonMapper.readTree(jsonString), schemaPath);
    }

    /**
     * Convert JSON Node to generic record.
     *
     * @param jsonNode JSON Node
     * @param schemaPath Schema path
     * @return GenericRecord
     * @throws IOException IOException
     */
    public static GenericRecord jsonToGenericRecord(JsonNode jsonNode, String schemaPath) throws IOException {
        var schema = new Schema.Parser().parse(getResourceAsString(schemaPath));
        var record = new GenericData.Record(schema);

        var it = jsonNode.fieldNames();
        while (it.hasNext()) {
            var name = it.next();
            var field = jsonNode.get(name);
            System.out.println("NAME : " + name);
            record.put(name, getFieldValue(field, schema.getField(name).schema()));
        }

        return record;
    }

    private static Object getFieldValue(JsonNode field, Schema schema) {
        return switch (schema.getType()) {
            case BOOLEAN -> field.asBoolean();
            case NULL -> null;
            case UNION -> {
                if (field.isNull()) {
                    yield null;
                } else {
                    var notNullType =
                            schema.getTypes().stream().filter(s -> !s.getType().equals(Schema.Type.NULL)).findFirst()
                                    .get();
                    yield getFieldValue(field, notNullType);
                }
            }
            case ENUM -> field.textValue();
            case STRING -> field.textValue();
            case INT -> field.asInt();
            case LONG -> field.asLong();
            case FLOAT, DOUBLE -> field.asDouble();
            case RECORD, ARRAY, MAP, FIXED, BYTES -> {
                throw new RuntimeException("Not implemented field");
            }
        };
    }


    //Used for ignoring static fields in Avro GenericRecords
    abstract static class IgnoreSchemaProperty {
        @JsonIgnore
        abstract void getClassSchema();

        @JsonIgnore
        abstract void getSpecificData();

        @JsonIgnore
        abstract void get();

        @JsonIgnore
        abstract void getSchema();
    }
}
