package com.cloudbeds.fiscaldocument.integration.utils;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.KafkaConsumer;

public class KafkaUtils {
    /**
     * Consume messages.
     *
     * @param consumer consumer
     * @param topic topic
     * @param expectedCount expectedCount Try to consume at least amount of messages
     * @param timeoutMillis timeoutMillis Maximum time to spend on trying
     *                      to fetch at least `expectedCount` amount of messages
     * @param <K> Key
     * @param <V> Value
     * @return List of records
     */
    public static <K, V> List<ConsumerRecord<K, V>> consumeMessages(KafkaConsumer<K, V> consumer, String topic,
                                                                    int expectedCount, int timeoutMillis) {
        final int pollTimeout = 50;
        var records = new ArrayList<ConsumerRecord<K, V>>();
        int timePassed = 0;

        do {
            var messages = consumer.poll(Duration.of(pollTimeout, ChronoUnit.MILLIS));
            messages.records(topic).forEach(records::add);
            timePassed += pollTimeout;
        } while (records.size() < expectedCount && timePassed < timeoutMillis);

        return records;
    }

    /**
     * Consume messages.
     *
     * @param consumer consumer
     * @param topic topic
     * @param idExtractFunction idExtractFunction
     * @param expectedIds expectedIds
     * @param timeoutMillis timeoutMillis
     * @param <K> Key
     * @param <V> Value
     * @return List of records
     */
    public static <K, V> List<ConsumerRecord<K, V>> consumeMessages(
            KafkaConsumer<K, V> consumer,
            String topic,
            Function<ConsumerRecord<K, V>, String> idExtractFunction,
            List<String> expectedIds,
            int timeoutMillis
    ) {
        final int pollTimeout = 50;
        var records = new ArrayList<ConsumerRecord<K, V>>();
        int timePassed = 0;

        Set<String> processedIds = new HashSet<>();

        do {
            var messages = consumer.poll(Duration.of(pollTimeout, ChronoUnit.MILLIS));
            messages.records(topic).forEach(message -> {
                records.add(message);
                var id = idExtractFunction.apply(message);
                if (expectedIds.contains(id)) {
                    processedIds.add(id);
                }
            });
            timePassed += pollTimeout;
        } while (processedIds.size() < expectedIds.size() && timePassed < timeoutMillis);

        return records;
    }

    /**
     * Subscribe consumers in parallel and consume all messages in the topics.
     *
     * @param consumerTopicPairs Pairs of kafka consumer and topic name
     */
    public static void subscribeConsumers(List<Pair<KafkaConsumer<?, ?>, String>> consumerTopicPairs) {
        subscribeConsumers(consumerTopicPairs, 100);
    }

    /**
     * Subscribe consumers in parallel and consume all messages in the topics.
     *
     * @param consumerTopicPairs Pairs of kafka consumer and topic name
     * @param timeoutMillis maximum time to poll for messages
     */
    public static void subscribeConsumers(
        List<Pair<KafkaConsumer<?, ?>, String>> consumerTopicPairs,
        int timeoutMillis
    ) {
        var threads = new ArrayList<Thread>();

        for (var consumerTopicPair : consumerTopicPairs) {
            var consumer = consumerTopicPair.getKey();
            var topic = consumerTopicPair.getValue();
            var t = new Thread(() -> {
                consumer.subscribe(List.of(topic));
                consumeMessages(consumer, topic, Integer.MAX_VALUE, timeoutMillis);
            });
            t.start();
            threads.add(t);
        }

        threads.forEach(t -> {
            try {
                t.join();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
    }
}
