package com.cloudbeds.fiscaldocument.integration.utils;

import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentLinkedDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentTransaction;
import com.cloudbeds.fiscaldocument.entity.Recipient.Address;
import com.cloudbeds.fiscaldocument.entity.Recipient.RecipientType;
import com.cloudbeds.fiscaldocument.entity.Recipient.TaxInfo;
import com.cloudbeds.fiscaldocument.enums.CreationMethod;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import com.cloudbeds.fiscaldocument.enums.Origin;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentRepository;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import static com.cloudbeds.fiscaldocument.integration.BaseIntegrationTest.CB_PROPERTY_ID;

@Component
@RequiredArgsConstructor
public class FiscalDocumentPreconditions {
    private final FiscalDocumentRepository repository;


    /**
     * Creates and persists a {@link FiscalDocument} with status {@code PAID},
     * kind {@code INVOICE}, and the provided property ID.
     *
     */
    public void createInvoices() {
        FiscalDocument failedInvoice = new FiscalDocument();
        failedInvoice.setId(1L);
        failedInvoice.setNumber("1-2025");
        failedInvoice.setPropertyId(CB_PROPERTY_ID);
        failedInvoice.setStatus(DocumentStatus.FAILED);
        failedInvoice.setKind(DocumentKind.INVOICE);
        failedInvoice.setFilePath("/reservation/invoice.pdf");
        failedInvoice.setOrigin(Origin.FISCAL_DOCUMENT);
        failedInvoice.setSourceId(1L);
        failedInvoice.setInvoiceDate(LocalDate.of(2025, 1, 1));
        failedInvoice.setSourceKind(SourceKind.RESERVATION);
        failedInvoice.setUserId(1L);
        failedInvoice.setCreatedAt(LocalDateTime.now());
        failedInvoice.setVersion(0);

        repository.save(failedInvoice);

        var transaction1 = new FiscalDocumentTransaction();
        transaction1.setTransactionId(1L);
        transaction1.setFiscalDocumentId(2L);

        FiscalDocument successfulInvoice = new FiscalDocument();
        transaction1.setFiscalDocument(successfulInvoice);
        successfulInvoice.setId(2L);
        successfulInvoice.setNumber("2-2025");
        successfulInvoice.setPropertyId(CB_PROPERTY_ID);
        successfulInvoice.setStatus(DocumentStatus.COMPLETED);
        successfulInvoice.setKind(DocumentKind.INVOICE);
        successfulInvoice.setFilePath("/reservation/invoice.pdf");
        successfulInvoice.setOrigin(Origin.FISCAL_DOCUMENT);
        successfulInvoice.setSourceId(1L);
        successfulInvoice.setSourceKind(SourceKind.GROUP_PROFILE);
        successfulInvoice.setTransactionList(List.of(transaction1));
        successfulInvoice.setUserId(1L);
        successfulInvoice.setInvoiceDate(LocalDate.of(2025, 1, 1));
        successfulInvoice.setCreatedAt(LocalDateTime.now());
        successfulInvoice.setVersion(1);
        successfulInvoice.setAmount(1000L);
        successfulInvoice.setBalance(200L);
        successfulInvoice.setCurrency("USD");
        var recipient = new com.cloudbeds.fiscaldocument.entity.Recipient();
        recipient.setId("12345");
        recipient.setType(RecipientType.PERSON);
        recipient.setFirstName("John");
        recipient.setLastName("Doe");
        recipient.setEmail("<EMAIL>");
        var recipientAddress = new Address();
        recipientAddress.setAddress1("123 Main St");
        recipientAddress.setAddress2("");
        recipientAddress.setCity("New York");
        recipientAddress.setCountry("USA");
        recipientAddress.setZipCode("121212");
        recipient.setAddress(recipientAddress);
        var tax = new TaxInfo();
        tax.setId("tax-1");
        recipient.setTax(tax);

        var fiscalDocumentRecipient = new FiscalDocumentRecipient();
        fiscalDocumentRecipient.setFiscalDocumentId(successfulInvoice.getId());
        fiscalDocumentRecipient.setRecipientId(12345L);
        fiscalDocumentRecipient.setRecipient(recipient);
        successfulInvoice.setRecipients(List.of(
            fiscalDocumentRecipient
        ));

        transaction1.setFiscalDocument(successfulInvoice);
        repository.save(successfulInvoice);

        FiscalDocument migratedInvoice = new FiscalDocument();
        migratedInvoice.setId(3L);
        migratedInvoice.setNumber("3-2025");
        migratedInvoice.setPropertyId(CB_PROPERTY_ID);
        migratedInvoice.setStatus(DocumentStatus.COMPLETED);
        migratedInvoice.setKind(DocumentKind.INVOICE);
        migratedInvoice.setExternalId("1234");
        migratedInvoice.setOrigin(Origin.MFD);
        migratedInvoice.setUrl("https://cloudbeds.amazonaws.com/invalidBucket/6721fa4708f38/invoice.pdf");
        migratedInvoice.setSourceId(1L);
        migratedInvoice.setSourceKind(SourceKind.GROUP_PROFILE);
        migratedInvoice.setUserId(1L);
        migratedInvoice.setInvoiceDate(LocalDate.of(2025, 1, 1));
        migratedInvoice.setCreatedAt(LocalDateTime.now());
        migratedInvoice.setVersion(2);

        repository.save(migratedInvoice);

        FiscalDocument noFileDocument = new FiscalDocument();
        noFileDocument.setId(4L);
        noFileDocument.setNumber("4-2025");
        noFileDocument.setPropertyId(CB_PROPERTY_ID);
        noFileDocument.setStatus(DocumentStatus.COMPLETED);
        noFileDocument.setKind(DocumentKind.INVOICE);
        noFileDocument.setSourceId(1L);
        noFileDocument.setOrigin(Origin.FISCAL_DOCUMENT);
        noFileDocument.setSourceKind(SourceKind.GROUP_PROFILE);
        noFileDocument.setUserId(1L);
        noFileDocument.setInvoiceDate(LocalDate.of(2025, 1, 1));
        noFileDocument.setCreatedAt(LocalDateTime.now());
        noFileDocument.setVersion(3);

        repository.save(noFileDocument);

        // Valid MFD Invoice
        FiscalDocument validMfdInvoice = new FiscalDocument();
        validMfdInvoice.setId(5L);
        validMfdInvoice.setNumber("5-2025");
        validMfdInvoice.setPropertyId(CB_PROPERTY_ID);
        validMfdInvoice.setStatus(DocumentStatus.COMPLETED);
        validMfdInvoice.setKind(DocumentKind.INVOICE);
        validMfdInvoice.setExternalId("12345");
        validMfdInvoice.setUrl("https://cloudbeds.amazonaws.com/01tt/6721fa4708f38/invoice.pdf");
        validMfdInvoice.setOrigin(Origin.MFD);
        validMfdInvoice.setSourceId(1L);
        validMfdInvoice.setSourceKind(SourceKind.GROUP_PROFILE);
        validMfdInvoice.setUserId(1L);
        validMfdInvoice.setCreatedAt(LocalDateTime.now());
        validMfdInvoice.setVersion(4);
        repository.save(validMfdInvoice);

        // Credit Note
        FiscalDocument creditNote = new FiscalDocument();
        creditNote.setId(6L);
        creditNote.setNumber("CREDIT-NOTE-001");
        creditNote.setPropertyId(CB_PROPERTY_ID);
        creditNote.setStatus(DocumentStatus.OPEN);
        creditNote.setKind(DocumentKind.CREDIT_NOTE);
        creditNote.setExternalId("12345");
        creditNote.setUrl("https://cloudbeds.amazonaws.com/01tt/6721fa4708f38/creditnote.pdf");
        creditNote.setOrigin(Origin.MFD);
        creditNote.setSourceId(1L);
        creditNote.setSourceKind(SourceKind.GROUP_PROFILE);
        creditNote.setUserId(1L);
        creditNote.setCreatedAt(LocalDateTime.now());

        // Linked Documents
        var linkedDocument = new FiscalDocumentLinkedDocument();
        linkedDocument.setFiscalDocumentId(6L);
        linkedDocument.setLinkedDocumentId(5L);
        linkedDocument.setFiscalDocument(creditNote);
        creditNote.getLinkedDocuments().add(linkedDocument);
        repository.save(creditNote);

        var transaction2 = new FiscalDocumentTransaction();
        transaction2.setTransactionId(1L);
        transaction2.setFiscalDocumentId(2L);

        FiscalDocument openInvoice = new FiscalDocument();
        transaction2.setFiscalDocument(openInvoice);
        openInvoice.setId(7L);
        openInvoice.setNumber("7-2025");
        openInvoice.setPropertyId(CB_PROPERTY_ID);
        openInvoice.setStatus(DocumentStatus.OPEN);
        openInvoice.setKind(DocumentKind.INVOICE);
        openInvoice.setFilePath("/reservation/invoice.pdf");
        openInvoice.setOrigin(Origin.FISCAL_DOCUMENT);
        openInvoice.setSourceId(1L);
        openInvoice.setSourceKind(SourceKind.GROUP_PROFILE);
        openInvoice.setTransactionList(List.of(transaction2));
        openInvoice.setUserId(1L);
        openInvoice.setInvoiceDate(LocalDate.of(2025, 1, 1));
        openInvoice.setCreatedAt(LocalDateTime.now());
        openInvoice.setAmount(1000L);
        openInvoice.setBalance(200L);
        openInvoice.setCurrency("USD");
        var recipient2 = new com.cloudbeds.fiscaldocument.entity.Recipient();
        recipient2.setId("123456");
        recipient2.setType(RecipientType.PERSON);
        recipient2.setFirstName("John");
        recipient2.setLastName("Doe");
        recipient2.setEmail("<EMAIL>");
        var recipient2Address = new Address();
        recipient2Address.setAddress1("123 Main St");
        recipient2Address.setAddress2("");
        recipient2Address.setCity("New York");
        recipient2Address.setCountry("USA");
        recipient2Address.setZipCode("121212");
        recipient2.setAddress(recipient2Address);
        var tax2 = new TaxInfo();
        tax2.setId("tax-2");
        recipient2.setTax(tax2);

        var fiscalDocument2Recipient = new FiscalDocumentRecipient();
        fiscalDocument2Recipient.setFiscalDocumentId(openInvoice.getId());
        fiscalDocument2Recipient.setRecipientId(12345L);
        fiscalDocument2Recipient.setRecipient(recipient2);
        openInvoice.setRecipients(List.of(
            fiscalDocument2Recipient
        ));

        transaction2.setFiscalDocument(openInvoice);
        repository.save(openInvoice);

        var transaction3 = new FiscalDocumentTransaction();
        transaction3.setTransactionId(1000L);
        transaction3.setFiscalDocumentId(8000L);

        FiscalDocument integrationInProgressInvoice = new FiscalDocument();
        integrationInProgressInvoice.setId(8000L);
        integrationInProgressInvoice.setNumber("2-2025");
        integrationInProgressInvoice.setPropertyId(CB_PROPERTY_ID);
        integrationInProgressInvoice.setStatus(DocumentStatus.PENDING_INTEGRATION);
        integrationInProgressInvoice.setKind(DocumentKind.INVOICE);
        integrationInProgressInvoice.setFilePath("/reservation/invoice.pdf");
        integrationInProgressInvoice.setOrigin(Origin.FISCAL_DOCUMENT);
        integrationInProgressInvoice.setSourceId(1L);
        integrationInProgressInvoice.setSourceKind(SourceKind.RESERVATION);
        integrationInProgressInvoice.setTransactionList(List.of(transaction3));
        integrationInProgressInvoice.setUserId(1L);
        integrationInProgressInvoice.setInvoiceDate(LocalDate.of(2025, 1, 1));
        integrationInProgressInvoice.setCreatedAt(LocalDateTime.now());
        integrationInProgressInvoice.setVersion(1);
        integrationInProgressInvoice.setAmount(1000L);
        integrationInProgressInvoice.setBalance(200L);
        integrationInProgressInvoice.setCurrency("USD");

        var fiscalDocumentIntegrationRecipient = new FiscalDocumentRecipient();
        fiscalDocumentIntegrationRecipient.setFiscalDocumentId(integrationInProgressInvoice.getId());
        fiscalDocumentIntegrationRecipient.setRecipientId(12345L);
        fiscalDocumentIntegrationRecipient.setRecipient(recipient);
        integrationInProgressInvoice.setRecipients(List.of(
            fiscalDocumentIntegrationRecipient
        ));
        transaction3.setFiscalDocument(integrationInProgressInvoice);
        repository.save(integrationInProgressInvoice);
    }

    /**
     * Creates a voided adjustment credit note sequence along with an initial invoice.
     */
    public void createVoidedAdjustmentCreditNote() {
        FiscalDocument invoice = new FiscalDocument();
        invoice.setId(1L);
        invoice.setNumber("2-2025");
        invoice.setPropertyId(CB_PROPERTY_ID);
        invoice.setStatus(DocumentStatus.VOIDED);
        invoice.setKind(DocumentKind.INVOICE);
        invoice.setFilePath("/reservation/invoice.pdf");
        invoice.setOrigin(Origin.FISCAL_DOCUMENT);
        invoice.setSourceId(1L);
        invoice.setSourceKind(SourceKind.GROUP_PROFILE);
        invoice.setUserId(1L);
        invoice.setInvoiceDate(LocalDate.of(2025, 1, 1));
        invoice.setCreatedAt(LocalDateTime.now());
        invoice.setAmount(1000L);
        invoice.setBalance(200L);
        invoice.setCurrency("USD");

        var transaction1 = new FiscalDocumentTransaction();
        transaction1.setTransactionId(1L);
        transaction1.setFiscalDocumentId(1L);
        transaction1.setFiscalDocument(invoice);
        invoice.setTransactionList(List.of(transaction1));

        repository.save(invoice);

        // Adj Credit Note
        FiscalDocument adjCreditNote = new FiscalDocument();
        adjCreditNote.setId(2L);
        adjCreditNote.setNumber("CREDIT-NOTE-002");
        adjCreditNote.setPropertyId(CB_PROPERTY_ID);
        adjCreditNote.setStatus(DocumentStatus.VOIDED);
        adjCreditNote.setKind(DocumentKind.CREDIT_NOTE);
        adjCreditNote.setMethod(CreationMethod.ADJUSTMENT);
        adjCreditNote.setUrl("https://cloudbeds.amazonaws.com/01tt/6721fa4708f38/creditnote.pdf");
        adjCreditNote.setOrigin(Origin.FISCAL_DOCUMENT);
        adjCreditNote.setSourceId(1L);
        adjCreditNote.setSourceKind(SourceKind.GROUP_PROFILE);
        adjCreditNote.setUserId(1L);
        adjCreditNote.setCreatedAt(LocalDateTime.now());

        // Linked Documents
        var adjCreditNoteLinkedDocument = new FiscalDocumentLinkedDocument();
        adjCreditNoteLinkedDocument.setFiscalDocumentId(2L);
        adjCreditNoteLinkedDocument.setLinkedDocumentId(1L);
        adjCreditNoteLinkedDocument.setFiscalDocument(adjCreditNote);
        adjCreditNote.getLinkedDocuments().add(adjCreditNoteLinkedDocument);

        var adjTransaction = new FiscalDocumentTransaction();
        adjTransaction.setTransactionId(2L);
        adjTransaction.setFiscalDocumentId(2L);
        adjTransaction.setFiscalDocument(adjCreditNote);
        adjCreditNote.setTransactionList(List.of(adjTransaction));

        repository.save(adjCreditNote);


        // Adj Credit Note
        FiscalDocument failedAdjCreditNote = new FiscalDocument();
        failedAdjCreditNote.setId(20L);
        failedAdjCreditNote.setNumber("CREDIT-NOTE-FAILED-002");
        failedAdjCreditNote.setPropertyId(CB_PROPERTY_ID);
        failedAdjCreditNote.setStatus(DocumentStatus.FAILED);
        failedAdjCreditNote.setKind(DocumentKind.CREDIT_NOTE);
        failedAdjCreditNote.setMethod(CreationMethod.ADJUSTMENT);
        failedAdjCreditNote.setUrl("https://cloudbeds.amazonaws.com/01tt/6721fa4708f38/creditnote.pdf");
        failedAdjCreditNote.setOrigin(Origin.FISCAL_DOCUMENT);
        failedAdjCreditNote.setSourceId(1L);
        failedAdjCreditNote.setSourceKind(SourceKind.GROUP_PROFILE);
        failedAdjCreditNote.setUserId(1L);
        failedAdjCreditNote.setCreatedAt(LocalDateTime.now());

        // Linked Documents
        var failedAdjCreditNoteLinkedDocument = new FiscalDocumentLinkedDocument();
        failedAdjCreditNoteLinkedDocument.setFiscalDocumentId(20L);
        failedAdjCreditNoteLinkedDocument.setLinkedDocumentId(1L);
        failedAdjCreditNoteLinkedDocument.setFiscalDocument(failedAdjCreditNote);
        failedAdjCreditNote.getLinkedDocuments().add(failedAdjCreditNoteLinkedDocument);

        var failedAdjTransaction = new FiscalDocumentTransaction();
        failedAdjTransaction.setTransactionId(20L);
        failedAdjTransaction.setFiscalDocumentId(2L);
        failedAdjTransaction.setFiscalDocument(failedAdjCreditNote);
        failedAdjCreditNote.setTransactionList(List.of(failedAdjTransaction));

        repository.save(failedAdjCreditNote);

        // Void Credit Note
        FiscalDocument voidCreditNote = new FiscalDocument();
        voidCreditNote.setId(3L);
        voidCreditNote.setNumber("CREDIT-NOTE-003");
        voidCreditNote.setPropertyId(CB_PROPERTY_ID);
        voidCreditNote.setStatus(DocumentStatus.OPEN);
        voidCreditNote.setKind(DocumentKind.CREDIT_NOTE);
        voidCreditNote.setMethod(CreationMethod.VOID);
        voidCreditNote.setUrl("https://cloudbeds.amazonaws.com/01tt/6721fa4708f38/creditnote.pdf");
        voidCreditNote.setOrigin(Origin.FISCAL_DOCUMENT);
        voidCreditNote.setSourceId(1L);
        voidCreditNote.setSourceKind(SourceKind.GROUP_PROFILE);
        voidCreditNote.setUserId(1L);
        voidCreditNote.setCreatedAt(LocalDateTime.now());

        // Linked Documents
        var voidCreditNoteLinkedDocument = new FiscalDocumentLinkedDocument();
        voidCreditNoteLinkedDocument.setFiscalDocumentId(3L);
        voidCreditNoteLinkedDocument.setLinkedDocumentId(1L);
        voidCreditNoteLinkedDocument.setFiscalDocument(voidCreditNote);
        voidCreditNote.getLinkedDocuments().add(voidCreditNoteLinkedDocument);

        repository.save(voidCreditNote);
    }
}
