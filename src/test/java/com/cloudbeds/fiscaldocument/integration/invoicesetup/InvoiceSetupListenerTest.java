package com.cloudbeds.fiscaldocument.integration.invoicesetup;

import com.cloudbeds.fiscaldocument.FiscalDocumentApplication;
import com.cloudbeds.fiscaldocument.entity.DocumentConfig;
import com.cloudbeds.fiscaldocument.entity.DocumentContent;
import com.cloudbeds.fiscaldocument.integration.BaseIntegrationTest;
import com.cloudbeds.fiscaldocument.integration.utils.JsonToAvroUtil;
import com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil;
import com.cloudbeds.fiscaldocument.listeners.InvoiceSetupListener;
import com.cloudbeds.fiscaldocument.repositories.DocumentConfigRepository;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentRecipientService;
import com.fasterxml.jackson.core.type.TypeReference;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import static com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil.getResourceAsJsonNode;
import static com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil.getResourceAsString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doAnswer;

@Slf4j
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = {FiscalDocumentApplication.class}
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InvoiceSetupListenerTest extends BaseIntegrationTest {
    @Value(value = "${topics.invoice_setup}")
    private String topic;

    @Autowired
    private DocumentConfigRepository documentConfigRepository;

    @SpyBean
    private InvoiceSetupListener invoiceSetupListener;

    @MockBean
    private FiscalDocumentRecipientService fiscalDocumentRecipientService;

    @ParameterizedTest(name = "{0}")
    @MethodSource("processMessagesSource")
    public void processMessages(
        String name,
        String keyPath,
        List<String> valuePaths,
        List<DocumentConfig> expectedConfigs
    ) throws Exception {
        AtomicLong index = new AtomicLong(0L);
        doAnswer(invocation -> index.incrementAndGet()).when(idPool).nextId();
        doAnswer(invocation -> index.incrementAndGet()).when(idPool).nextIdWithRetry(anyInt(), anyLong());

        var key = JsonToAvroUtil.jsonToAvro(
            getResourceAsJsonNode(keyPath),
            new Schema.Parser().parse(getResourceAsString("avro/InvoiceSetupKey.avsc"))
        );

        var messages = valuePaths.stream().map(valuePath -> {
            try {
                var message = JsonToAvroUtil.jsonToAvro(
                    getResourceAsJsonNode(valuePath),
                    new Schema.Parser().parse(getResourceAsString("avro/InvoiceSetupValue.avsc"))
                );
                return new ProducerRecord<>(topic, key, message);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }).toList();

        produceMessagesAndWait(messages);

        var configs = documentConfigRepository.findAll();

        var expectedConfigsMap = expectedConfigs.stream().collect(Collectors.toMap(
            DocumentConfig::getDocumentKind,
            Function.identity()
        ));
        assertEquals(configs.size(), expectedConfigs.size());

        for (var config : configs) {
            var expectedConfig = expectedConfigsMap.get(config.getDocumentKind());
            assertEquals(expectedConfig.getPropertyId(), config.getPropertyId(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getDocumentKind(), config.getDocumentKind(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.isShowDetailedTaxFee(), config.isShowDetailedTaxFee(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.isChargeBreakdown(), config.isChargeBreakdown(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.isUseGuestLang(), config.isUseGuestLang(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getDueDays(), config.getDueDays(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getLang(), config.getLang(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getPrefix(), config.getPrefix(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getSuffix(), config.getSuffix(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getLegalCompanyName(), config.getLegalCompanyName(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getImgUrl(), config.getImgUrl(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getTitle(), config.getTitle(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.isShowLegalCompanyName(), config.isShowLegalCompanyName(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.isIncludeRoomNumber(), config.isIncludeRoomNumber(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.isUseDocumentNumber(), config.isUseDocumentNumber(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.isCompact(), config.isCompact(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getTaxId1(), config.getTaxId1(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getTaxId2(), config.getTaxId2(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getCpf(), config.getCpf(),
                "Error in config " + config.getDocumentKind());
            assertEquals(expectedConfig.getCustomText(), config.getCustomText(),
                "Error in config " + config.getDocumentKind());

            var expectedContents = expectedConfig.getDocumentContents().stream()
                .collect(Collectors.toMap(
                    DocumentContent::getSourceKind,
                    Function.identity()
                ));

            var activeConfigs = config.getDocumentContents().stream().filter(DocumentContent::isActive).toList();

            assertEquals(expectedConfigs.size(), activeConfigs.size());

            for (var content : activeConfigs) {
                var expectedContent = expectedContents.get(content.getSourceKind());
                assertEquals(expectedContent.getDocumentKind(), content.getDocumentKind(),
                    "Error in sourceKind " + content.getSourceKind());
                assertEquals(expectedContent.getSourceKind(), content.getSourceKind(),
                    "Error in sourceKind " + content.getSourceKind());
                assertEquals(expectedContent.isActive(), content.isActive(),
                    "Error in sourceKind " + content.getSourceKind());
                assertEquals(expectedContent.getDocumentSequence().getId(), content.getDocumentSequence().getId(),
                    "Error in sourceKind " + content.getSourceKind());
                assertEquals(expectedContent.getDocumentSequence().getNumber(),
                    content.getDocumentSequence().getNumber(),
                    "Error in sourceKind " + content.getSourceKind());
                assertEquals(expectedContent.getPrefix(), content.getPrefix(),
                    "Error in sourceKind " + content.getSourceKind());
                assertEquals(expectedContent.getSuffix(), content.getSuffix(),
                             "Error in sourceKind " + content.getSourceKind());
                assertEquals(expectedContent.getExternalId(), content.getExternalId(),
                             "Error in sourceKind " + content.getSourceKind());
            }
        }
    }

    public Stream<Arguments> processMessagesSource() throws IOException {
        return Stream.of(
            Arguments.of(
                "create config",
                "/integration/invoice-setup/create/key.json",
                List.of("/integration/invoice-setup/create/value.json"),
                getDocumentConfigs("integration/invoice-setup/create/expected.json")
            ),
            Arguments.of(
                "Update from compact to not compact",
                "/integration/invoice-setup/update-compact/key.json",
                List.of(
                    "/integration/invoice-setup/update-compact/value1.json",
                    "/integration/invoice-setup/update-compact/value2.json"
                ),
                getDocumentConfigs("integration/invoice-setup/update-compact/expected.json")
            ),
            Arguments.of(
                "Update prefix and suffix",
                "/integration/invoice-setup/update-prefix/key.json",
                List.of(
                    "/integration/invoice-setup/update-prefix/value1.json",
                    "/integration/invoice-setup/update-prefix/value2.json"
                ),
                getDocumentConfigs("integration/invoice-setup/update-prefix/expected.json")
            ),
            Arguments.of(
                "Update from same config for credit note and invoice to different",
                "/integration/invoice-setup/update-credit-note-config/key.json",
                List.of(
                    "/integration/invoice-setup/update-credit-note-config/value1.json",
                    "/integration/invoice-setup/update-credit-note-config/value2.json"
                ),
                getDocumentConfigs("integration/invoice-setup/update-credit-note-config/expected.json")
            ),
            Arguments.of(
                "Config number is null",
                "/integration/invoice-setup/invoice-config-null/key.json",
                List.of(
                    "/integration/invoice-setup/invoice-config-null/value.json"
                ),
                getDocumentConfigs("integration/invoice-setup/invoice-config-null/expected.json")
            )
        );
    }

    private List<DocumentConfig> getDocumentConfigs(String path) throws IOException {
        return ResourceReaderUtil.getObjectFromJsonResource(path, new TypeReference<>() {});
    }

    private void produceMessagesAndWait(
        List<ProducerRecord<GenericData.Record, GenericData.Record>> messages
    ) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(messages.size());
        Mockito.doAnswer(invocation -> {
            Object result = invocation.callRealMethod();
            latch.countDown();
            return result;
        }).when(invoiceSetupListener).listenMessages(any(), any());


        try (var producer = getGenericRecordProducer()) {
            messages.forEach(
                producer::send
            );
        }
        assertTrue(latch.await(10, TimeUnit.SECONDS));
    }
}
