package com.cloudbeds.fiscaldocument.integration.config;

import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@TestConfiguration
public class TestTopicConfigurations {

    @Bean
    public NewTopic invoiceSetupTopic(@Value("${topics.invoice_setup}") String topic) {
        return new NewTopic(topic, 1, (short) 1);
    }

    @Bean
    public NewTopic invoiceTopic(@Value("${topics.invoices}") String topic) {
        return new NewTopic(topic, 1, (short) 1);
    }

    @Bean
    public NewTopic fiscalDocumentEventsTopic(@Value("${topics.fiscal_document_events}") String topic) {
        return new NewTopic(topic, 1, (short) 1);
    }

    @Bean
    public NewTopic fiscalDocumentsTopic(@Value("${topics.fiscal_documents}") String topic) {
        return new NewTopic(topic, 1, (short) 1);
    }
}
