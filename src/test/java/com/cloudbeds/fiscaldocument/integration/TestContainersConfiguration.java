package com.cloudbeds.fiscaldocument.integration;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import com.cloudbeds.fiscaldocument.integration.config.TestTopicConfigurations;
import com.redis.testcontainers.RedisContainer;
import io.confluent.kafka.serializers.AbstractKafkaSchemaSerDeConfig;
import io.confluent.kafka.serializers.KafkaAvroDeserializerConfig;
import java.util.Properties;
import java.util.UUID;
import org.apache.avro.generic.GenericData;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.utility.DockerImageName;


@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@Import(TestTopicConfigurations.class)
@SuppressWarnings({"PMD.TestClassWithoutTestCases", "PMD.UseUtilityClass"})
public class TestContainersConfiguration {
    
    @Container
    private static final PostgreSQLContainer<?> POSTGRES_CONTAINER =
            new PostgreSQLContainer<>(DockerImageName.parse("postgres:16.2"))
                    .withDatabaseName("fiscaldocument")
                    .withUsername("main")
                    .withPassword("pass")
                    .withUrlParam("stringtype", "unspecified")
                    .withInitScript("setup.sql");


    @Container
    private static final KafkaContainer KAFKA_CONTAINER =
        new KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.8.0"))
            .withEmbeddedZookeeper()
            .withEnv("KAFKA_LISTENERS", "PLAINTEXT://0.0.0.0:9093 ,BROKER://0.0.0.0:9092")
            .withEnv("KAFKA_LISTENER_SECURITY_PROTOCOL_MAP", "BROKER:PLAINTEXT,PLAINTEXT:PLAINTEXT")
            .withEnv("KAFKA_INTER_BROKER_LISTENER_NAME", "BROKER")
            .withEnv("KAFKA_BROKER_ID", "1")
            .withEnv("KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR", "1")
            .withEnv("KAFKA_OFFSETS_TOPIC_NUM_PARTITIONS", "1")
            .withEnv("KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR", "1")
            .withEnv("KAFKA_TRANSACTION_STATE_LOG_MIN_ISR", "1")
            .withEnv("KAFKA_LOG_FLUSH_INTERVAL_MESSAGES", Long.MAX_VALUE + "")
            .withEnv("KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS", "0");

    @Container
    private static final RedisContainer REDIS_CONTAINER =
        new RedisContainer(DockerImageName.parse("valkey/valkey:8.0-alpine"));

    static {
        KAFKA_CONTAINER.start();
        POSTGRES_CONTAINER.start();
        REDIS_CONTAINER.start();
    }

    @DynamicPropertySource
    static void properties(DynamicPropertyRegistry properties) {
        properties.add("spring.kafka.bootstrap-servers", KAFKA_CONTAINER::getBootstrapServers);
        properties.add("spring.kafka.consumer.bootstrap-servers", KAFKA_CONTAINER::getBootstrapServers);
        properties.add("spring.kafka.producer.bootstrap-servers", KAFKA_CONTAINER::getBootstrapServers);

        properties.add("spring.datasource.url", POSTGRES_CONTAINER::getJdbcUrl);
        properties.add("spring.datasource.username", POSTGRES_CONTAINER::getUsername);
        properties.add("spring.datasource.password", POSTGRES_CONTAINER::getPassword);
        properties.add("spring.datasource.hikari.schema", () -> "public");

        properties.add("spring.liquibase.url", POSTGRES_CONTAINER::getJdbcUrl);
        properties.add("spring.liquibase.user", POSTGRES_CONTAINER::getUsername);
        properties.add("spring.liquibase.password", POSTGRES_CONTAINER::getPassword);
        properties.add("spring.liquibase.change-log", () -> "classpath:/db/changelog/databaseChangeLog.xml");
        properties.add("spring.liquibase.driver-class-name", () -> "org.postgresql.Driver");
        properties.add("spring.liquibase.default-schema", () -> "public");

        // Redis properties
        properties.add("spring.data.redis.host", REDIS_CONTAINER::getHost);
        properties.add("spring.data.redis.port", REDIS_CONTAINER::getRedisPort);
    }

    protected static KafkaProducer<GenericData.Record, GenericData.Record> getGenericRecordProducer() {
        Properties props = basicKafkaProducerProps(
            io.confluent.kafka.serializers.KafkaAvroSerializer.class,
            io.confluent.kafka.serializers.KafkaAvroSerializer.class
        );

        return new KafkaProducer<>(props);
    }

    protected static KafkaConsumer<FiscalDocumentEventKey, FiscalDocumentEventValue> getFiscalDocumentEventsConsumer() {
        Properties props = basicKafkaConsumerProps(
            io.confluent.kafka.serializers.KafkaAvroDeserializer.class,
            io.confluent.kafka.serializers.KafkaAvroDeserializer.class,
            "fiscal-document-events" + UUID.randomUUID()
        );

        return new KafkaConsumer<>(props);
    }

    protected static KafkaConsumer<FiscalDocumentKey, FiscalDocumentValue> getFiscalDocumentsConsumer() {
        Properties props = basicKafkaConsumerProps(
            io.confluent.kafka.serializers.KafkaAvroDeserializer.class,
            io.confluent.kafka.serializers.KafkaAvroDeserializer.class,
            "fiscal-document-events" + UUID.randomUUID()
        );

        return new KafkaConsumer<>(props);
    }

    protected static KafkaProducer<
        FiscalDocumentEventKey,
        FiscalDocumentEventValue
        > getFiscalDocumentEventProducer() {
        Properties props = basicKafkaProducerProps(
            io.confluent.kafka.serializers.KafkaAvroSerializer.class,
            io.confluent.kafka.serializers.KafkaAvroSerializer.class
        );
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 64000);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 20);

        return new KafkaProducer<>(props);
    }

    protected static KafkaProducer<
        FiscalDocumentKey,
        FiscalDocumentValue
        > getFiscalDocumentProducer() {
        Properties props = basicKafkaProducerProps(
            io.confluent.kafka.serializers.KafkaAvroSerializer.class,
            io.confluent.kafka.serializers.KafkaAvroSerializer.class
        );
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 64000);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 20);

        return new KafkaProducer<>(props);
    }


    private static <K, V> Properties basicKafkaProducerProps(
        Class<K> keySerializerClass,
        Class<V> valueSerializerClass
    ) {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_CONTAINER.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, keySerializerClass);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, valueSerializerClass);
        props.put(AbstractKafkaSchemaSerDeConfig.SCHEMA_REGISTRY_URL_CONFIG, "mock://testUrl");
        props.put("specific.avro.writer", false);
        props.put("acks", "all");

        return props;
    }

    private static <K, V> Properties basicKafkaConsumerProps(
        Class<K> keyDeserializerClass,
        Class<V> valueDeserializerClass,
        String consumerName
    ) {
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_CONTAINER.getBootstrapServers());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, keyDeserializerClass);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, valueDeserializerClass);
        props.put(AbstractKafkaSchemaSerDeConfig.SCHEMA_REGISTRY_URL_CONFIG, "mock://testUrl");
        props.put(ConsumerConfig.CLIENT_ID_CONFIG, "kafkatest-consumer-" + consumerName);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "kafkatest-consumer-" + consumerName);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, true);

        return props;
    }

}

