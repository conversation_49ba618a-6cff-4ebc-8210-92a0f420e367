package com.cloudbeds.fiscaldocument.integration;

import com.cloudbeds.booking.v1.Booking;
import com.cloudbeds.booking.v1.BookingGuest;
import com.cloudbeds.booking.v1.BookingRoom;
import com.cloudbeds.booking.v1.BookingWithRooms;
import com.cloudbeds.distributedid.pool.IdPool;
import com.cloudbeds.fiscaldocument.entity.DocumentConfig;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.grpc.marketplace.MarketplaceServiceClient;
import com.cloudbeds.fiscaldocument.integration.config.TestTopicConfigurations;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.entity.CurrencySettingsDetails;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.entity.CurrencySettingsDetails.CurrencyDetails;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.entity.CurrencySettingsDetails.CurrencyFormat;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.entity.CurrencySettingsDetails.RatesDetails;
import com.cloudbeds.fiscaldocument.services.DocumentConfigService;
import com.cloudbeds.fiscaldocument.support.features.FeatureFlags;
import com.cloudbeds.fiscaldocument.support.features.ProviderInterface;
import com.cloudbeds.group.v1.GroupProfile;
import com.cloudbeds.organization.v1.Address;
import com.cloudbeds.organization.v1.Property;
import com.cloudbeds.organization.v1.PropertyProfile;
import com.cloudbeds.organization.v1.Status;
import com.google.type.Date;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Table;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.context.annotation.Import;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import static java.util.Objects.nonNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;

@Import(TestTopicConfigurations.class)
@DirtiesContext
public abstract class BaseIntegrationTest extends TestContainersConfiguration {
    public static final Long CB_PROPERTY_ID = 1L;
    protected static final String CB_ACCESS_TOKEN_VALUE = "1.eyJ2ZXIiOjEsImp0aSI6IkFULlczQ2taWGhXV2t4SGpQYVp5WDJRM1NvRG"
        + "ZpZjZvVVFkZFZqeXU3dFl4dEkub2FyeTI1ZGxicGR2ZEo1ZTcxZDYiLCJpc3MiOiJodHRwczovL2lkcC5jbG91ZGJlZHMtZGV2LmNvbS9vYX"
        + "V0aDIvYXVzaDFzMTNuU3p6ZVBNOWQxZDYiLCJhdWQiOiJodHRwczovL2hvdGVscy5jbG91ZGJlZHMtZGV2LmNvbS9hcGkiLCJzdWIiOiJlbm"
        + "dpbmVlcmluZ0BjbG91ZGJlZHMuY29tIiwiaWF0IjoxNzA1MDYxNTQ2LCJleHAiOjE3MDUwNjUxNDYsImNpZCI6IjBvYWhwZmZlZWJ0dWV3dm"
        + "RhMWQ2IiwidWlkIjoiMDB1Mmc5c2kwalRuU3FKSEQxZDciLCJzY3AiOlsicHJvZmlsZSIsIm9wZW5pZCIsIm9mZmxpbmVfYWNjZXNzIiwiZ3"
        + "JvdXBzIiwiZW1haWwiXSwiYXV0aF90aW1lIjoxNzA1MDYxNTI3LCJmaXJzdE5hbWUiOiJTdXBlciIsImFzc29jaWF0aW9uSWRzIjpbXSwicH"
        + "JvcGVydHlJZHMiOltdLCJpc1N1cGVyQWRtaW4iOnRydWUsIm1mZFVzZXJJZCI6MSwidHlwZSI6Im5vbmUifQ.1";
    protected static final String BASE_URL = "/fiscal-document/v1";
    protected static final String PROPERTY_ID_HEADER = "X-PROPERTY-ID";
    protected static final String AUTHORIZATION_HEADER = "Authorization";
    protected static final String AUTHORIZATION_VALUE = "Bearer " + CB_ACCESS_TOKEN_VALUE;

    @Autowired
    private TestRestTemplate restTemplate;

    @MockBean
    protected IdPool idPool;

    @MockBean
    protected ProviderInterface providerInterface;

    @MockBean
    protected MarketplaceServiceClient marketplaceServiceClient;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private DataSource dataSource;

    @LocalServerPort
    private int port;

    @Autowired
    protected DocumentConfigService documentConfigService;

    private AtomicLong idCounter = new AtomicLong(0L);

    private List<String> tableNames;

    protected void setupProperty(Long propertyId, boolean isCompact) {
        var config = documentConfigService.createConfig(
            propertyId, DocumentKind.INVOICE, isCompact, null
        );
        var newPropertyDocumentConfig = new DocumentConfig();
        newPropertyDocumentConfig.setPropertyId(propertyId);
        newPropertyDocumentConfig.setDocumentKind(DocumentKind.CREDIT_NOTE);
        newPropertyDocumentConfig.setCompact(isCompact);

        var defaultSequence = config.getSequence(SourceKind.RESERVATION);
        newPropertyDocumentConfig.addContent(
            documentConfigService.createDefaultContentsCreditNote(
                propertyId,
                null,
                null,
                null,
                defaultSequence,
                SourceKind.RESERVATION
            )
        );
        newPropertyDocumentConfig.addContent(
            documentConfigService.createDefaultContentsCreditNote(
                propertyId,
                null,
                null,
                null,
                defaultSequence,
                SourceKind.GROUP_PROFILE
            )
        );
        newPropertyDocumentConfig.addContent(
            documentConfigService.createDefaultContentsRectifyInvoice(
                propertyId,
                null,
                null,
                null,
                defaultSequence,
                SourceKind.RESERVATION
            )
        );
        newPropertyDocumentConfig.addContent(
            documentConfigService.createDefaultContentsRectifyInvoice(
                propertyId,
                null,
                null,
                null,
                defaultSequence,
                SourceKind.GROUP_PROFILE
            )
        );

        documentConfigService.saveAll(List.of(newPropertyDocumentConfig));
    }

    protected static Property DEFAULT_PROPERTY = Property.newBuilder()
        .setId(CB_PROPERTY_ID)
        .setLang("en")
        .setCurrency("USD")
        .setDateFormat("Y-m-d")
        .setIsActive(true)
        .setTimezone("UTC")
        .setName("Hotel name")
        .setPropertyProfile(
            PropertyProfile.newBuilder()
                .setHotelName("Hotel name")
                .setHotelPhone("Hotel phone")
                .setHotelFax("Hotel fax")
                .setStatus(Status.STATUS_REGULAR)
                .setBusinessName("Business Name")
                .setHotelAddress(
                    Address.newBuilder()
                        .setAddress1("Hotel Address 1")
                        .setAddress2("Hotel Address 2")
                        .setCity("Hotel City")
                        .setState("Hotel State")
                        .setCountryCode("US")
                        .build())
                .setBusinessAddress(
                    Address.newBuilder()
                        .setAddress1("Address 1")
                        .setAddress2("Address 2")
                        .setCity("City")
                        .setState("State")
                        .setCountryCode("US")
                        .build())
                .build())
        .build();

    public static final Long CB_SPAIN_PROPERTY_ID = 333L;
    protected static Property SPAIN_PROPERTY = Property.newBuilder()
        .setId(CB_SPAIN_PROPERTY_ID)
        .setLang("es")
        .setCurrency("EUR")
        .setDateFormat("Y-m-d")
        .setIsActive(true)
        .setTimezone("UTC")
        .setName("Hotel name")
        .setPropertyProfile(
            PropertyProfile.newBuilder()
                .setHotelName("Hotel name")
                .setHotelPhone("Hotel phone")
                .setHotelFax("Hotel fax")
                .setStatus(Status.STATUS_REGULAR)
                .setBusinessName("Business Name")
                .setHotelAddress(
                    Address.newBuilder()
                        .setAddress1("Hotel Address 1")
                        .setAddress2("Hotel Address 2")
                        .setCity("Barcelona")
                        .setState("Catalonia")
                        .setCountryCode("ES")
                        .build())
                .setBusinessAddress(
                    Address.newBuilder()
                        .setAddress1("Address 1")
                        .setAddress2("Address 2")
                        .setCity("Barcelona")
                        .setState("Catalonia")
                        .setCountryCode("ES")
                        .build())
                .build())
        .build();

    protected static GroupProfile DEFAULT_GROUP_PROFILE = GroupProfile.newBuilder()
        .setId(1L)
        .setCode("GP")
        .setName("Group Profile")
        .buildPartial();

    protected static BookingWithRooms DEFAULT_RESERVATION = BookingWithRooms.newBuilder()
        .setBooking(
            Booking.newBuilder()
                .setId(1L)
                .setCustomerName("Customer Name")
                .setIdentifier("Reservation ID")
                .setCheckinDate(
                    Date.newBuilder()
                        .setYear(2025)
                        .setMonth(4)
                        .setDay(5)
                        .build()
                )
                .setCheckoutDate(
                    Date.newBuilder()
                        .setYear(2025)
                        .setMonth(4)
                        .setDay(15)
                        .build()
                )
                .buildPartial())
        .addAllRooms(
            List.of(
                BookingRoom.newBuilder()
                    .setId(321L)
                    .setRoomTypeName("DLX")
                    .buildPartial()
            )
        )
        .addAllGuests(
            List.of(
                BookingGuest.newBuilder()
                    .setIsMainGuest(true)
                    .setFirstName("First Name")
                    .setLastName("Last Name")
                    .setZip("Zip")
                    .setState("Idaho")
                    .setCompanyName("n/a")
                    .setAddress1("Address 1")
                    .setAddress2("Address 2")
                    .setCity("City")
                    .setCountry("Country")
                    .setEmail("<EMAIL>")
                    .setPhone("123-345")
                    .setCellPhone("0747-345-23")
                    .setGuestTaxIdNumber("1245")
                    .buildPartial()
            )
        )
        .buildPartial();

    protected static CurrencySettingsDetails DEFAULT_CURRENCY_SETTINGS_DETAILS = new CurrencySettingsDetails();

    static {
        DEFAULT_CURRENCY_SETTINGS_DETAILS.setAcceptable(List.of("USD"));
        DEFAULT_CURRENCY_SETTINGS_DETAILS.setDefaultCurrency("USD");
        CurrencyFormat currencyFormat = new CurrencyFormat();
        currencyFormat.setDecimal(",");
        currencyFormat.setThousand(".");
        DEFAULT_CURRENCY_SETTINGS_DETAILS.setFormat(currencyFormat);
        RatesDetails rates = new RatesDetails();
        CurrencyDetails currencyDetails = new CurrencyDetails();
        currencyDetails.setCurrency("USD");
        currencyDetails.setRate(1.0);
        rates.setFixed(List.of(currencyDetails));
        DEFAULT_CURRENCY_SETTINGS_DETAILS.setRates(rates);
    }

    @PostConstruct
    void afterPropertiesSet() {
        tableNames = entityManager.getMetamodel().getEntities().stream()
            .filter(entityType -> entityType.getJavaType().getAnnotation(Table.class) != null)
            .map(entityType -> entityType.getJavaType().getAnnotation(Table.class))
            .map(this::convertToTableName)
            .filter(name -> !name.equals("locked_tables"))
            .toList();
    }

    @BeforeEach
    protected void beforeEach() throws SQLException {
        var orderedTableNames = new ArrayList<>(tableNames);

        var connection = dataSource.getConnection();
        connection.setAutoCommit(false);

        for (var tableName : orderedTableNames) {
            boolean success = false;
            int maxRetry = 5;
            int attempts = 0;

            while (!success && attempts++ < maxRetry) {
                try (var statement = connection.createStatement()) {
                    statement.execute("TRUNCATE " + tableName + " CASCADE");
                    success = true;
                } catch (SQLException e) {
                    System.err.printf("Attempt %d to truncate %s failed: %s%n", attempts, tableName, e.getMessage());
                    connection.rollback();

                    if (attempts >= maxRetry) {
                        throw new RuntimeException("Failed to truncate table " + tableName + " after retries", e);
                    }

                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
        connection.commit();
        connection.close();

        idCounter.set(0);
        doAnswer(invocation -> idCounter.incrementAndGet()).when(idPool).nextId();

        try {
            doAnswer(invocation -> idCounter.incrementAndGet()).when(idPool).nextIdWithRetry(anyInt(), anyLong());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        Mockito.reset(providerInterface, marketplaceServiceClient);
        doReturn(true)
            .when(providerInterface)
            .evaluatePropertyFlag(any(), eq(FeatureFlags.FISCAL_DOCUMENT_SERVICE_ENABLED));
        doReturn(true)
            .when(marketplaceServiceClient)
            .getInvoiceIntegrationEnabled(any());
    }

    protected <T> ResponseEntity<T> getRequest(
        String resource,
        Long propertyId,
        ParameterizedTypeReference<T> responseType,
        Map<String, Object> params
    ) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(
            "http://localhost:" + port + getApiUrl(resource));

        if (params != null) {
            params.forEach(uriBuilder::queryParam);
        }

        String finalUrl = uriBuilder.toUriString();

        // Add headers
        HttpEntity<?> httpEntity = new HttpEntity<>(null, getHeaders(propertyId));

        // Use final URL
        return restTemplate.exchange(
            finalUrl,
            HttpMethod.GET,
            httpEntity,
            responseType
        );
    }

    protected <T> ResponseEntity<T> putRequest(
        String resource,
        Long propertyId,
        Object request,
        Class<T> responseType
    ) {
        var httpEntity = new HttpEntity<>(request, getHeaders(propertyId));
        return this.restTemplate.exchange(
            this.getApiUrl(resource),
            HttpMethod.PUT,
            httpEntity,
            responseType
        );
    }

    protected <T> ResponseEntity<T> patchRequest(
        String resource,
        Long propertyId,
        Object request,
        Class<T> responseType
    ) {
        var httpEntity = new HttpEntity<>(request, getHeaders(propertyId));
        return this.restTemplate.exchange(
            this.getApiUrl(resource),
            HttpMethod.PATCH,
            httpEntity,
            responseType
        );
    }

    protected <T> ResponseEntity<T> postRequest(
        String resource,
        Long propertyId,
        Object request,
        Class<T> responseType
    ) {
        var httpEntity = new HttpEntity<>(request, getHeaders(propertyId));
        return this.restTemplate.exchange(
            this.getApiUrl(resource),
            HttpMethod.POST,
            httpEntity,
            responseType
        );
    }

    protected <T> ResponseEntity<T> deleteRequest(
        String resource,
        Long propertyId,
        Object request,
        Class<T> responseType
    ) {
        var httpEntity = new HttpEntity<>(request, getHeaders(propertyId));
        return this.restTemplate.exchange(
            this.getApiUrl(resource),
            HttpMethod.DELETE,
            httpEntity,
            responseType
        );
    }

    private String getApiUrl(String resource) {
        return BASE_URL + (resource.charAt(0) == '/' ? resource : '/' + resource);
    }

    private HttpHeaders getHeaders(Long propertyId) {
        var headers = new HttpHeaders();
        if (propertyId != null) {
            headers.add(PROPERTY_ID_HEADER, String.valueOf(propertyId));
        }
        headers.add(AUTHORIZATION_HEADER, AUTHORIZATION_VALUE);
        headers.add(HttpHeaders.CONTENT_TYPE, "application/json");
        return headers;
    }

    protected String buildStringUri(String path, Collection<String> extraParamNames) {
        UriComponentsBuilder url = UriComponentsBuilder
                .newInstance()
                .path(path);

        if (nonNull(extraParamNames)) {
            extraParamNames.forEach(name -> url.queryParam(name, String.format("{%s}", name)));
        }
        return url.build().toUriString();
    }

    private String convertToTableName(Table table) {
        String schema = table.schema();
        String tableName = table.name();

        String convertedSchema = StringUtils.hasText(schema) ? schema.toLowerCase() + "." : "";

        return convertedSchema + tableName;
    }
}
