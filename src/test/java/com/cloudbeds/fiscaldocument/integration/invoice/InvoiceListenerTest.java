package com.cloudbeds.fiscaldocument.integration.invoice;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import com.cloudbeds.fiscaldocument.FiscalDocumentApplication;
import com.cloudbeds.fiscaldocument.grpc.accounting.AccountingService;
import com.cloudbeds.fiscaldocument.grpc.organization.PropertyServiceClient;
import com.cloudbeds.fiscaldocument.integration.BaseIntegrationTest;
import com.cloudbeds.fiscaldocument.integration.utils.JsonToAvroUtil;
import com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil;
import com.cloudbeds.fiscaldocument.listeners.InvoiceListener;
import com.cloudbeds.fiscaldocument.repositories.DocumentSequenceRepository;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentCustomRepository;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentTransactionRepository;
import com.cloudbeds.fiscaldocument.services.DocumentConfigService;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentRecipientService;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;

import static com.cloudbeds.fiscaldocument.integration.utils.KafkaUtils.consumeMessages;
import static com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil.getResourceAsJsonNode;
import static com.cloudbeds.fiscaldocument.integration.utils.ResourceReaderUtil.getResourceAsString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@Slf4j
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = {FiscalDocumentApplication.class}
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InvoiceListenerTest extends BaseIntegrationTest {
    @Value(value = "${topics.invoices}")
    private String topic;

    @Value(value = "${topics.fiscal_document_events}")
    private String eventsTopic;

    private static final KafkaConsumer<FiscalDocumentEventKey, FiscalDocumentEventValue> consumer =
        getFiscalDocumentEventsConsumer();

    @Autowired
    private FiscalDocumentCustomRepository fiscalDocumentRepository;

    @Autowired
    private DocumentSequenceRepository documentSequenceRepository;

    @Autowired
    private FiscalDocumentTransactionRepository fiscalDocumentTransactionRepository;

    @Autowired
    private DocumentConfigService documentConfigService;

    @SpyBean
    private InvoiceListener invoiceListener;

    @MockBean
    private AccountingService accountingService;

    @MockBean
    private PropertyServiceClient propertyServiceClient;

    @Autowired
    private FiscalDocumentRecipientService fiscalDocumentRecipientService;

    @BeforeEach
    void consumeMessagesBeforeTests() {
        consumer.subscribe(List.of(eventsTopic));
        consumeMessages(consumer, eventsTopic, 10, 500);

        Mockito.reset(propertyServiceClient);
        doReturn(DEFAULT_PROPERTY).when(propertyServiceClient).getProperty(any());
        doReturn(List.of(DEFAULT_PROPERTY)).when(propertyServiceClient).listProperties(any());
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("processMessagesSource")
    public void processMessages(
        String name,
        String keyPath,
        String valuePath,
        FiscalDocumentEventValue expectedValue
    ) throws Exception {
        var key = JsonToAvroUtil.jsonToAvro(
            getResourceAsJsonNode(keyPath),
            new Schema.Parser().parse(getResourceAsString("avro/InvoiceKey.avsc"))
        );

        var value = JsonToAvroUtil.jsonToAvro(
            getResourceAsJsonNode(valuePath),
            new Schema.Parser().parse(getResourceAsString("avro/InvoiceValue.avsc"))
        );

        produceMessagesAndWait(List.of(new ProducerRecord<>(topic, key, value)));

        consumer.subscribe(List.of(eventsTopic));
        var kafkaMessages = consumeMessages(consumer, eventsTopic, 1, 5000);

        assertEquals(1, kafkaMessages.size());

        assertEquals(expectedValue, kafkaMessages.get(0).value());
    }

    public Stream<Arguments> processMessagesSource() throws IOException {
        return Stream.of(
            Arguments.of(
                "create config",
                "/integration/invoice/create/key.json",
                "/integration/invoice/create/value.json",
                getFiscalDocumentEventValue("integration/invoice/create/expected.json"),
                2L
            ),
            Arguments.of(
                "Update invoice status",
                "/integration/invoice/update-status/key.json",
                "/integration/invoice/update-status/value.json",
                getFiscalDocumentEventValue("integration/invoice/update-status/expected.json"),
                1L
            ),
            Arguments.of(
                "Group Profile Invoice",
                "/integration/invoice/create-group-profile/key.json",
                "/integration/invoice/create-group-profile/value.json",
                getFiscalDocumentEventValue("integration/invoice/create-group-profile/expected.json"),
                1L
            )
        );
    }

    private FiscalDocumentEventValue getFiscalDocumentEventValue(String path) throws IOException {
        return ResourceReaderUtil.getObjectFromJsonResource(path, FiscalDocumentEventValue.class);
    }

    private void produceMessagesAndWait(
        List<ProducerRecord<GenericData.Record, GenericData.Record>> messages
    ) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(messages.size());
        Mockito.doAnswer(invocation -> {
            Object result = invocation.callRealMethod();
            List<ConsumerRecord<GenericRecord, GenericRecord>> receivedMessages = invocation.getArgument(0);
            receivedMessages.forEach(message -> latch.countDown());
            return result;
        }).when(invoiceListener).listenMessages(any(), any());


        try (var producer = getGenericRecordProducer()) {
            messages.forEach(
                producer::send
            );
        }
        assertTrue(latch.await(10, TimeUnit.SECONDS));
    }
}
