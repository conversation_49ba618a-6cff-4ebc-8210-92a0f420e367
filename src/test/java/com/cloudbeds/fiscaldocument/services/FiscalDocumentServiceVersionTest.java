package com.cloudbeds.fiscaldocument.services;

import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import com.cloudbeds.fiscaldocument.enums.Origin;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.outbox.FiscalDocumentSyncService;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentRepository;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FiscalDocumentServiceVersionTest {

    @Mock
    private FiscalDocumentRepository fiscalDocumentRepository;

    @Mock
    private FiscalDocumentSyncService fiscalDocumentSyncService;

    @InjectMocks
    private FiscalDocumentService fiscalDocumentService;

    private FiscalDocument createTestDocument(Long id, Integer version) {
        FiscalDocument document = new FiscalDocument();
        document.setId(id);
        document.setNumber("TEST-2025");
        document.setPropertyId(123L);
        document.setStatus(DocumentStatus.COMPLETED);
        document.setKind(DocumentKind.INVOICE);
        document.setOrigin(Origin.FISCAL_DOCUMENT);
        document.setSourceId(1L);
        document.setSourceKind(SourceKind.RESERVATION);
        document.setUserId(1L);
        document.setInvoiceDate(LocalDate.now());
        document.setCreatedAt(LocalDateTime.now());
        if (version != null) {
            document.setVersion(version);
        }
        return document;
    }

    @BeforeEach
    void setUp() {
        // Mock the sync service to do nothing
        doNothing().when(fiscalDocumentSyncService).save(any());
    }

    @Test
    void testNewDocumentVersionHandling() {
        FiscalDocument newDocument = createTestDocument(null, null);

        when(fiscalDocumentRepository.save(any())).thenAnswer(invocation -> {
            FiscalDocument doc = invocation.getArgument(0);
            return doc;
        });

        FiscalDocument result = fiscalDocumentService.save(newDocument);

        assertEquals(0, result.getVersion());
    }

    @Test
    void testUpdateDocumentFromVersion0() {
        FiscalDocument updateDocument = createTestDocument(1L, 0);

        when(fiscalDocumentRepository.save(any())).thenAnswer(invocation -> {
            FiscalDocument doc = invocation.getArgument(0);
            if (doc.getId() != null) {
                doc.setVersion(doc.getVersion() + 1);
            }
            return doc;
        });

        FiscalDocument result = fiscalDocumentService.save(updateDocument);

        assertEquals(1, result.getVersion());
    }

    @Test
    void testUpdateDocumentFromVersion1() {
        FiscalDocument updateDocument = createTestDocument(1L, 1);

        when(fiscalDocumentRepository.save(any())).thenAnswer(invocation -> {
            FiscalDocument doc = invocation.getArgument(0);
            if (doc.getId() != null) {
                doc.setVersion(doc.getVersion() + 1);
            }
            return doc;
        });

        FiscalDocument result = fiscalDocumentService.save(updateDocument);

        assertEquals(2, result.getVersion());
    }

    @Test
    void testUpdateDocumentFromVersion4() {
        FiscalDocument updateDocument = createTestDocument(1L, 4);

        when(fiscalDocumentRepository.save(any())).thenAnswer(invocation -> {
            FiscalDocument doc = invocation.getArgument(0);
            if (doc.getId() != null) {
                doc.setVersion(doc.getVersion() + 1);
            }
            return doc;
        });

        FiscalDocument result = fiscalDocumentService.save(updateDocument);

        assertEquals(5, result.getVersion());
    }

    @Test
    void testUpdateDocumentFromHighVersion() {
        FiscalDocument updateDocument = createTestDocument(1L, 10);

        when(fiscalDocumentRepository.save(any())).thenAnswer(invocation -> {
            FiscalDocument doc = invocation.getArgument(0);
            if (doc.getId() != null) {
                doc.setVersion(doc.getVersion() + 1);
            }
            return doc;
        });

        FiscalDocument result = fiscalDocumentService.save(updateDocument);

        assertEquals(11, result.getVersion());
    }


}
