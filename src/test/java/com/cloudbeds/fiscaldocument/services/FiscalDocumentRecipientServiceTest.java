package com.cloudbeds.fiscaldocument.services;

import com.cloudbeds.fiscaldocument.controller.model.RecipientRequest;
import com.cloudbeds.fiscaldocument.converters.RecipientConverter;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.entity.Recipient;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.grpc.guest.GuestServiceClient;
import com.cloudbeds.fiscaldocument.grpc.guestrequirement.GuestRequirementServiceClient;
import com.cloudbeds.fiscaldocument.grpc.mfd.GroupProfileServiceClient;
import com.cloudbeds.fiscaldocument.grpc.organization.PropertyServiceClient;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentRecipientRepository;
import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import com.cloudbeds.group.v1.ContactDetail;
import com.cloudbeds.group.v1.GroupProfileContact;
import com.cloudbeds.guest.v1.Address;
import com.cloudbeds.guest.v1.ContactDetails;
import com.cloudbeds.guest.v1.Person;
import com.cloudbeds.guest.v1.PersonVersion;
import com.cloudbeds.guest.v1.TaxInfo;
import com.cloudbeds.guestrequirements.v1.GuestRequirements;
import com.cloudbeds.organization.v1.Organization;
import com.cloudbeds.organization.v1.Property;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@SpringBootTest(
    classes = { FiscalDocumentRecipientService.class, RecipientConverter.class }
)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class FiscalDocumentRecipientServiceTest {

    @Autowired
    private FiscalDocumentRecipientService fiscalDocumentRecipientService;

    @MockBean
    private PropertyServiceClient propertyServiceClient;

    @MockBean
    private GroupProfileServiceClient groupProfileServiceClient;

    @MockBean
    private GuestRequirementServiceClient guestRequirementServiceClient;

    @MockBean
    private GuestServiceClient guestServiceClient;

    @MockBean
    private FiscalDocumentRecipientRepository fiscalDocumentRecipientRepository;

    @ParameterizedTest
    @MethodSource("cdcTestData")
    void testGetRecipientsFromCdc(
        Map<String, Object> reservation,
        Map<String, Object> groupProfile,
        int expectedCount
    ) {
        List<Recipient> recipients = fiscalDocumentRecipientService.getRecipientsFromCdc(reservation, groupProfile);
        assertEquals(expectedCount, recipients.size());
    }

    private Stream<Arguments> cdcTestData() {
        return Stream.of(
            Arguments.of(
                Map.of(
                    "customer_id", "1", "first_name", "John", "last_name", "Doe"
                ), null, 1
            ),
            Arguments.of(
                null, Map.of("id", 2, "name", "Acme Corp"), 1
            ),
            Arguments.of(
                Map.of("customer_id", "1", "first_name", "John", "last_name", "Doe"),
                Map.of("id", 2, "name", "Acme Corp"),
                2
            ),
            Arguments.of(null, null, 0)
        );
    }

    @ParameterizedTest
    @MethodSource("guestRecipientData")
    void testGuestRecipient(SourceKind sourceKind, long propertyId, long recipientId, long organizationId) {
        var property = Property.newBuilder()
            .setOrganization(Organization.newBuilder().setId(organizationId).build()).build();
        when(propertyServiceClient.getProperty(propertyId)).thenReturn(property);

        var guest = PersonVersion.newBuilder()
            .setId(recipientId)
            .setContactDetails(ContactDetails.newBuilder()
                .setFirstName("Jane")
                .setLastName("Doe")
                .setEmail("<EMAIL>")
                .setPhone("123456")
                .build())
            .setAddress(Address.newBuilder()
                .setCity("City").setState("State").setCountry("US")
                .setZip("12345").setAddress1("Main").setAddress2("1A").build())
            .setTaxInfo(TaxInfo.newBuilder().setGuestTaxIdNumber("TAX123").build())
            .build();
        when(guestServiceClient.getGuestById(organizationId, recipientId)).thenReturn(
            Person.newBuilder().setCurrentVersion(guest).build());
        when(guestRequirementServiceClient.listGuestsRequirement(propertyId, recipientId))
            .thenReturn(GuestRequirements.newBuilder().build());

        var recipients = fiscalDocumentRecipientService.getRecipients(
            1L, propertyId,
            List.of(new RecipientRequest(RecipientRequest.TypeEnum.GUEST, recipientId)),
            sourceKind, 1L
        );

        assertEquals(1, recipients.size());
        var r = recipients.get(0).getRecipient();
        assertEquals(String.valueOf(recipientId), r.getId());
        assertEquals("Jane", r.getFirstName());
        assertEquals("Doe", r.getLastName());
        assertEquals("<EMAIL>", r.getEmail());
    }

    private static Stream<Arguments> guestRecipientData() {
        return Stream.of(Arguments.of(SourceKind.RESERVATION, 101L, 1001L, 999L));
    }

    @ParameterizedTest
    @MethodSource("groupRecipientData")
    void testGroupRecipient(SourceKind sourceKind, long propertyId, long recipientId, long groupProfileId) {
        var group = com.cloudbeds.group.v1.GroupProfile.newBuilder()
            .setId(groupProfileId)
            .setName("Acme Inc.")
            .setCountryCode("US")
            .setAddress1("123 Biz St")
            .setAddress2("Suite A")
            .setCity("SF")
            .setState("CA")
            .setZip("94105")
            .build();

        when(propertyServiceClient.getProperty(propertyId))
            .thenReturn(Property.newBuilder().setOrganization(Organization.newBuilder().setId(1L).build()).build());
        when(groupProfileServiceClient.listGroups(propertyId, List.of(groupProfileId))).thenReturn(List.of(group));

        var recipients = fiscalDocumentRecipientService.getRecipients(
            1L, propertyId,
            List.of(new RecipientRequest(RecipientRequest.TypeEnum.GROUP, recipientId)),
            sourceKind, groupProfileId
        );

        assertEquals(1, recipients.size());
        var r = recipients.get(0).getRecipient();
        assertEquals("Acme Inc.", r.getFirstName());
        assertEquals(Recipient.RecipientType.COMPANY, r.getType());
    }

    private static Stream<Arguments> groupRecipientData() {
        return Stream.of(Arguments.of(SourceKind.GROUP_PROFILE, 201L, 2001L, 5001L));
    }

    @ParameterizedTest
    @MethodSource("invalidSourceKinds")
    void testInvalidSourceKindsThrows(SourceKind kind) {
        long propertyId = 101L;
        long recipientId = 1001L;

        when(propertyServiceClient.getProperty(propertyId))
            .thenReturn(Property.newBuilder().setOrganization(Organization.newBuilder().setId(1L).build()).build());

        var request = new RecipientRequest(RecipientRequest.TypeEnum.GUEST, recipientId);

        assertThrows(FiscalDocumentException.class, () -> fiscalDocumentRecipientService.getRecipients(
            1L, propertyId, List.of(request), kind, 9999L
        ));
    }

    private static Stream<Arguments> invalidSourceKinds() {
        return Stream.of(
            Arguments.of(SourceKind.HOUSE_ACCOUNT),
            Arguments.of(SourceKind.ACCOUNTS_RECEIVABLE_LEDGER)
        );
    }

    @ParameterizedTest
    @MethodSource("groupContactRecipientData")
    void testGroupContactRecipient(SourceKind sourceKind, long propertyId, long recipientId, long groupProfileId) {
        var contact = GroupProfileContact.newBuilder()
            .setId(recipientId)
            .setFirstName("Alice")
            .setLastName("Smith")
            .addEmails(ContactDetail.newBuilder().setType("business").setValue("<EMAIL>"))
            .addPhones(ContactDetail.newBuilder().setType("work").setValue("123456"))
            .build();

        when(propertyServiceClient.getProperty(propertyId))
            .thenReturn(Property.newBuilder().setOrganization(Organization.newBuilder().setId(1L).build()).build());
        when(groupProfileServiceClient.getGroupProfileContactById(recipientId)).thenReturn(contact);

        var recipients = fiscalDocumentRecipientService.getRecipients(
            1L, propertyId,
            List.of(new RecipientRequest(RecipientRequest.TypeEnum.CONTACT, recipientId)),
            sourceKind, groupProfileId
        );

        assertEquals(1, recipients.size());
        var r = recipients.get(0).getRecipient();
        assertEquals("Alice", r.getFirstName());
        assertEquals("Smith", r.getLastName());
        assertEquals("<EMAIL>", r.getEmail());
    }

    private static Stream<Arguments> groupContactRecipientData() {
        return Stream.of(Arguments.of(SourceKind.GROUP_PROFILE, 301L, 3001L, 6001L));
    }

    @ParameterizedTest
    @MethodSource("recipientScenarios")
    void testFindRecipientsByFiscalDocumentId(List<FiscalDocumentRecipient> dbRecipients, int expectedSize) {
        long testId = 123L;
        when(fiscalDocumentRecipientRepository.findAllByFiscalDocumentId(testId)).thenReturn(dbRecipients);

        var result = fiscalDocumentRecipientService.findRecipientsByFiscalDocumentId(testId);

        assertEquals(expectedSize, result.size());

        if (expectedSize > 0) {
            var recipient = result.get(0);
            assertNotNull(recipient.getId());
            switch (recipient.getId()) {
                case "123" -> {
                    assertEquals("John", recipient.getFirstName());
                    assertEquals("Doe", recipient.getLastName());
                    assertEquals("<EMAIL>", recipient.getEmail());
                    assertEquals(
                        com.cloudbeds.fiscaldocument.controller.model.RecipientType.PERSON,
                        recipient.getType()
                    );

                    assertNotNull(recipient.getAddress());
                    assertEquals("123 Main St", recipient.getAddress().getAddress1());
                    assertEquals("Apt 1", recipient.getAddress().getAddress2());
                    assertEquals("NY", recipient.getAddress().getCity());
                    assertEquals("NY", recipient.getAddress().getState());
                    assertEquals("10001", recipient.getAddress().getZipCode());
                    assertEquals("US", recipient.getAddress().getCountry());

                    assertNotNull(recipient.getTax());
                    assertEquals("TAX123", recipient.getTax().getId());

                    assertNotNull(recipient.getContactDetails());
                    assertEquals("123456", recipient.getContactDetails().getPhone());
                    assertEquals(
                        "MALE",
                        recipient.getContactDetails().getGender()
                    );
                    assertEquals("654321", recipient.getContactDetails().getCellPhone());
                    assertEquals(OffsetDateTime.parse(
                        "1990-01-01T00:00:00Z"),
                        recipient.getContactDetails().getBirthday()
                    );

                    assertNotNull(recipient.getDocument());
                    assertEquals("PASSPORT", recipient.getDocument().getType());
                    assertEquals("*********", recipient.getDocument().getNumber());
                    assertEquals("US", recipient.getDocument().getIssuingCountry());
                    assertEquals(OffsetDateTime.parse(
                        "2010-01-01T00:00:00Z"),
                        recipient.getDocument().getIssueDate()
                    );
                    assertEquals(OffsetDateTime.parse(
                        "2030-01-01T00:00:00Z"),
                        recipient.getDocument().getExpirationDate()
                    );

                    assertNotNull(recipient.getCountryData());
                    assertTrue(recipient.getCountryData().containsKey("ES"));
                }

                case "124" -> {
                    assertEquals("Jane", recipient.getFirstName());
                }

                default -> Assertions.fail("Unexpected recipient ID: " + recipient.getId());
            }
        }
    }

    private static Stream<Arguments> recipientScenarios() {
        Recipient fullRecipient = new Recipient();
        fullRecipient.setId("123");
        fullRecipient.setFirstName("John");
        fullRecipient.setLastName("Doe");
        fullRecipient.setEmail("<EMAIL>");
        fullRecipient.setType(Recipient.RecipientType.PERSON);

        var address = new Recipient.Address();
        address.setAddress1("123 Main St");
        address.setAddress2("Apt 1");
        address.setCity("NY");
        address.setState("NY");
        address.setZipCode("10001");
        address.setCountry("US");
        fullRecipient.setAddress(address);

        var taxInfo = new Recipient.TaxInfo();
        taxInfo.setId("TAX123");
        taxInfo.setCompanyName("Acme Corp");
        fullRecipient.setTax(taxInfo);

        var contact = new Recipient.ContactDetails();
        contact.setPhone("123456");
        contact.setCellPhone("654321");
        contact.setGender("M");
        contact.setBirthday(Instant.parse("1990-01-01T00:00:00Z"));
        fullRecipient.setContactDetails(contact);

        var doc = new Recipient.Document();
        doc.setType("PASSPORT");
        doc.setNumber("*********");
        doc.setIssuingCountry("US");
        doc.setIssueDate(Instant.parse("2010-01-01T00:00:00Z"));
        doc.setExpirationDate(Instant.parse("2030-01-01T00:00:00Z"));
        fullRecipient.setDocument(doc);

        fullRecipient.setCountryData(Map.of("ES", Map.of("nif", "12345678A")));

        FiscalDocumentRecipient entityWithFull = new FiscalDocumentRecipient();
        entityWithFull.setRecipient(fullRecipient);

        // Partial
        Recipient partialRecipient = new Recipient();
        partialRecipient.setId("124");
        partialRecipient.setFirstName("Jane");
        FiscalDocumentRecipient entityWithPartial = new FiscalDocumentRecipient();
        entityWithPartial.setRecipient(partialRecipient);

        return Stream.of(
            Arguments.of(List.of(entityWithFull), 1),
            Arguments.of(List.of(), 0),
            Arguments.of(List.of(entityWithPartial), 1)
        );
    }

    @ParameterizedTest
    @MethodSource("validCombinations")
    void testCheckRecipientType_valid(SourceKind sourceKind, RecipientRequest.TypeEnum type) {
        RecipientRequest request = new RecipientRequest();
        request.setType(type);

        Assertions.assertDoesNotThrow(() -> fiscalDocumentRecipientService.checkRecipientType(sourceKind, request));
    }

    private Stream<Arguments> validCombinations() {
        return Stream.of(
            Arguments.of(SourceKind.RESERVATION, RecipientRequest.TypeEnum.GUEST),
            Arguments.of(SourceKind.RESERVATION, RecipientRequest.TypeEnum.COMPANY),
            Arguments.of(SourceKind.GROUP_PROFILE, RecipientRequest.TypeEnum.CONTACT),
            Arguments.of(SourceKind.GROUP_PROFILE, RecipientRequest.TypeEnum.GROUP)
        );
    }

    @ParameterizedTest
    @MethodSource("invalidCombinations")
    void testCheckRecipientType_invalid(SourceKind sourceKind, RecipientRequest.TypeEnum type) {
        RecipientRequest request = new RecipientRequest();
        request.setType(type);

        FiscalDocumentException exception = assertThrows(
            FiscalDocumentException.class,
            () -> fiscalDocumentRecipientService.checkRecipientType(sourceKind, request)
        );

        assertEquals(ErrorCode.INVALID_REQUEST, exception.getError().getCode());
        assertTrue(exception.getMessage().contains("Unsupported combination"));
    }

    private Stream<Arguments> invalidCombinations() {
        return Stream.of(
            Arguments.of(SourceKind.RESERVATION, RecipientRequest.TypeEnum.CONTACT),
            Arguments.of(SourceKind.RESERVATION, RecipientRequest.TypeEnum.GROUP),
            Arguments.of(SourceKind.GROUP_PROFILE, RecipientRequest.TypeEnum.GUEST),
            Arguments.of(SourceKind.GROUP_PROFILE, RecipientRequest.TypeEnum.COMPANY)
        );
    }
}

