package com.cloudbeds.fiscaldocument.services.data;

import com.cloudbeds.accounting.v1.InternalCode;
import com.cloudbeds.accounting.v1.InternalCodeGroup;
import com.cloudbeds.accounting.v1.Source;
import com.cloudbeds.booking.v1.Booking;
import com.cloudbeds.booking.v1.BookingWithRooms;
import com.cloudbeds.currency.v1.PropertyCurrency;
import com.cloudbeds.fiscaldocument.grpc.mfd.GroupProfileServiceClient;
import com.cloudbeds.fiscaldocument.grpc.mfd.PropertyCurrencyServiceClient;
import com.cloudbeds.fiscaldocument.models.InvoiceTemplateKeys;
import com.cloudbeds.fiscaldocument.models.TransactionDto;
import com.cloudbeds.fiscaldocument.services.S3Service;
import com.cloudbeds.organization.v1.Property;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@SpringBootTest(
        classes = TransactionDataExtractor.class
)
public class TransactionDataExtractorTest {

    @MockBean
    private PropertyCurrencyServiceClient propertyCurrencyServiceClient;

    @MockBean
    private GroupProfileServiceClient groupProfileServiceClient;

    @Autowired
    private TransactionDataExtractor transactionDataExtractor;

    @MockBean
    private S3Service s3Service;

    @Test
    public void testAddTemplateKeysFromTransactionsWithValidData() {
        TransactionDto transaction1 = new TransactionDto();
        transaction1.setInternalCode(
                InternalCode.newBuilder().setGroup(InternalCodeGroup.INTERNAL_CODE_GROUP_TAX).setCode("2000").build());
        transaction1.setAmount(new BigDecimal("102.50"));
        transaction1.setSource(Source.SOURCE_RESERVATION);
        transaction1.setSourceId(1L);
        transaction1.setSubSourceId(1L);
        transaction1.setTransactionDatetimePropertyTime(Instant.now());

        TransactionDto transaction2 = new TransactionDto();
        transaction2.setInternalCode(
                InternalCode.newBuilder().setGroup(InternalCodeGroup.INTERNAL_CODE_GROUP_FEE).setCode("3000").build());
        transaction2.setAmount(new BigDecimal("20.00"));
        transaction2.setSource(Source.SOURCE_RESERVATION);
        transaction2.setSourceId(1L);
        transaction2.setSubSourceId(1L);
        transaction2.setTransactionDatetimePropertyTime(Instant.now());

        List<TransactionDto> transactions = new ArrayList<>();
        transactions.add(transaction1);
        transactions.add(transaction2);

        List<TransactionDto> routedTransactions = new ArrayList<>();
        Property property = Property.newBuilder()
                .setId(1L)
                .setCurrency("USD")
                .build();

        PropertyCurrency propertyCurrency = PropertyCurrency.newBuilder()
                .setCurrencyCode("USD")
                .setIsActive(true)
                .setDisplayInvoiceRate(true).build();

        HashMap<Long, BookingWithRooms> bookingMap = new HashMap<>();
        bookingMap.put(
                1L,
                BookingWithRooms.newBuilder()
                        .setBooking(
                                Booking.newBuilder()
                                        .setCustomerName("name").setIdentifier("booking1").buildPartial()
                        ).build());
        Map<Long, Object> roomsMap = new HashMap<>();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ISO_DATE;

        when(propertyCurrencyServiceClient.listPropertyCurrencies(property.getId()))
                .thenReturn(List.of(propertyCurrency));

        InvoiceTemplateKeys templateKeys = new InvoiceTemplateKeys("en");
        transactionDataExtractor.addTemplateKeysFromTransactions(
                templateKeys,
                transactions,
                routedTransactions,
                property,
                true,
                false,
                false,
                bookingMap,
                Map.of(),
                dateFormatter
        );

        assertEquals(2, templateKeys.getTransactions().size());
        assertEquals("$ 122.50", templateKeys.getGrandTotal());
        assertEquals("USD", property.getCurrency());
    }
}