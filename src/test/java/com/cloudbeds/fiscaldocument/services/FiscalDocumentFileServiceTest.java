package com.cloudbeds.fiscaldocument.services;

import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import java.io.File;
import java.time.LocalDate;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@SpringBootTest(
    classes = { FiscalDocumentFileService.class }
)
public class FiscalDocumentFileServiceTest {
    
    @Autowired
    private FiscalDocumentFileService fiscalDocumentFileService;

    @MockBean
    private S3Service s3Service;

    @Test
    public void testUploadFile() {
        FiscalDocument testFiscalDocument = new FiscalDocument();
        testFiscalDocument.setId(1L);
        testFiscalDocument.setPropertyId(123L);
        testFiscalDocument.setInvoiceDate(LocalDate.of(2022, 10, 1));
        testFiscalDocument.setKind(DocumentKind.INVOICE);
        testFiscalDocument.setNumber("1234");
        testFiscalDocument.setVersion(0);

        var mockFile = Mockito.mock(File.class);
        var expectedFilePath = "123/2022/10/01/Invoice_1_1234.pdf";
        var actualFilePath = fiscalDocumentFileService.uploadFile(mockFile, testFiscalDocument, "pdf");

        assertEquals(expectedFilePath, actualFilePath);
        verify(s3Service).uploadContent(mockFile, "bucket-name", expectedFilePath);
    }

    @Test
    public void testGetContent() {
        FiscalDocument testFiscalDocument = new FiscalDocument();
        testFiscalDocument.setId(1L);
        testFiscalDocument.setPropertyId(123L);
        testFiscalDocument.setInvoiceDate(LocalDate.of(2022, 10, 1));
        testFiscalDocument.setKind(DocumentKind.INVOICE);
        testFiscalDocument.setNumber("1234");
        testFiscalDocument.setFilePath("123/2022/10/01/Invoice_1_1234.pdf");
        testFiscalDocument.setVersion(1);

        byte[] expectedContent = new byte[] {1, 2, 3};
        Mockito.when(s3Service.getContent("bucket-name", testFiscalDocument.getFilePath()))
            .thenReturn(expectedContent);

        var actualContent = fiscalDocumentFileService.getContent(testFiscalDocument);

        assertEquals(expectedContent, actualContent);
    }
}
