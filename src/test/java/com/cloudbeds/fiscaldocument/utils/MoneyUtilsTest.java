package com.cloudbeds.fiscaldocument.utils;

import com.cloudbeds.fiscaldocument.utils.MoneyUtils.CurrencyFormat;
import java.math.BigDecimal;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MoneyUtilsTest {
    @Test
    public void formatAmountCustomSeparatorsTest() {
        BigDecimal amount = new BigDecimal("123456.789");
        var currencyFormat = new CurrencyFormat(',', ' ');

        String expected = "$ 123 456,789";
        String actual = MoneyUtils.formatAmount(amount, "USD", currencyFormat);

        assertEquals(expected, actual, "Formatted amount is different from the expected one.");
    }

    @Test
    public void formatAmountDefaultFractionDigitsTest() {
        BigDecimal amount = new BigDecimal("5");
        String expected = "$ 5.00";
        String actual = MoneyUtils.formatAmount(amount, "USD", null);

        assertEquals(expected, actual, "Formatted amount is different from the expected one.");
    }

    @Test
    public void applyCurrencyScaleTest() {
        Long amount = 123456L;
        Currency currency = Currency.EUR;

        BigDecimal expected = new BigDecimal("1234.56");
        BigDecimal actual = MoneyUtils.applyCurrencyScale(amount, currency);

        assertEquals(expected, actual, "Formatted amount is different from the expected one.");
    }
}
