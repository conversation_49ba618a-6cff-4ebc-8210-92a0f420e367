package com.cloudbeds.fiscaldocument.utils;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

class QrCodeGeneratorTest {

    @Test
    void createQrCodeImage() {
        String qrCodeText = "https://example.com";
        int size = 200;

        try {
            var image = QrCodeGenerator.getCodeAsBase64Image(qrCodeText, size);
            assertNotNull(image);
        } catch (Exception e) {
            fail("QRCode generation failed: " + e.getMessage());
        }
    }
}