package com.cloudbeds.fiscaldocument.validation;

import com.cloudbeds.fiscaldocument.controller.model.ConfigsUpdateRequest;
import com.cloudbeds.fiscaldocument.controller.model.CreateCreditNoteRequest;
import com.cloudbeds.fiscaldocument.controller.model.CreateInvoiceRequest;
import com.cloudbeds.fiscaldocument.controller.model.CreationMethod;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentEmailRequest;
import com.cloudbeds.fiscaldocument.controller.model.RecipientRequest;
import com.cloudbeds.fiscaldocument.controller.model.SourceKind;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test class to verify that OpenAPI validation annotations are working correctly
 * for the generated model classes.
 */
public class OpenApiValidationTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testCreateInvoiceRequestValidation_ValidRequest() {
        CreateInvoiceRequest request = new CreateInvoiceRequest();
        request.setTransactionIds(List.of(1L, 2L));
        request.setSourceId(1L);
        request.setSourceKind(SourceKind.RESERVATION);
        request.setRecipient(
            new RecipientRequest(RecipientRequest.TypeEnum.GUEST, 1L)
        );
        request.setUserId(0L); // System user can be 0

        Set<ConstraintViolation<CreateInvoiceRequest>> violations = validator.validate(request);
        assertTrue(violations.isEmpty(), "Valid request should not have validation errors");
    }

    @Test
    void testCreateInvoiceRequestValidation_EmptyTransactionIds() {
        CreateInvoiceRequest request = new CreateInvoiceRequest();
        request.setTransactionIds(List.of()); // Empty list should fail
        request.setSourceId(1L);
        request.setSourceKind(SourceKind.RESERVATION);
        request.setRecipient(
            new RecipientRequest(RecipientRequest.TypeEnum.GUEST, 1L)
        );

        Set<ConstraintViolation<CreateInvoiceRequest>> violations = validator.validate(request);
        assertFalse(violations.isEmpty(), "Empty transaction IDs should cause validation error");

        boolean foundTransactionIdsViolation = violations.stream()
            .anyMatch(violation -> violation.getPropertyPath().toString().equals("transactionIds"));
        assertTrue(foundTransactionIdsViolation, "Should have violation on transactionIds property");
    }

    @Test
    void testCreateInvoiceRequestValidation_ZeroSourceId() {
        CreateInvoiceRequest request = new CreateInvoiceRequest();
        request.setTransactionIds(List.of(1L));
        request.setSourceId(0L); // Zero should fail @Min(1L)
        request.setSourceKind(SourceKind.RESERVATION);
        request.setRecipient(
            new RecipientRequest(RecipientRequest.TypeEnum.GUEST, 1L)
        );

        Set<ConstraintViolation<CreateInvoiceRequest>> violations = validator.validate(request);
        assertFalse(violations.isEmpty(), "Zero source ID should cause validation error");

        boolean foundSourceIdViolation = violations.stream()
            .anyMatch(violation -> violation.getPropertyPath().toString().equals("sourceId"));
        assertTrue(foundSourceIdViolation, "Should have violation on sourceId property");
    }

    @ParameterizedTest
    @MethodSource("invalidCreateInvoiceRequestData")
    void testCreateInvoiceRequestValidation_InvalidRequests(
        String testName,
        CreateInvoiceRequest request,
        String expectedViolationProperty
    ) {
        Set<ConstraintViolation<CreateInvoiceRequest>> violations = validator.validate(request);
        assertFalse(violations.isEmpty(), testName + " should have validation errors");
        
        boolean foundExpectedViolation = violations.stream()
            .anyMatch(violation -> violation.getPropertyPath().toString().equals(expectedViolationProperty));
        assertTrue(foundExpectedViolation,
            "Expected violation on property: " + expectedViolationProperty
            + ", but found violations on: " + violations.stream()
                .map(v -> v.getPropertyPath().toString())
                .toList());
    }

    @Test
    void testCreateCreditNoteRequestValidation_ValidRequest() {
        CreateCreditNoteRequest request = new CreateCreditNoteRequest();
        request.setInvoiceId(1L);
        request.setMethod(CreationMethod.VOID);
        request.setUserId(0L); // System user can be 0
        request.setReason("Valid reason - no length constraints applied");

        Set<ConstraintViolation<CreateCreditNoteRequest>> violations = validator.validate(request);
        assertTrue(violations.isEmpty(), "Valid request should not have validation errors");
    }

    @ParameterizedTest
    @MethodSource("invalidCreateCreditNoteRequestData")
    void testCreateCreditNoteRequestValidation_InvalidRequests(
        String testName,
        CreateCreditNoteRequest request,
        String expectedViolationProperty
    ) {
        Set<ConstraintViolation<CreateCreditNoteRequest>> violations = validator.validate(request);
        assertFalse(violations.isEmpty(), testName + " should have validation errors");

        boolean foundExpectedViolation = violations.stream()
            .anyMatch(violation -> violation.getPropertyPath().toString().equals(expectedViolationProperty));
        assertTrue(foundExpectedViolation,
            "Expected violation on property: " + expectedViolationProperty
            + ", but found violations on: " + violations.stream()
                .map(v -> v.getPropertyPath().toString())
                .toList());
    }

    @Test
    void testFiscalDocumentEmailRequestValidation_ValidRequest() {
        FiscalDocumentEmailRequest request = new FiscalDocumentEmailRequest();
        request.setEmails(List.of("<EMAIL>", "<EMAIL>"));

        Set<ConstraintViolation<FiscalDocumentEmailRequest>> violations = validator.validate(request);
        assertTrue(violations.isEmpty(), "Valid request should not have validation errors");
    }

    @Test
    void testConfigsUpdateRequestValidation_ValidRequest() {
        ConfigsUpdateRequest request = new ConfigsUpdateRequest();
        request.setShowDetailedTaxFee(true);
        request.setChargeBreakdown(false);
        request.setUseGuestLang(true);
        request.setShowLegalCompanyName(false);
        request.setIncludeRoomNumber(true);
        request.setIsCompact(false);
        request.setUseDocumentNumber(true);
        request.setDueDays(30);
        request.setLang("en-US");
        request.setCpf("123.456.789-00");

        Set<ConstraintViolation<ConfigsUpdateRequest>> violations = validator.validate(request);
        assertTrue(violations.isEmpty(), "Valid request should not have validation errors");
    }

    @Test
    void testConfigsUpdateRequestValidation_NullableFields() {
        ConfigsUpdateRequest request = new ConfigsUpdateRequest();
        request.setShowDetailedTaxFee(true);
        request.setChargeBreakdown(false);
        request.setUseGuestLang(true);
        request.setShowLegalCompanyName(false);
        request.setIncludeRoomNumber(true);
        request.setIsCompact(false);
        request.setUseDocumentNumber(true);
        // All nullable fields set to null
        request.setDueDays(null);
        request.setLang(null);
        request.setPrefix(null);
        request.setSuffix(null);
        request.setLegalCompanyName(null);
        request.setTaxId1(null);
        request.setTaxId2(null);
        request.setCpf(null);
        request.setTitle(null);
        request.setCustomText(null);

        Set<ConstraintViolation<ConfigsUpdateRequest>> violations = validator.validate(request);
        assertTrue(violations.isEmpty(), "Request with null nullable fields should not have validation errors");
    }

    private static Stream<Arguments> invalidCreateInvoiceRequestData() {
        return Stream.of(
            Arguments.of(
                "Empty transaction IDs",
                createInvoiceRequest(List.of(), 1L, SourceKind.RESERVATION, 1L, 0L),
                "transactionIds"
            ),
            Arguments.of(
                "Null transaction IDs",
                createInvoiceRequest(null, 1L, SourceKind.RESERVATION, 1L, 0L),
                "transactionIds"
            ),
            Arguments.of(
                "Zero source ID",
                createInvoiceRequest(List.of(1L), 0L, SourceKind.RESERVATION, 1L, 0L),
                "sourceId"
            ),
            Arguments.of(
                "Negative source ID",
                createInvoiceRequest(List.of(1L), -1L, SourceKind.RESERVATION, 1L, 0L),
                "sourceId"
            ),
            Arguments.of(
                "Zero recipient ID",
                createInvoiceRequest(List.of(1L), 1L, SourceKind.RESERVATION, 0L, 0L),
                "recipient.id"
            ),
            Arguments.of(
                "Null guest ID",
                createInvoiceRequest(List.of(1L), 1L, SourceKind.RESERVATION, null, 0L),
                "recipient.id"
            ),
            Arguments.of(
                "Negative user ID",
                createInvoiceRequest(List.of(1L), 1L, SourceKind.RESERVATION, 1L, -1L),
                "userId"
            )
        );
    }

    private static Stream<Arguments> invalidCreateCreditNoteRequestData() {
        return Stream.of(
            Arguments.of(
                "Zero invoice ID",
                createCreditNoteRequest(0L, CreationMethod.VOID, 0L, "reason"),
                "invoiceId"
            ),
            Arguments.of(
                "Negative invoice ID",
                createCreditNoteRequest(-1L, CreationMethod.VOID, 0L, "reason"),
                "invoiceId"
            ),
            Arguments.of(
                "Null method",
                createCreditNoteRequest(1L, null, 0L, "reason"),
                "method"
            ),
            Arguments.of(
                "Negative user ID",
                createCreditNoteRequest(1L, CreationMethod.VOID, -1L, "reason"),
                "userId"
            )
        );
    }

    private static CreateInvoiceRequest createInvoiceRequest(
        List<Long> transactionIds, Long sourceId, SourceKind sourceKind, Long guestId, Long userId
    ) {
        CreateInvoiceRequest request = new CreateInvoiceRequest();
        request.setTransactionIds(transactionIds);
        request.setSourceId(sourceId);
        request.setSourceKind(sourceKind);
        request.setRecipient(
            new RecipientRequest(RecipientRequest.TypeEnum.GUEST, guestId)
        );
        request.setUserId(userId);
        return request;
    }

    private static CreateCreditNoteRequest createCreditNoteRequest(
        Long invoiceId, CreationMethod method, Long userId, String reason
    ) {
        CreateCreditNoteRequest request = new CreateCreditNoteRequest();
        request.setInvoiceId(invoiceId);
        request.setMethod(method);
        request.setUserId(userId);
        request.setReason(reason);
        return request;
    }
}
