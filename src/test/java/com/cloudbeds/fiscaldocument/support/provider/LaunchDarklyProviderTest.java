package com.cloudbeds.fiscaldocument.support.provider;

import com.cloudbeds.fiscaldocument.support.features.ProviderInterface;
import com.cloudbeds.fiscaldocument.support.features.provider.LaunchDarklyProvider;
import com.launchdarkly.sdk.server.LDClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;

@SpringBootTest(
    classes = {
        LaunchDarklyProvider.class
    },
    properties = {
        "application.feature-management.provider-type=LAUNCH_DARKLY"
    }
)
class LaunchDarklyProviderTest {
    @Autowired
    private ProviderInterface featureFlagProvider;

    @MockBean
    private LDClient mockLdClient;

    @Test
    void testEvaluatePropertyFlag() {
        // Given
        Long propertyId = 1L;
        String flag = "test";
        when(mockLdClient.boolVariation(eq(flag), any(), eq(false))).thenReturn(true);

        // When
        boolean result = featureFlagProvider.evaluatePropertyFlag(propertyId, flag);

        // Then
        Assertions.assertTrue(result, "The result of evaluatePropertyFlag should be true");
    }

    @Test
    void testEvaluatePropertyFlag_defaultFlagResult() {
        // Given
        Long propertyId = 1L;
        String flag = "test";
        when(mockLdClient.boolVariation(eq(flag), any(), eq(false))).thenReturn(false);

        // When
        boolean result = featureFlagProvider.evaluatePropertyFlag(propertyId, flag);

        // Then
        Assertions.assertFalse(result, "The result of evaluatePropertyFlag should be false");
    }
}