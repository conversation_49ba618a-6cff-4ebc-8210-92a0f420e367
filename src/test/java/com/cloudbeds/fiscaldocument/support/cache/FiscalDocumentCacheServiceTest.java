package com.cloudbeds.fiscaldocument.support.cache;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for FiscalDocumentCacheService.
 * Tests the caching logic without requiring Redis infrastructure.
 */
@ExtendWith(MockitoExtension.class)
class FiscalDocumentCacheServiceTest {

    @Mock
    private CacheManager cacheManager;

    @Mock
    private Cache cache;

    @InjectMocks
    private FiscalDocumentCacheService fiscalDocumentCacheService;

    @Test
    void shouldGenerateCorrectCacheKey() {
        Long propertyId = 123L;
        String requestHash = "abc123def";
        
        String result = fiscalDocumentCacheService.generateCacheKey(propertyId, requestHash);
        
        assertThat(result).isEqualTo("123|abc123def");
    }

    @Test
    void shouldGenerateCorrectPropertyPrefix() {
        Long propertyId = 456L;
        
        String result = fiscalDocumentCacheService.getPropertyPrefix(propertyId);
        
        assertThat(result).isEqualTo("456|");
    }

    @Test
    void shouldCacheFiscalDocumentResponse() {
        String response = "Test fiscal document response";
        String cacheKey = "123|abc123";

        Object result = fiscalDocumentCacheService.cacheFiscalDocumentResponse(response, cacheKey);

        assertThat(result).isEqualTo(response);
    }

    @Test
    void shouldNotCacheNullResponse() {
        String cacheKey = "123|abc123";

        Object result = fiscalDocumentCacheService.cacheFiscalDocumentResponse(null, cacheKey);

        assertThat(result).isNull();
    }

    @Test
    void shouldRetrieveCachedResponse() {
        String cacheKey = "123|abc123";

        // The method returns null by default (cache miss simulation)
        Object result = fiscalDocumentCacheService.getFiscalDocumentResponseCache(cacheKey);

        assertThat(result).isNull();
    }

    @Test
    void shouldReturnNullWhenCacheEntryNotFound() {
        String cacheKey = "123|abc123";

        Object result = fiscalDocumentCacheService.getFiscalDocumentResponseCache(cacheKey);

        assertThat(result).isNull();
    }

    @Test
    void shouldEvictAllCacheEntriesForProperty() {
        Long propertyId = 123L;

        // This method uses @CacheEvict annotation, so we just test it doesn't throw
        fiscalDocumentCacheService.evictAllForProperty(propertyId);

        // No exception should be thrown
    }

    @Test
    void shouldInvalidateFiscalDocumentCache() {
        Long propertyId = 456L;

        // Setup cache manager to return the mock cache
        when(cacheManager.getCache("fiscalDocumentResponses")).thenReturn(cache);

        fiscalDocumentCacheService.invalidateFiscalDocumentCache(propertyId);

        // Verify evictIfPresent is called with the property prefix
        verify(cache).evictIfPresent("456|");
    }

    @Test
    void shouldHandleCacheManagerReturningNullCache() {
        // Setup cache manager to return null cache
        when(cacheManager.getCache("fiscalDocumentResponses")).thenReturn(null);

        // This should not throw an exception
        fiscalDocumentCacheService.invalidateFiscalDocumentCache(123L);

        // No exception should be thrown
    }

    @Test
    void shouldHandleEmptyStringInputs() {
        String result1 = fiscalDocumentCacheService.generateCacheKey(0L, "");
        assertThat(result1).isEqualTo("0|");

        String result2 = fiscalDocumentCacheService.getPropertyPrefix(0L);
        assertThat(result2).isEqualTo("0|");
    }

    @Test
    void shouldHandleNullPropertyId() {
        // Test with null property ID (edge case)
        String result = fiscalDocumentCacheService.generateCacheKey(null, "test");
        assertThat(result).isEqualTo("null|test");

        String prefix = fiscalDocumentCacheService.getPropertyPrefix(null);
        assertThat(prefix).isEqualTo("null|");
    }
}
