package com.cloudbeds.fiscaldocument.support.local;

import java.net.URI;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

@Configuration
public class AwsLocalConfig {

    /**
     * Configures and provides an S3Client for interacting with an S3 storage service in a local environment.
     * This method is annotated with @Profile("local") to ensure it is used when the active Spring profile is "local".
     *
     * @return an S3Client object configured for a local environment with the specified settings.
     */
    @Profile("local")
    @Bean
    public S3Client s3Client() {
        return S3Client.builder()
            .region(Region.US_EAST_1)
            .credentialsProvider(StaticCredentialsProvider.create(
                AwsBasicCredentials.create("test", "test")))
            .endpointOverride(URI.create("http://localhost:4566"))
            .forcePathStyle(true)
            .build();
    }
}
