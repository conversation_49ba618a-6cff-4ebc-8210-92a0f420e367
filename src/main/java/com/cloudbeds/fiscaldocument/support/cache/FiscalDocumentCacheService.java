package com.cloudbeds.fiscaldocument.support.cache;

import lombok.RequiredArgsConstructor;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class FiscalDocumentCacheService {
    private final CacheManager cacheManager;

    public static final String CACHE_NAME = "fiscalDocumentResponses";

    /**
     * Get from the cache, result is not cached.
     *
     * @param cacheKey Cache key
     * @return Cached response or null if not present
     */
    @Cacheable(cacheNames = {"fiscalDocumentResponses"}, key = "#cacheKey", unless = "#result == null")
    public Object getFiscalDocumentResponseCache(String cacheKey) {
        return null;
    }

    /**
     * Cache the response.
     *
     * @param response to cache
     * @param cacheKey cache key
     * @return same response object as the input parameter
     */
    @CachePut(cacheNames = {"fiscalDocumentResponses"}, key = "#cacheKey")
    public Object cacheFiscalDocumentResponse(Object response, String cacheKey) {
        return response;
    }

    /**
     * Invalidate cached responses for property.
     *
     * @param propertyId property id
     */
    public void invalidateFiscalDocumentCache(Long propertyId) {
        // Get all cache names and evict keys with property prefix
        var cache = cacheManager.getCache(CACHE_NAME);
        if (cache != null) {
            // This is a simplified approach - in production you might want to use Redis SCAN
            // to find and delete keys with the property prefix
            cache.evictIfPresent(getPropertyPrefix(propertyId));
        }
    }

    /**
     * Generate cache key for fiscal document requests.
     *
     * @param propertyId property id
     * @param requestHash hash of the request parameters
     * @return cache key
     */
    public String generateCacheKey(Long propertyId, String requestHash) {
        return getPropertyPrefix(propertyId) + requestHash;
    }

    public String getPropertyPrefix(Long propertyId) {
        return propertyId + "|";
    }

    /**
     * Evict all cache entries for a specific property.
     *
     * @param propertyId property id
     */
    @CacheEvict(cacheNames = {"fiscalDocumentResponses"}, allEntries = true)
    public void evictAllForProperty(Long propertyId) {
        // This will be handled by the annotation
    }
}
