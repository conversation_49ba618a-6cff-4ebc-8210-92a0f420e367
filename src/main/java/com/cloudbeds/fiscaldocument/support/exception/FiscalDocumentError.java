package com.cloudbeds.fiscaldocument.support.exception;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;


@Setter
@Getter
public class FiscalDocumentError {
    private String id;
    private LocalDateTime timestamp = LocalDateTime.now();
    private ErrorCode code;
    private String description;

    public FiscalDocumentError(ErrorCode code, String description) {
        this.code = code;
        this.description = description;
    }
}
