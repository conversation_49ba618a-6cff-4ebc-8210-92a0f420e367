package com.cloudbeds.fiscaldocument.support.exception;

/**
 * A custom exception class that provides more verbose error reporting.
 * This class always response to 400 status
 * Application is supposed to use this exception to mark problems.</p>
 */
public class FiscalDocumentException extends RuntimeException {
    private final FiscalDocumentError error;

    public FiscalDocumentException(ErrorCode code, String description) {
        this(code, description, null);
    }

    public FiscalDocumentException(ErrorCode code, String description, Throwable throwable) {
        super(description, throwable);
        error = new FiscalDocumentError(code, description);
    }

    public FiscalDocumentError getError() {
        return error;
    }
}
