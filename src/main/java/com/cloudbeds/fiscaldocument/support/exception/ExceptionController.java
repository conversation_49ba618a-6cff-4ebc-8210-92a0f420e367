package com.cloudbeds.fiscaldocument.support.exception;

import com.cloudbeds.dynamicquerying.exception.PageValidationException;
import com.cloudbeds.fiscaldocument.controller.model.ApiError;
import com.fasterxml.jackson.databind.exc.ValueInstantiationException;
import jakarta.validation.ValidationException;
import java.net.BindException;
import java.time.ZoneOffset;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.support.WebExchangeBindException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

/**
 * Registers a global exception handler.
 *
 * <p>The purpose of this class is to ensure, that all exceptions generated by the applications
 * are formatter according to the specification.</p>
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice
@RequiredArgsConstructor
public class ExceptionController {

    private static final Logger logger = LoggerFactory.getLogger(ExceptionController.class);

    /**
     * Default handler for {@link FiscalDocumentException}.
     */
    @ExceptionHandler(FiscalDocumentException.class)
    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ApiError handleFiscalDocumentException(FiscalDocumentException exception) {
        return convertError(exception.getError());
    }

    /**
     * Exception handler for WebExchangeBindException exceptions.
     *
     * @param ex WebExchangeBindException
     * @return ApiError
     */
    @ExceptionHandler(WebExchangeBindException.class)
    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ApiError handleValidationErrors(WebExchangeBindException ex) {
        var errorDetails = ex.getBindingResult().getFieldErrors()
                .stream()
                .map(fieldError -> fieldError.getField() + "-" + fieldError.getDefaultMessage())
                .collect(Collectors.joining(", "));

        var exception = new FiscalDocumentException(ErrorCode.INVALID_REQUEST, errorDetails);
        return convertError(exception.getError());
    }

    /**
     * Exception handler for AccessDeniedException exceptions.
     *
     * @param accessException AccessDeniedException
     * @return FiscalDocumentException
     */
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(value = HttpStatus.FORBIDDEN)
    @ResponseBody
    public ApiError handleAccessDeniedException(
        final AccessDeniedException accessException
    ) {
        return convertError(
            new FiscalDocumentException(ErrorCode.ACCESS_DENIED, "Access denied").getError()
        );
    }

    /**
     * Exception handler for MethodArgumentTypeMismatchException exceptions.
     *
     * @param ex MethodArgumentTypeMismatchException
     * @return ApiError
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ApiError handleEnumConversionError(MethodArgumentTypeMismatchException ex) {
        String param = ex.getName();
        Object value = ex.getValue();
        String message = String.format("Invalid value '%s' for parameter '%s'.", value, param);

        var exception = new FiscalDocumentException(ErrorCode.INVALID_REQUEST, message);
        return convertError(exception.getError());
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ApiError handlePersistenceErrors(DataIntegrityViolationException ex) {
        var exception = new FiscalDocumentException(ErrorCode.NOT_UNIQUE_VALUE, "Not unique value");
        return convertError(exception.getError());
    }

    /**
     * Default exception handler for Validation Exception raised by Open API.
     *
     * @param exception Exception
     * @return ApiAccountingError
     */
    @ExceptionHandler({
        MethodArgumentNotValidException.class,
        ValidationException.class,
        BindException.class,
        HttpMessageNotReadableException.class,
        PageValidationException.class
    })
    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ApiError handleValidationException(Exception exception) {
        logger.debug("Validation exception occurred", exception);

        var message = exception.getMessage();
        if (exception instanceof HttpMessageNotReadableException
                && exception.getCause() instanceof ValueInstantiationException valueInstantiationException
        ) {
            message = generateValueInstantiationExceptionMessage(valueInstantiationException);
        } else if (exception.getCause() instanceof FiscalDocumentException ae) {
            message = ae.getMessage();
        }

        return convertError(
                new FiscalDocumentException(ErrorCode.INVALID_REQUEST, message).getError()
        );
    }

    /**
     * Default exception handler for validating missing request headers.
     *
     * @param exception MissingRequestHeaderException object
     * @return AccountingError
     */
    @ExceptionHandler({MissingRequestHeaderException.class})
    @ResponseStatus(value = HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ApiError handleMissingRequestHeaderException(MissingRequestHeaderException exception) {
        logger.debug("MissingRequestHeaderException Error", exception);
        return convertError(
                new FiscalDocumentException(
                        ErrorCode.INVALID_PROPERTY_HEADER,
                        String.format(
                                "Required request header '%s' not found",
                                (exception).getHeaderName()
                        )
                ).getError()
        );
    }

    /**
     * Default exception handler for Validation.
     *
     * @param exception methodArgumentNotValidException
     * @return AccountingError
     */
    @ExceptionHandler({Exception.class})
    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public ApiError handleAnyException(Exception exception) {
        logger.error("Validation Error", exception);
        return convertError(
                new FiscalDocumentException(ErrorCode.UNEXPECTED_ERROR, "Unexpected error occurred").getError()
        );
    }

    private ApiError convertError(FiscalDocumentError error) {
        var apiError = new ApiError();
        if (StringUtils.hasText(error.getId())) {
            apiError.setId(error.getId());
        }
        apiError.setTimestamp(error.getTimestamp().atOffset(ZoneOffset.UTC));
        apiError.setErrorCode(convertErrorCode(error.getCode()));
        apiError.errorDetails(error.getDescription());

        return apiError;
    }

    private String convertErrorCode(ErrorCode errorCode) {
        return errorCode.toString();
    }

    private String generateValueInstantiationExceptionMessage(ValueInstantiationException ex) {
        StringBuilder pathString = new StringBuilder();
        for (var path : ex.getPath()) {
            if (path.getFieldName() != null) {
                if (!pathString.isEmpty()) {
                    pathString.append(".");
                }
                pathString.append(path.getFieldName());
            } else if (path.getIndex() >= 0) {
                pathString.append("[").append(path.getIndex()).append("]");
            }
        }

        if (ex.getCause() != null) {
            pathString.append(": ").append(ex.getCause().getMessage());
        } else {
            pathString.append(": ").append(ex.getMessage());
        }

        return pathString.toString();
    }
}
