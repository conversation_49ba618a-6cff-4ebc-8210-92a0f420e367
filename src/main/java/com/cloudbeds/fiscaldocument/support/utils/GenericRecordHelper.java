package com.cloudbeds.fiscaldocument.support.utils;

import java.time.Instant;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;

@Slf4j
public class GenericRecordHelper {

    /**
     * Get long value or null from GenericRecord property by name.
     *
     * @param record GenericRecord
     * @param name Name
     * @return Long value
     */
    public static Long longOrNull(GenericRecord record, String name) {
        if (!record.hasField(name)) {
            return null;
        }

        var value = record.get(name);

        if (value != null) {
            if (value instanceof Integer) {
                return (long) (int) value;
            } else {
                return (long) value;
            }
        } else {
            return null;
        }
    }

    /**
     * Convert long value from GenericRecord property by name.
     *
     * @param record GenericRecord
     * @param name Name
     * @return Long value
     */
    public static Long toLong(GenericRecord record, String name) {
        var value = record.get(name);

        if (value instanceof Integer) {
            return (long) (int) value;
        } else {
            return (long) value;
        }
    }

    /**
     * Get int value or null from GenericRecord property by name.
     *
     * @param record GenericRecord
     * @param name Name
     * @return Integer value
     */
    public static Integer intOrNull(GenericRecord record, String name) {
        if (!record.hasField(name)) {
            return null;
        }

        var value = record.get(name);

        return value != null ? (int) value : null;
    }

    /**
     * Get String value or null from GenericRecord property by name.
     *
     * @param record GenericRecord
     * @param name Name
     * @return String value
     */
    public static String stringOrNull(GenericRecord record, String name) {
        if (!record.hasField(name)) {
            return null;
        }

        var value = record.get(name);

        return value != null ? value.toString().replaceAll("\u0000", "") : null;
    }

    /**
     * Get Date value or null from GenericRecord property by name.
     *
     * @param record GenericRecord
     * @param name Name
     * @return LocalDate value
     */
    public static LocalDate toLocalDate(GenericRecord record, String name) {
        if (!record.hasField(name)) {
            return null;
        }

        var value = longOrNull(record, name);

        if (value != null) {
            return LocalDate.ofEpochDay(value);
        } else {
            return null;
        }
    }

    public static boolean isTrueString(GenericRecord record, String name) {
        return "true".equals(stringOrNull(record, name));
    }

    /**
     * Get Instant value or null from GenericRecord property by name.
     *
     * @param record GenericRecord
     * @param name Name
     * @return Instant value
     */
    public static Instant toInstant(GenericRecord record, String name) {
        if (!record.hasField(name)) {
            return null;
        }

        var milli = longOrNull(record, name);

        return milli != null ? Instant.ofEpochMilli(milli) : null;
    }

    /**
     * Get Double value or null from GenericRecord property by name.
     *
     * @param record GenericRecord
     * @param name Name
     * @return Double value
     */
    public static Double doubleOrNull(GenericRecord record, String name) {
        if (!record.hasField(name)) {
            return null;
        }

        var value = record.get(name);

        if (value != null) {
            return (double) value;
        } else {
            return null;
        }
    }
}
