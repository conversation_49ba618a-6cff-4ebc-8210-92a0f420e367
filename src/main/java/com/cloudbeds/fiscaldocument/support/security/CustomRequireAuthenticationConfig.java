package com.cloudbeds.fiscaldocument.support.security;

import com.cloudbeds.shared.api.AuthenticationRequestFilter;
import com.cloudbeds.shared.api.RequireAuthenticationConfig;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
@EnableMethodSecurity
@EnableWebSecurity
@RequiredArgsConstructor
public class CustomRequireAuthenticationConfig extends RequireAuthenticationConfig {

    private final AuthenticationRequestFilter authenticationRequestFilter;
    private final CustomAuthenticationEntryPoint customAuthenticationEntryPoint;

    @Bean(name = "RequireAuthenticationConfig")
    @Order(1)
    public SecurityFilterChain requireAuthenticationFilterChain(HttpSecurity http) throws Exception {
        return setupFilterChain(http);
    }

    /**
     * Cors configuration Source.
     *
     * @return CorsConfigurationSource
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(List.of("*"));
        configuration.setAllowedMethods(List.of("GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"));
        configuration.setAllowedHeaders(List.of("*"));
        configuration.setAllowCredentials(false);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    @Override
    protected HttpSecurity customizeAuthorizeHttpRequests(HttpSecurity http) throws Exception {
        http.csrf(AbstractHttpConfigurer::disable);
        http.cors(Customizer.withDefaults());

        http
            .authorizeHttpRequests(requests -> requests
                .requestMatchers(
                    "/actuator/**",
                    "/fiscal-document/swagger-ui/**",
                    "/fiscal-document/openapi.yaml",
                    "/fiscal-document/v3/api-docs/**"
                )
                .permitAll()
                .requestMatchers(HttpMethod.OPTIONS).permitAll()
                .anyRequest()
                .authenticated()
            )
            .sessionManagement(management -> management.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .exceptionHandling(handler -> handler.authenticationEntryPoint(customAuthenticationEntryPoint));

        http.addFilterBefore(authenticationRequestFilter, UsernamePasswordAuthenticationFilter.class);

        return http;
    }

    @Override
    protected HttpSecurity customizeOauth2ResourceServer(HttpSecurity http) {
        return http;
    }
}
