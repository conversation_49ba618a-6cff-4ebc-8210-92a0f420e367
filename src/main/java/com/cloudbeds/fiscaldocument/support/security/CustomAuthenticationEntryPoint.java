package com.cloudbeds.fiscaldocument.support.security;

import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import static com.cloudbeds.fiscaldocument.support.exception.ErrorCode.ACCESS_DENIED;

@Component
@Slf4j
@AllArgsConstructor
public class CustomAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper mapper;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        var exception = new FiscalDocumentException(ACCESS_DENIED, "Access denied");

        response.setContentType(MediaType.APPLICATION_JSON.toString());
        response.setStatus(HttpStatus.FORBIDDEN.value());
        response.getWriter().write(convertObjectToJson(exception.getError()));
    }

    private String convertObjectToJson(Object object) throws JsonProcessingException {
        if (object == null) {
            return null;
        }
        return mapper.writeValueAsString(object);
    }
}
