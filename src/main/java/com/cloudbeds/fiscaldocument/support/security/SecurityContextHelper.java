package com.cloudbeds.fiscaldocument.support.security;

import com.cloudbeds.shared.models.AuthenticatedUser;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
public class SecurityContextHelper {
    /**
     * Get authenticated user.
     *
     * @return AuthenticatedUser
     */
    public AuthenticatedUser getAuthenticatedUser() {
        var authentication = SecurityContextHolder.getContext().getAuthentication();

        return authentication != null
            ? (AuthenticatedUser) authentication.getPrincipal()
            : null;
    }
}
