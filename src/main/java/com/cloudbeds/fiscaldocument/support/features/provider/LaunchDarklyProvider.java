package com.cloudbeds.fiscaldocument.support.features.provider;

import com.cloudbeds.fiscaldocument.support.features.ProviderInterface;
import com.launchdarkly.sdk.ContextKind;
import com.launchdarkly.sdk.LDContext;
import com.launchdarkly.sdk.server.LDClient;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@ConditionalOnProperty(name = "application.feature-management.provider-type", havingValue = "LAUNCH_DARKLY")
@Service
public class LaunchDarklyProvider implements ProviderInterface {

    private final LDClient ldClient;

    @Value("${config.island}")
    private String island;

    public boolean evaluatePropertyFlag(Long propertyId, String flag) {
        return ldClient.boolVariation(flag, propertyIdToContext(propertyId), false);
    }

    private LDContext propertyIdToContext(Long propertyId) {
        return LDContext.builder("property:" + propertyId)
            .kind(ContextKind.DEFAULT)
            .set("island", island)
            .anonymous(false)
            .build();
    }
}
