package com.cloudbeds.fiscaldocument.support.features;

import com.launchdarkly.sdk.server.LDClient;
import com.launchdarkly.sdk.server.LDConfig;
import java.io.IOException;
import javax.annotation.PreDestroy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeatureManagementConfig {
    private LDClient client;

    @Value("${application.feature-management.launch-darkly.sdk-key}")
    private String ldSdkKey;

    @Bean
    @ConditionalOnProperty(name = "application.feature-management.provider-type", havingValue = "LAUNCH_DARKLY")
    LDClient ldClient() {
        LDConfig config = new LDConfig.Builder().build();

        return this.client = new LDClient(ldSdkKey, config);
    }

    /**
     * Shut down the LD client (if used) to allow it to send flag evaluation metrics.
     */
    @PreDestroy
    public void destroy() {
        if (this.client != null) {
            try {
                this.client.close();
            } catch (IOException e) {
                // Ignore
            }

        }
    }
}
