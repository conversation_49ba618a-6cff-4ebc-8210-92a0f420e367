package com.cloudbeds.fiscaldocument.support.features.aspect;

import com.cloudbeds.fiscaldocument.grpc.marketplace.MarketplaceServiceClient;
import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import com.cloudbeds.fiscaldocument.support.features.ProviderInterface;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;

import static com.cloudbeds.fiscaldocument.support.features.FeatureFlags.FISCAL_DOCUMENT_SERVICE_ENABLED;

@RequiredArgsConstructor
@Aspect
@Component
@Slf4j
public class ServiceEnabledCheckInterceptor implements Ordered {
    private final ProviderInterface providerInterface;
    private final MarketplaceServiceClient marketplaceServiceClient;

    /**
     * Tracks the execution of REST Controller methods annotated with ServiceEnabledCheck
     * to enforce service availability based on a property ID.
     *
     * @param joinPoint ProceedingJoinPoint
     * @return execute method or throw exception
     * @throws Throwable exception
     */
    @Around("(@within(org.springframework.web.bind.annotation.RestController) && @within(ServiceEnabledCheck)) || "
        + "(@within(org.springframework.web.bind.annotation.RestController) && @annotation(ServiceEnabledCheck))"
    )
    public Object trackExecution(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();

        Annotation[][] parameterAnnotations = method.getParameterAnnotations();
        Long propertyId = findPropertyId(parameterAnnotations, args);

        if (propertyId == null) {
            Method interfaceMethod = getInterfaceMethod(method, joinPoint.getTarget());
            if (interfaceMethod != null) {
                parameterAnnotations = interfaceMethod.getParameterAnnotations();
                propertyId = findPropertyId(parameterAnnotations, args);
            }
        }
        var enabled = false;
        if (propertyId != null) {
            enabled = providerInterface.evaluatePropertyFlag(propertyId, FISCAL_DOCUMENT_SERVICE_ENABLED);
        }

        if (!enabled) {
            throw new FiscalDocumentException(
                ErrorCode.ACCESS_DENIED,
                "Invoicing is disabled with feature flag for property " + propertyId);
        }

        return joinPoint.proceed();
    }

    private Long findPropertyId(Annotation[][] parameterAnnotations, Object[] args) {
        for (int i = 0; i < parameterAnnotations.length; i++) {
            for (Annotation annotation : parameterAnnotations[i]) {
                if (annotation instanceof RequestHeader requestHeader
                    && "X-Property-ID".equals(requestHeader.value())
                ) {
                    return args[i] != null ? (Long) args[i] : null;
                }
            }
        }
        return null;
    }

    private Method getInterfaceMethod(Method method, Object target) throws NoSuchMethodException {
        Class<?> targetClass = target.getClass();
        for (Class<?> interfaceClass : targetClass.getInterfaces()) {
            try {
                return interfaceClass.getMethod(method.getName(), method.getParameterTypes());
            } catch (NoSuchMethodException e) {
                // continue
            }
        }
        return null;
    }

    @Override
    public int getOrder() {
        return HIGHEST_PRECEDENCE + 1;
    }
}


