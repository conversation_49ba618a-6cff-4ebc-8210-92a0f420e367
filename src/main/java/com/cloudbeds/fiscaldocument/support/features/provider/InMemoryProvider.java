package com.cloudbeds.fiscaldocument.support.features.provider;

import com.cloudbeds.fiscaldocument.support.features.ProviderInterface;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

@Service
@ConditionalOnProperty(name = "application.feature-management.provider-type", havingValue = "IN_MEMORY")
@Slf4j
public class InMemoryProvider implements ProviderInterface {

    private static final String flagsFiles = "classpath:.local-feature-flags.json";
    private InMemoryFlags inMemoryFlags;

    public InMemoryProvider(final ObjectMapper objectMapper) {
        try {
            File flagsJson = ResourceUtils.getFile(flagsFiles);
            inMemoryFlags = objectMapper.readValue(flagsJson, InMemoryFlags.class);
        } catch (IOException e) {
            log.info("unable to load in memory flags", e);
            inMemoryFlags = new InMemoryFlags();
        }
    }

    @Override
    public boolean evaluatePropertyFlag(Long propertyId, String flag) {
        return inMemoryFlags.booleanMap().getOrDefault(flag, false);
    }
}
