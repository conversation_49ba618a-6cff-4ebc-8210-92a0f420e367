package com.cloudbeds.fiscaldocument.support.features.provider;

import com.cloudbeds.fiscaldocument.support.features.FeatureFlags;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;

public record InMemoryFlags(@JsonProperty("bool-features") Map<String, Boolean> booleanMap) {

    public InMemoryFlags() {
        this(Map.of(
            FeatureFlags.FISCAL_DOCUMENT_SERVICE_ENABLED, true
        ));
    }
}
