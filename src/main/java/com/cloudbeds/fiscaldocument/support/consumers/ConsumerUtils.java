package com.cloudbeds.fiscaldocument.support.consumers;

import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.listener.BatchInterceptor;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.util.backoff.FixedBackOff;

@Slf4j
public class ConsumerUtils {

    /**
     *  Get default factory for kafka listener.
     *
     * @param consumerFactory ConsumerFactory
     * @param concurrency concurrency
     * @param backOffMs backOffMs
     * @param <K> Key
     * @param <V> Value
     * @return Factory
     */
    public static <K, V> ConcurrentKafkaListenerContainerFactory<K, V> getDefaultListenerFactory(
            ConsumerFactory<K, V> consumerFactory,
            int concurrency,
            long backOffMs
    ) {
        ConcurrentKafkaListenerContainerFactory<K, V> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.setConcurrency(concurrency);
        factory.setCommonErrorHandler(new DefaultErrorHandler(new FixedBackOff(backOffMs, Long.MAX_VALUE)));
        return factory;
    }

    /**
     *  Get default factory for Batch listener.
     *
     * @param consumerFactory ConsumerFactory
     * @param concurrency concurrency
     * @param backOffMs backOffMs
     * @param <K> Key
     * @param <V> Value
     * @return Factory
     */
    public static <K, V> ConcurrentKafkaListenerContainerFactory<K, V> getDefaultConcurrentBatchListenerFactory(
        ConsumerFactory<K, V> consumerFactory,
        int concurrency,
        long backOffMs
    ) {
        ConcurrentKafkaListenerContainerFactory<K, V> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.setConcurrency(concurrency);
        factory.setBatchListener(true);
        factory.setBatchInterceptor(getBatchInterceptor());
        factory.setCommonErrorHandler(new DefaultErrorHandler(new FixedBackOff(backOffMs, Long.MAX_VALUE)));
        return factory;
    }

    private static <K, V> BatchInterceptor<K, V> getBatchInterceptor() {
        return new BatchInterceptor<>() {
            @Override
            public ConsumerRecords<K, V> intercept(ConsumerRecords<K, V> records, Consumer<K, V> consumer) {
                return records;
            }

            @Override
            public void failure(ConsumerRecords<K, V> records, Exception exception, Consumer<K, V> consumer) {
                var partitionData = "";
                try {
                    partitionData = records.partitions().stream()
                        .map(p -> {
                            var count = records.records(p).size();
                            if (count == 0) {
                                return "Partition " + p.topic();
                            }

                            var firstOffset = records.records(p).get(0).offset();
                            var lastOffset = records.records(p).get(count - 1).offset();

                            return "Partition " + p.topic() + "-" + p.partition()
                                + " offsets: " + firstOffset + " to " + lastOffset;
                        }).collect(Collectors.joining("; "));
                } catch (Exception e) {
                    log.error("Failed to get partition data", e);
                }

                log.error("Failed to process records for partitions: {}", partitionData, exception);
            }
        };
    }
}
