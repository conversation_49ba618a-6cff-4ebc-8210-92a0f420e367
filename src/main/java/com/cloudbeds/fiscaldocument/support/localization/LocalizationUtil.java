package com.cloudbeds.fiscaldocument.support.localization;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.ResourceBundle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LocalizationUtil {
    private static final Map<String, ResourceBundle> resourceBundleMap = new HashMap<>();
    public static final String BUNDLE_NAME = "l10n/messages";

    /**
     * Gets translation for message by key and language locale code.
     *
     * @param key Key of the message
     * @param langCode Language code for the translation
     * @return Translated message
     */
    public static String getTranslation(String key, String langCode) {
        var resourceBundle = getBundle(Locale.forLanguageTag(langCode));
        return resourceBundle.getString(key);
    }

    private static ResourceBundle getBundle(Locale locale) {
        try {
            String localeString = locale.toString();
            if (resourceBundleMap.containsKey(localeString)) {
                return resourceBundleMap.get(localeString);
            }

            synchronized (resourceBundleMap) {
                var resourceBundle = ResourceBundle.getBundle(BUNDLE_NAME, locale);
                resourceBundleMap.put(localeString, resourceBundle);
                return resourceBundle;
            }
        } catch (MissingResourceException e) {
            throw new RuntimeException("Unable to load resource bundle", e);
        }
    }
}
