package com.cloudbeds.fiscaldocument.support.locks;

import jakarta.persistence.LockModeType;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface TableLockRepository extends JpaRepository<TableLock, Long> {
    @Lock(LockModeType.PESSIMISTIC_WRITE)
    Optional<TableLock> findByTableName(String tableName);

    @Query(
        value = "SELECT * FROM locked_tables WHERE table_name = :tableName FOR UPDATE SKIP LOCKED",
        nativeQuery = true
    )
    Optional<TableLock> findByTableNameWithNoWait(@Param("tableName") String tableName);
}
