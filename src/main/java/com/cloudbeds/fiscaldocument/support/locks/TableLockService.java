package com.cloudbeds.fiscaldocument.support.locks;

import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class TableLockService {
    private final TableLockRepository tableLockRepository;

    /**
     * Check table for lock and wait if any.
     *
     * @param tableName table name
     */
    @Transactional
    public void acquireLock(String tableName) {
        tableLockRepository.findByTableName(tableName);
    }

    /**
     * Check table is lock and don't wait for it.
     *
     * @param tableName table name
     */
    @Transactional
    public boolean isTableLockedNonBlocking(String tableName) {
        Optional<TableLock> tableLock = tableLockRepository.findByTableNameWithNoWait(tableName);
        // If the result is present, it means the table is not locked by another transaction
        // If the result is empty, it means the table is currently locked and was skipped
        return tableLock.isEmpty();
    }
}
