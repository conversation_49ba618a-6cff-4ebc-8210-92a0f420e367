package com.cloudbeds.fiscaldocument.support.locks;

import com.cloudbeds.fiscaldocument.entity.DistributedId;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "locked_tables")
public class TableLock {
    @DistributedId
    @Id
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "table_name")
    private String tableName;

}
