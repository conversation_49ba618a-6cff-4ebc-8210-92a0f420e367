package com.cloudbeds.fiscaldocument.converters;

import com.cloudbeds.accounting.v1.ListPostedTransactionsResponse;
import com.cloudbeds.accounting.v1.ListPostedTransactionsWithFolioResponse;
import com.cloudbeds.accounting.v1.TransactionWithFolio;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentTransactionResponse;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentTransactionsPaginated;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentRecipientService;
import com.cloudbeds.guest.v1.PersonVersion;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ListPostedTransactionsResponseToRestConverter {
    private final FiscalDocumentRecipientService fiscalDocumentRecipientService;

    /**
     * Converts a {@link ListPostedTransactionsResponse} to a {@link FiscalDocumentTransactionsPaginated}.
     *
     * @param propertyId propertyId
     * @param source     the source object to convert
     * @return the converted object
     */
    public FiscalDocumentTransactionsPaginated convert(
        Long propertyId,
        ListPostedTransactionsWithFolioResponse source
    ) {
        if (source == null) {
            var document = new FiscalDocumentTransactionsPaginated();
            document.setNextPageToken("");
            document.setTransactions(List.of());
            return document;
        }

        var guestIds = source.getTransactionsList().stream()
            .map(a -> a.getTransaction().getCustomerId())
            .collect(Collectors.toSet());
        var guests = fiscalDocumentRecipientService.getGuestInfo(propertyId, guestIds)
            .stream().collect(Collectors.toMap(
                PersonVersion::getId,
                Function.identity()
            ));

        var result = new FiscalDocumentTransactionsPaginated();
        result.setTransactions(
            source.getTransactionsList().stream()
                .map(transaction -> convertOne(transaction, guests))
                .toList()
        );
        result.setNextPageToken(source.getNextPageToken());
        return result;
    }

    private FiscalDocumentTransactionResponse convertOne(
        TransactionWithFolio transactionWithFolio,
        Map<Long, PersonVersion> guests
    ) {
        var transaction = transactionWithFolio.getTransaction();
        var result = new FiscalDocumentTransactionResponse();
        result.setId(Long.toString(transaction.getId()));
        result.setAmount(BigDecimal.valueOf(transaction.getAmount(), transaction.getCurrencyScale()));
        result.setDescription(transaction.getDescription());
        result.setPropertyId(Long.toString(transaction.getPropertyId()));
        result.setSourceId(Long.toString(transaction.getSourceId()));
        result.setSourceKind(SourceKindConverter.convert(transaction.getSource()));
        result.setInternalCode(transaction.getInternalCode().getCode());
        if (transactionWithFolio.hasFolioId()) {
            result.setFolioId(Long.toString(transactionWithFolio.getFolioId()));
        }

        if (guests.containsKey(transaction.getCustomerId())) {
            var firstName = guests.get(transaction.getCustomerId()).getContactDetails().getFirstName();
            var lastName = guests.get(transaction.getCustomerId()).getContactDetails().getLastName();
            var fullName = firstName + " " + lastName;
            result.setGuestName(fullName);
        }

        var transactionInstant = Instant.ofEpochSecond(transaction.getTransactionDatetime().getSeconds());
        result.setTransactionDate(OffsetDateTime.ofInstant(transactionInstant, ZoneId.of("UTC")));
        return result;
    }
}
