package com.cloudbeds.fiscaldocument.converters;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventType;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.entity.Recipient;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.models.FiscalDocumentCdcModel;
import com.cloudbeds.fiscaldocument.models.InvoiceTransactionCdcModel;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@RequiredArgsConstructor
@Slf4j
public class FiscalDocumentCdcConverter {
    private final ObjectMapper objectMapper;

    /**
     * Converts a GenericRecord representing an invoice into a list of FiscalDocumentCdcModel.
     *
     * @param invoice the GenericRecord representing the invoice
     * @return a list of FiscalDocumentCdcModel
     */
    public List<FiscalDocumentCdcModel> convert(FiscalDocumentEventValue invoice) {

        if (invoice.getType().equals(FiscalDocumentEventType.MFD_INVOICE_EVENT)) {
            return convertMfdInvoiceEvent(invoice);
        }

        return List.of();
    }

    private List<FiscalDocumentCdcModel> convertMfdInvoiceEvent(FiscalDocumentEventValue fiscalDocumentEvent) {
        var mfdInvoiceEvent = fiscalDocumentEvent.getMfdInvoiceEvent();
        var invoiceModel = new FiscalDocumentCdcModel();
        invoiceModel.setId(mfdInvoiceEvent.getId());
        invoiceModel.setPropertyId(fiscalDocumentEvent.getPropertyId());
        invoiceModel.setNumber(mfdInvoiceEvent.getInvoiceNumber());
        var sourceKind = SourceKind.RESERVATION;
        if (fiscalDocumentEvent.getSourceKind().equals(com.cloudbeds.FiscalDocumentService.SourceKind.GROUP_PROFILE)) {
            sourceKind = SourceKind.GROUP_PROFILE;
        }
        invoiceModel.setSourceId(fiscalDocumentEvent.getSourceId());
        invoiceModel.setSourceKind(sourceKind);
        invoiceModel.setDocumentKind(DocumentKind.INVOICE);
        invoiceModel.setDate(mfdInvoiceEvent.getGenerateDate());
        invoiceModel.setPfdUrl(mfdInvoiceEvent.getPdfUrl());
        invoiceModel.setCurrency(mfdInvoiceEvent.getCurrency());
        invoiceModel.setAmount(mfdInvoiceEvent.getAmount());
        invoiceModel.setBalance(mfdInvoiceEvent.getBalance());
        var status = mfdInvoiceEvent.getStatus();
        if (status == null) {
            invoiceModel.setStatus(FiscalDocumentCdcModel.CdcDocumentStatus.OPEN);
        } else {
            invoiceModel.setStatus(FiscalDocumentCdcModel.CdcDocumentStatus.fromString(status));
        }

        var transactionsString = mfdInvoiceEvent.getTransactions();
        if (StringUtils.hasText(transactionsString)) {
            try {
                List<InvoiceTransactionCdcModel> transactionsModel =
                    objectMapper.readValue(transactionsString, new TypeReference<>() {});

                invoiceModel.setTransactionIds(
                    transactionsModel.stream()
                        .map(InvoiceTransactionCdcModel::getId)
                        .toList()
                );
            } catch (Exception e) {
                log.error("Error parsing transactions: {}", e.getMessage(), e);
                throw new RuntimeException("Error parsing transactions", e);
            }
        }

        var recipientsString = mfdInvoiceEvent.getRecipients();
        if (StringUtils.hasText(recipientsString)) {
            try {
                List<Recipient> recipients = objectMapper.readValue(
                    recipientsString, new TypeReference<List<Recipient>>() {}
                );

                List<FiscalDocumentRecipient> recipientEntities = recipients.stream()
                    .filter(dto -> dto.getId() != null)
                    .map(dto -> {
                        FiscalDocumentRecipient entity = new FiscalDocumentRecipient();
                        entity.setRecipientId(Long.valueOf(dto.getId()));
                        entity.setRecipient(dto);
                        return entity;
                    })
                    .toList();

                invoiceModel.setRecipients(recipientEntities);
            } catch (Exception e) {
                log.error("Error parsing recipients: {}", e.getMessage(), e);
                throw new RuntimeException("Error parsing recipients", e);
            }
        }


        var models = new ArrayList<FiscalDocumentCdcModel>();
        models.add(invoiceModel);

        var creditNoteNumber = mfdInvoiceEvent.getCreditNoteNumber();
        if (creditNoteNumber != null) {
            var creditNoteModel = new FiscalDocumentCdcModel();
            creditNoteModel.setId(mfdInvoiceEvent.getId());
            creditNoteModel.setPropertyId(mfdInvoiceEvent.getPropertyId());
            creditNoteModel.setNumber(creditNoteNumber);
            creditNoteModel.setSourceId(fiscalDocumentEvent.getSourceId());
            creditNoteModel.setSourceKind(sourceKind);

            creditNoteModel.setDocumentKind(DocumentKind.CREDIT_NOTE);
            creditNoteModel.setDate(mfdInvoiceEvent.getCreditNoteDate());
            creditNoteModel.setPfdUrl(mfdInvoiceEvent.getCreditNoteUrl());
            models.add(creditNoteModel);
        }

        return models;
    }
}
