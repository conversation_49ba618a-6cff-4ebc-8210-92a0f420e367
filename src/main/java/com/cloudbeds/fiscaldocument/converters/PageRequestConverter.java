package com.cloudbeds.fiscaldocument.converters;

import com.cloudbeds.dynamicquerying.enums.Direction;
import com.cloudbeds.dynamicquerying.model.PageRequest;
import com.cloudbeds.dynamicquerying.model.Sort;
import com.cloudbeds.dynamicquerying.parsing.FilterToSpecificationConverter;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.enums.FiscalDocumentSortField;
import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class PageRequestConverter extends
    FilterToSpecificationConverter<FiscalDocument> {

    /**
     * Prepare PageRequest object.
     *
     * @param size page size
     * @param token token
     * @param sort list of Sort objects
     * @return PageRequest
     */
    public PageRequest<FiscalDocument> createPageRequest(
        Integer size, String token,
        String sort
    ) {
        return new PageRequest<>(token, size, getSortModelList(sort));
    }

    /**
     * Convert the query param sort into a list of Sortable Fiscal Document fields.
     *
     * @param sort sort in string format from the query param
     * @return List of Sort Fiscal Document
     */
    public List<Sort<FiscalDocument>> getSortModelList(String sort) {
        List<Sort<FiscalDocument>> result = new ArrayList<>();

        if (sort != null && !sort.isBlank()) {
            String[] orderBys = sort.split(";");
            for (String orderBy : orderBys) {
                if (orderBy.isBlank()) {
                    continue;
                }

                String[] parts = orderBy.split(":");
                String field = parts[0].trim();
                String direction = parts.length > 1 ? parts[1].trim() : "asc";

                FiscalDocumentSortField sortField = FieldNameToEnumConverter.getSortEnum(
                    field, FiscalDocumentSortField.class
                );

                if (sortField == null) {
                    throw new FiscalDocumentException(
                        ErrorCode.INVALID_ARGUMENT_ERROR,
                        "Field '" + field + "' is not allowed for sorting or is non existing."
                    );
                }

                result.add(new Sort<>(sortField, getDirectionEnum(direction)));
            }
        }

        return result;
    }

    private Direction getDirectionEnum(String dir) {
        return "desc".equalsIgnoreCase(dir) ? Direction.DESC : Direction.ASC;
    }
}
