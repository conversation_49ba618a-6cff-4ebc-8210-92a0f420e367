package com.cloudbeds.fiscaldocument.converters;

import com.cloudbeds.accounting.v1.Transaction;
import com.cloudbeds.fiscaldocument.models.TransactionDto;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

public class TransactionToDtoConverter {

    /**
     * Converts Transactions to a TransactionDto.
     *
     * @param transactions transactions
     * @return the converted list with TransactionDto
     */
    public static List<TransactionDto> convertToDto(List<Transaction> transactions) {
        if (transactions == null || transactions.isEmpty()) {
            return List.of();
        }
        return transactions.stream()
            .map(transaction -> {
                var dto = new TransactionDto();
                dto.setId(transaction.getId());
                dto.setSource(transaction.getSource());
                dto.setSourceId(transaction.getSourceId());
                dto.setInternalCode(transaction.getInternalCode());
                dto.setDescription(transaction.getDescription());
                dto.setSubSourceId(transaction.getSubSourceId());
                dto.setRoutedFrom(transaction.getRoutedFrom());
                dto.setTransactionDatetimePropertyTime(
                    Instant.ofEpochSecond(
                        transaction.getTransactionDatetimePropertyTime().getSeconds()
                    )
                );
                dto.setAmount(BigDecimal.valueOf(transaction.getAmount(), transaction.getCurrencyScale()));
                dto.setAmountMinorUnits(BigInteger.valueOf(transaction.getAmount()));
                dto.setCurrency(transaction.getCurrency());
                dto.setQuantity(transaction.getQuantity());
                dto.setRootId(transaction.getRootId());
                dto.setParentId(transaction.getParentId());
                dto.setCurrencyScale(transaction.getCurrencyScale());
                return dto;
            })
            .collect(Collectors.toList());
    }
}
