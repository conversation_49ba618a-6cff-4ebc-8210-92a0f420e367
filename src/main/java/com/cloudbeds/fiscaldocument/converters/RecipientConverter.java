package com.cloudbeds.fiscaldocument.converters;

import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.controller.model.RecipientAddress;
import com.cloudbeds.fiscaldocument.controller.model.RecipientContactDetails;
import com.cloudbeds.fiscaldocument.controller.model.RecipientDocument;
import com.cloudbeds.fiscaldocument.controller.model.RecipientRequest;
import com.cloudbeds.fiscaldocument.controller.model.RecipientTaxInfo;
import com.cloudbeds.fiscaldocument.controller.model.RecipientType;
import com.cloudbeds.fiscaldocument.entity.Recipient;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RecipientConverter {
    private static final ZoneOffset DEFAULT_ZONE = ZoneOffset.UTC;

    /**
     * Converts an Entity {@code Recipient} to its corresponding avro model {@code Recipient}.
     *
     * @param recipient recipient
     * @return the corresponding avro representation {@code Recipient}
     */
    public com.cloudbeds.FiscalDocumentService.Recipient toAvro(Recipient recipient) {
        var avroRecipient = com.cloudbeds.FiscalDocumentService.Recipient.newBuilder()
            .setId(recipient.getId())
            .setFirstName(recipient.getFirstName())
            .setLastName(recipient.getLastName())
            .setEmail(recipient.getEmail())
            .setType(com.cloudbeds.FiscalDocumentService.RecipientType.valueOf(recipient.getType().name()))
            .setCountryData(recipient.getCountryData() != null
                ? new HashMap<>(recipient.getCountryData())
                : new HashMap<>()
            )
            .build();

        if (recipient.getAddress() != null) {
            avroRecipient.setAddress(
                com.cloudbeds.FiscalDocumentService.Address.newBuilder()
                    .setAddress1(recipient.getAddress().getAddress1())
                    .setAddress2(recipient.getAddress().getAddress2())
                    .setCity(recipient.getAddress().getCity())
                    .setState(recipient.getAddress().getState())
                    .setZipCode(recipient.getAddress().getZipCode())
                    .setCountry(recipient.getAddress().getCountry())
                    .build()
            );
        }

        if (recipient.getTax() != null) {
            avroRecipient.setTax(
                com.cloudbeds.FiscalDocumentService.TaxInfo.newBuilder()
                    .setId(recipient.getTax().getId())
                    .setCompanyName(recipient.getTax().getCompanyName())
                    .build()
            );
        }

        if (recipient.getContactDetails() != null) {
            var contactDetailsBuilder = com.cloudbeds.FiscalDocumentService.ContactDetails.newBuilder()
                .setPhone(recipient.getContactDetails().getPhone())
                .setCellPhone(recipient.getContactDetails().getCellPhone());

            if (recipient.getContactDetails().getGender() != null) {
                contactDetailsBuilder.setGender(recipient.getContactDetails().getGender());
            }

            if (recipient.getContactDetails().getBirthday() != null) {
                contactDetailsBuilder.setBirthday(recipient.getContactDetails().getBirthday());
            }

            avroRecipient.setContactDetails(contactDetailsBuilder.build());
        }

        if (recipient.getDocument() != null) {
            avroRecipient.setDocument(
                com.cloudbeds.FiscalDocumentService.Document.newBuilder()
                    .setType(recipient.getDocument().getType())
                    .setNumber(recipient.getDocument().getNumber())
                    .setIssuingCountry(recipient.getDocument().getIssuingCountry())
                    .setIssueDate(recipient.getDocument().getIssueDate() != null
                        ? recipient.getDocument().getIssueDate()
                        : null
                    )
                    .setExpirationDate(recipient.getDocument().getExpirationDate() != null
                        ? recipient.getDocument().getExpirationDate()
                        : null)
                    .build()
            );
        }

        return avroRecipient;
    }


    /**
     * Converts an Entity {@code Recipient} to its corresponding rest model {@code FiscalDocumentRecipient}.
     *
     * @param recipient recipient
     * @return the corresponding rest representation {@code FiscalDocumentRecipient}
     */
    public FiscalDocumentRecipient toRest(Recipient recipient) {
        var restRecipient = new FiscalDocumentRecipient();
        restRecipient.setId(recipient.getId());
        restRecipient.setFirstName(recipient.getFirstName());
        restRecipient.setLastName(recipient.getLastName());
        restRecipient.setEmail(recipient.getEmail());

        if (recipient.getType() != null) {
            restRecipient.setType(convertTypeToRest(recipient.getType()));
        }

        if (recipient.getAddress() != null) {
            var address = new RecipientAddress();
            address.setAddress1(recipient.getAddress().getAddress1());
            address.setAddress2(recipient.getAddress().getAddress2());
            address.setCity(recipient.getAddress().getCity());
            address.setState(recipient.getAddress().getState());
            address.setZipCode(recipient.getAddress().getZipCode());
            address.setCountry(recipient.getAddress().getCountry());
            restRecipient.setAddress(address);
        }

        if (recipient.getTax() != null) {
            var taxInfo = new RecipientTaxInfo();
            taxInfo.setId(recipient.getTax().getId());
            taxInfo.setCompanyName(recipient.getTax().getCompanyName());
            restRecipient.setTax(taxInfo);
        }

        if (recipient.getContactDetails() != null) {
            var contact = new RecipientContactDetails();
            contact.setPhone(recipient.getContactDetails().getPhone());
            contact.setCellPhone(recipient.getContactDetails().getCellPhone());
            if (recipient.getContactDetails().getGender() != null) {
                contact.setGender(mapGenderToFullValue(recipient.getContactDetails().getGender()));
            }
            if (recipient.getContactDetails().getBirthday() != null) {
                contact.setBirthday(OffsetDateTime.ofInstant(
                    recipient.getContactDetails().getBirthday(),
                    DEFAULT_ZONE
                ));
            }
            restRecipient.setContactDetails(contact);
        }

        if (recipient.getDocument() != null) {
            var doc = new RecipientDocument();
            doc.setType(recipient.getDocument().getType());
            doc.setNumber(recipient.getDocument().getNumber());
            doc.setIssuingCountry(recipient.getDocument().getIssuingCountry());
            if (recipient.getDocument().getIssueDate() != null) {
                doc.setIssueDate(OffsetDateTime.ofInstant(
                    recipient.getDocument().getIssueDate(),
                    DEFAULT_ZONE
                ));
            }

            if (recipient.getDocument().getExpirationDate() != null) {
                doc.setExpirationDate(OffsetDateTime.ofInstant(
                    recipient.getDocument().getExpirationDate(),
                    DEFAULT_ZONE
                ));
            }
            restRecipient.setDocument(doc);
        }

        if (recipient.getCountryData() != null && !recipient.getCountryData().isEmpty()) {
            restRecipient.setCountryData(recipient.getCountryData());
        }

        return restRecipient;
    }

    /**
     * Converts an Entity {@code RecipientRequest.TypeEnum} to its corresponding model {@code FiscalDocumentRecipient}.
     *
     * @param recipientType recipientType
     * @return the corresponding rest representation {@code FiscalDocumentRecipient}
     */
    public static Recipient.RecipientType toRecipientType(RecipientRequest.TypeEnum recipientType) {
        return switch (recipientType) {
            case GUEST, CONTACT -> Recipient.RecipientType.PERSON;
            case COMPANY, GROUP -> Recipient.RecipientType.COMPANY;
        };
    }

    private RecipientType convertTypeToRest(
        Recipient.RecipientType type
    ) {
        return switch (type) {
            case PERSON -> RecipientType.PERSON;
            case COMPANY -> RecipientType.COMPANY;
        };
    }

    /**
     * Maps short gender codes to full values.
     *
     * @param gender the gender string (e.g., "M", "F", "MALE", "FEMALE")
     * @return the corresponding full value
     */
    private String mapGenderToFullValue(String gender) {
        if (gender == null) {
            return null;
        }

        return switch (gender.toUpperCase()) {
            case "M", "MALE" -> "MALE";
            case "F", "FEMALE" -> "FEMALE";
            case "O", "OTHER" -> "OTHER";
            case "P", "PREFER_NOT_TO_SAY" -> "PREFER_NOT_TO_SAY";
            default -> gender.toUpperCase(); // fallback to original value
        };
    }
}
