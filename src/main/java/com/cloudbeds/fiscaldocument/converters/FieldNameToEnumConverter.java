package com.cloudbeds.fiscaldocument.converters;

import com.cloudbeds.dynamicquerying.model.Value;
import com.cloudbeds.fiscaldocument.support.dynamicquery.NamedSortFieldInterface;
import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import java.math.BigDecimal;
import java.util.List;

public class FieldNameToEnumConverter {

    /**
     * Convert REST API Value to Dynamic query Value object.
     *
     * @param value REST API Value
     * @return Dynamic query Value object
     */
    public static Value parseValue(
        Object value,
        Class<?> restType
    ) throws NumberFormatException {
        if (value == null) {
            return new Value();
        } else if ((restType == Long.class || restType == Integer.class)) {
            if (value instanceof List<?> listValue) {
                var numberValues = listValue
                    .stream()
                    .map(FieldNameToEnumConverter::getBigDecimalFromObject)
                    .map(Value::new)
                    .toList();

                return new Value(numberValues);
            }

            return new Value(getBigDecimalFromObject(value));
        } else if (restType == String.class) {
            if (value instanceof List<?>) {
                @SuppressWarnings("unchecked")
                var stringValues = ((List<String>) value)
                    .stream()
                    .map(Value::new)
                    .toList();

                return new Value(stringValues);
            }

            return new Value((String) value);
        } else {
            throw new FiscalDocumentException(ErrorCode.INVALID_REQUEST, "Unsupported operation");
        }
    }

    /**
     * Find Field for Dynamic query sorting by name.
     *
     * @param value field name
     * @param enumClass Enum with sort fields
     * @return Dynamic query Field
     */
    public static <E extends Enum<E> & NamedSortFieldInterface<T>, T> E getSortEnum(
        String value,
        Class<E> enumClass
    ) {
        if (enumClass.isEnum() && value != null) {
            E[] enumConstants = enumClass.getEnumConstants();

            for (E field : enumConstants) {
                if (field.getFieldName().equalsIgnoreCase(value)
                    || field.getAlternativeName().equalsIgnoreCase(value)) {
                    return field;
                }
            }
        }

        return null;
    }

    private static BigDecimal getBigDecimalFromObject(Object value) {
        if (value instanceof Integer intValue) {
            return BigDecimal.valueOf(intValue);
        }

        if (value instanceof Long longValue) {
            return BigDecimal.valueOf(longValue);
        }

        return BigDecimal.valueOf(Long.parseLong((String) value));
    }
}
