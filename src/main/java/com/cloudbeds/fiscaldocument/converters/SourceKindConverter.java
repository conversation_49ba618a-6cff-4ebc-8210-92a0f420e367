package com.cloudbeds.fiscaldocument.converters;

import com.cloudbeds.accounting.v1.Source;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import org.springframework.stereotype.Component;

@Component
public class SourceKindConverter {

    /**
     * Converts an API-level {@code SourceKind} enum to its corresponding domain-level {@code SourceKind} enum.
     *
     * @param apiSourceKind the {@code SourceKind} value received from the API layer
     * @return the corresponding domain-level {@code SourceKind} enum value
     */
    public static SourceKind toDomain(
        com.cloudbeds.fiscaldocument.controller.model.SourceKind apiSourceKind
    ) {
        if (apiSourceKind == null) {
            return null;
        }

        return switch (apiSourceKind) {
            case RESERVATION -> SourceKind.RESERVATION;
            case GROUP_PROFILE -> SourceKind.GROUP_PROFILE;
            case HOUSE_ACCOUNT -> SourceKind.HOUSE_ACCOUNT;
            case ACCOUNTS_RECEIVABLE_LEDGER -> SourceKind.ACCOUNTS_RECEIVABLE_LEDGER;
        };
    }

    /**
     * Converts a domain-level {@code SourceKind} enum to its corresponding API-level {@code SourceKind} enum.
     *
     * @param source the {@code SourceKind} value received from the domain layer
     * @return the corresponding API-level {@code SourceKind} enum value
     */
    public static com.cloudbeds.fiscaldocument.controller.model.SourceKind convert(Source source) {
        if (source == null) {
            return null;
        }

        return switch (source) {
            case SOURCE_RESERVATION -> com.cloudbeds.fiscaldocument.controller.model.SourceKind.RESERVATION;
            case SOURCE_GROUP_PROFILE -> com.cloudbeds.fiscaldocument.controller.model.SourceKind.GROUP_PROFILE;
            case SOURCE_HOUSE_ACCOUNT -> com.cloudbeds.fiscaldocument.controller.model.SourceKind.HOUSE_ACCOUNT;
            case SOURCE_ACCOUNTS_RECEIVABLE_LEDGER ->
                com.cloudbeds.fiscaldocument.controller.model.SourceKind.ACCOUNTS_RECEIVABLE_LEDGER;
            case UNRECOGNIZED, SOURCE_CITY_LEDGER, SOURCE_UNSPECIFIED -> null;
        };
    }
}

