package com.cloudbeds.fiscaldocument.converters;

import com.cloudbeds.fiscaldocument.entity.GovernmentIntegration;
import org.springframework.stereotype.Component;

@Component
public class GovernmentIntegrationToEntityConverter {

    /**
     * Converts a {@link com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration}
     * to a {@link GovernmentIntegration} entity.
     *
     * @param governmentIntegration the source object to convert
     * @return the converted entity, or null if the source object is null
     */
    public GovernmentIntegration convert(
            com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration governmentIntegration
    ) {
        if (governmentIntegration == null) {
            return null;
        }

        GovernmentIntegration entity = new GovernmentIntegration();
        entity.setNumber(governmentIntegration.getNumber());
        entity.setSeries(governmentIntegration.getSeries());
        entity.setOfficialId(governmentIntegration.getOfficialId());
        entity.setExternalId(governmentIntegration.getExternalId());
        entity.setRectifyingInvoiceType(governmentIntegration.getRectifyingInvoiceType());
        entity.setStatus(governmentIntegration.getStatus());
        entity.setUrl(governmentIntegration.getUrl() != null
            ? governmentIntegration.getUrl().toString() : null);

        if (governmentIntegration.getQr() != null) {
            GovernmentIntegration.Qr qr = new GovernmentIntegration.Qr();
            qr.setUrl(governmentIntegration.getQr().getUrl() != null
                ? governmentIntegration.getQr().getUrl().toString() : null);
            qr.setString(governmentIntegration.getQr().getString());
            entity.setQr(qr);
        }

        return entity;
    }
}
