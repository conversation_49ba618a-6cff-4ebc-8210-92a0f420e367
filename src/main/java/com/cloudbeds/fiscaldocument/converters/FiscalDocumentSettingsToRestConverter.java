package com.cloudbeds.fiscaldocument.converters;

import com.cloudbeds.fiscaldocument.controller.model.ConfigsResponse;
import com.cloudbeds.fiscaldocument.entity.DocumentConfig;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class FiscalDocumentSettingsToRestConverter {

    /**
     * Convert List of Configs to List of REST API representation.
     *
     * @param configs List of configs
     * @return REST API list representation of the configs
     */
    public List<ConfigsResponse> convert(List<DocumentConfig> configs) {
        return configs.stream()
            .map(this::convert)
            .toList();
    }

    /**
     * Convert a single DocumentConfig to REST API representation.
     *
     * @param config DocumentConfig
     * @return REST API representation of the config
     */
    public ConfigsResponse convert(DocumentConfig config) {
        var response = new ConfigsResponse();

        response.setPropertyId(config.getPropertyId().toString());
        response.setDocumentKind(convertToRest(config.getDocumentKind()));
        response.setShowDetailedTaxFee(config.isShowDetailedTaxFee());
        response.setChargeBreakdown(config.isChargeBreakdown());
        response.setUseGuestLang(config.isUseGuestLang());
        response.setDueDays(config.getDueDays());
        response.setLang(config.getLang());
        response.setPrefix(config.getPrefix());
        response.setSuffix(config.getSuffix());
        response.setLegalCompanyName(config.getLegalCompanyName());
        response.setTitle(config.getTitle());
        response.setShowLegalCompanyName(config.isShowLegalCompanyName());
        response.setIncludeRoomNumber(config.isIncludeRoomNumber());
        response.setUseDocumentNumber(config.isUseDocumentNumber());
        response.setIsCompact(config.isCompact());
        response.setTaxId1(config.getTaxId1());
        response.setTaxId2(config.getTaxId2());
        response.setCpf(config.getCpf());
        response.setCustomText(config.getCustomText());
        return response;
    }

    private com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind convertToRest(DocumentKind documentKind) {
        if (documentKind == null) {
            return null;
        }

        return switch (documentKind) {
            case INVOICE -> com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind.INVOICE;
            case CREDIT_NOTE -> com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind.CREDIT_NOTE;
            case RECEIPT -> com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind.RECEIPT;
            case RECTIFY_INVOICE -> com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind.RECTIFY_INVOICE;
        };
    }

}



