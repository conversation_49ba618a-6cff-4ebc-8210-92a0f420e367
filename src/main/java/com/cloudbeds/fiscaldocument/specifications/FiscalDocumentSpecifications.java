package com.cloudbeds.fiscaldocument.specifications;

import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument_;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import java.util.List;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

public class FiscalDocumentSpecifications {

    /**
     * Builds a JPA Specification for filtering {@code FiscalDocument} entities based on multiple criteria.
     *
     * @param propertyId        Property ID.
     * @param ids               List of ids.
     * @param sourceIds         List of source document IDs to filter by.
     * @param sourceIdentifiers List of external source identifiers to filter by.
     * @param sourceKind        Enum representing the kind of source.
     * @param statuses          List of document statuses to filter by.
     * @param kinds             List of fiscal document kinds to filter by.
     * @param numberContains    A string to search for within the document number, case-insensitive.
     * @return A {@code Specification<FiscalDocument>} instance representing the combined filter.
     */
    public static Specification<FiscalDocument> fromFilters(
        Long propertyId,
        List<String> ids,
        List<String> sourceIds,
        List<String> sourceIdentifiers,
        SourceKind sourceKind,
        List<FiscalDocumentStatus> statuses,
        List<FiscalDocumentKind> kinds,
        String numberContains
    ) {
        Specification<FiscalDocument> spec = Specification.where(propertyIdEqual(propertyId));

        if (ids != null && !ids.isEmpty()) {
            spec = spec.and(ids(ids));
        }

        if (sourceIds != null && !sourceIds.isEmpty()) {
            spec = spec.and(sourceIds(sourceIds));
        }

        if (sourceIdentifiers != null && !sourceIdentifiers.isEmpty()) {
            spec = spec.and(sourceIdentifiers(sourceIdentifiers));
        }

        if (sourceKind != null) {
            spec = spec.and(sourceKind(sourceKind));
        }

        if (statuses != null && !statuses.isEmpty()) {
            spec = spec.and(statuses(statuses));
        }

        if (StringUtils.hasText(numberContains)) {
            spec = spec.and(numberContains(numberContains));
        }

        if (kinds != null && !kinds.isEmpty()) {
            spec = spec.and(kinds(kinds));
        }

        return spec;
    }

    private static Specification<FiscalDocument> numberContains(String number) {
        return (root, query, cb) ->
            cb.like(cb.lower(root.get(FiscalDocument_.NUMBER)), "%" + number.toLowerCase() + "%");
    }

    /**
     * Creates a {@link Specification} to filter a {@link FiscalDocument} by its unique ID and associated property ID.
     * This specification is typically used to ensure that a document belongs to a specific property context,
     * which is critical for multi-tenant or scoped access logic.
     *
     * @param id         the unique identifier of the fiscal document
     * @param propertyId the ID of the property to which the fiscal document must belong
     * @return a {@link Specification} for querying a fiscal document with the specified ID and property ID
     */
    public static Specification<FiscalDocument> idAndPropertyId(String id, Long propertyId) {
        return (root, query, criteriaBuilder) ->
            criteriaBuilder.and(
                criteriaBuilder.equal(root.get("id"), id),
                criteriaBuilder.equal(root.get("propertyId"), propertyId)
            );
    }

    private static Specification<FiscalDocument> ids(List<String> ids) {
        return (root, query, cb) ->
            (ids == null || ids.isEmpty()) ? null : root.get("id").in(ids);
    }

    private static Specification<FiscalDocument> propertyIdEqual(Long propertyId) {
        return (root, query, cb) ->
            cb.equal(root.get("propertyId"), propertyId);
    }

    private static Specification<FiscalDocument> sourceIds(List<String> sourceIds) {
        return (root, query, cb) ->
            (sourceIds == null || sourceIds.isEmpty()) ? null : root.get("sourceId").in(sourceIds);
    }

    private static Specification<FiscalDocument> sourceIdentifiers(List<String> sourceIdentifiers) {
        return (root, query, cb) ->
            (sourceIdentifiers == null || sourceIdentifiers.isEmpty())
                ? null
                : root.get("sourceIdentifier").in(sourceIdentifiers);
    }

    private static Specification<FiscalDocument> sourceKind(SourceKind sourceKind) {
        return (root, query, cb) ->
            (sourceKind == null) ? null : cb.equal(root.get("sourceKind"), sourceKind);
    }

    private static Specification<FiscalDocument> statuses(
        List<com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus> statuses) {
        return (root, query, cb) ->
            (statuses == null || statuses.isEmpty()) ? null : root.get("status").in(statuses);
    }

    private static Specification<FiscalDocument> kinds(
        List<com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind> kinds) {
        return (root, query, cb) ->
            (kinds == null || kinds.isEmpty()) ? null : root.get("kind").in(kinds);
    }
}
