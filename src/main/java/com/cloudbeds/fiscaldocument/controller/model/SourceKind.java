package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Kind of the source entity
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public enum SourceKind {
  
  GROUP_PROFILE("GROUP_PROFILE"),
  
  RESERVATION("RESERVATION"),
  
  HOUSE_ACCOUNT("HOUSE_ACCOUNT"),
  
  ACCOUNTS_RECEIVABLE_LEDGER("ACCOUNTS_RECEIVABLE_LEDGER");

  private String value;

  SourceKind(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  @JsonCreator
  public static SourceKind fromValue(String value) {
    for (SourceKind b : SourceKind.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }
}

