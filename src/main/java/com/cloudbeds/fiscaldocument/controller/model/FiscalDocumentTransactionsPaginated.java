package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentTransactionResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * FiscalDocumentTransactionsPaginated
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class FiscalDocumentTransactionsPaginated implements Serializable {

  private static final long serialVersionUID = 1L;

  @Valid
  private List<@Valid FiscalDocumentTransactionResponse> transactions;

  private String nextPageToken;

  public FiscalDocumentTransactionsPaginated transactions(List<@Valid FiscalDocumentTransactionResponse> transactions) {
    this.transactions = transactions;
    return this;
  }

  public FiscalDocumentTransactionsPaginated addTransactionsItem(FiscalDocumentTransactionResponse transactionsItem) {
    if (this.transactions == null) {
      this.transactions = new ArrayList<>();
    }
    this.transactions.add(transactionsItem);
    return this;
  }

  /**
   * Get transactions
   * @return transactions
  */
  @Valid 
  @Schema(name = "transactions", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("transactions")
  public List<@Valid FiscalDocumentTransactionResponse> getTransactions() {
    return transactions;
  }

  public void setTransactions(List<@Valid FiscalDocumentTransactionResponse> transactions) {
    this.transactions = transactions;
  }

  public FiscalDocumentTransactionsPaginated nextPageToken(String nextPageToken) {
    this.nextPageToken = nextPageToken;
    return this;
  }

  /**
   * Token for fetching the next page of results
   * @return nextPageToken
  */
  
  @Schema(name = "nextPageToken", description = "Token for fetching the next page of results", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("nextPageToken")
  public String getNextPageToken() {
    return nextPageToken;
  }

  public void setNextPageToken(String nextPageToken) {
    this.nextPageToken = nextPageToken;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FiscalDocumentTransactionsPaginated fiscalDocumentTransactionsPaginated = (FiscalDocumentTransactionsPaginated) o;
    return Objects.equals(this.transactions, fiscalDocumentTransactionsPaginated.transactions) &&
        Objects.equals(this.nextPageToken, fiscalDocumentTransactionsPaginated.nextPageToken);
  }

  @Override
  public int hashCode() {
    return Objects.hash(transactions, nextPageToken);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FiscalDocumentTransactionsPaginated {\n");
    sb.append("    transactions: ").append(toIndentedString(transactions)).append("\n");
    sb.append("    nextPageToken: ").append(toIndentedString(nextPageToken)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

