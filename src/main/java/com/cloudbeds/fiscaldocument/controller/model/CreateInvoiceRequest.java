package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.RecipientRequest;
import com.cloudbeds.fiscaldocument.controller.model.SourceKind;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * CreateInvoiceRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class CreateInvoiceRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  @Valid
  private List<Long> transactionIds = new ArrayList<>();

  private Long sourceId;

  private Long sequenceId = null;

  private SourceKind sourceKind;

  private Long userId = null;

  private RecipientRequest recipient;

  public CreateInvoiceRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public CreateInvoiceRequest(List<Long> transactionIds, Long sourceId, SourceKind sourceKind, RecipientRequest recipient) {
    this.transactionIds = transactionIds;
    this.sourceId = sourceId;
    this.sourceKind = sourceKind;
    this.recipient = recipient;
  }

  public CreateInvoiceRequest transactionIds(List<Long> transactionIds) {
    this.transactionIds = transactionIds;
    return this;
  }

  public CreateInvoiceRequest addTransactionIdsItem(Long transactionIdsItem) {
    if (this.transactionIds == null) {
      this.transactionIds = new ArrayList<>();
    }
    this.transactionIds.add(transactionIdsItem);
    return this;
  }

  /**
   * Get transactionIds
   * @return transactionIds
  */
  @NotNull @Size(min = 1) 
  @Schema(name = "transactionIds", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("transactionIds")
  public List<Long> getTransactionIds() {
    return transactionIds;
  }

  public void setTransactionIds(List<Long> transactionIds) {
    this.transactionIds = transactionIds;
  }

  public CreateInvoiceRequest sourceId(Long sourceId) {
    this.sourceId = sourceId;
    return this;
  }

  /**
   * Get sourceId
   * minimum: 1
   * @return sourceId
  */
  @NotNull @Min(1L) 
  @Schema(name = "sourceId", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("sourceId")
  public Long getSourceId() {
    return sourceId;
  }

  public void setSourceId(Long sourceId) {
    this.sourceId = sourceId;
  }

  public CreateInvoiceRequest sequenceId(Long sequenceId) {
    this.sequenceId = sequenceId;
    return this;
  }

  /**
   * Get sequenceId
   * minimum: 1
   * @return sequenceId
  */
  @Min(1L) 
  @Schema(name = "sequenceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("sequenceId")
  public Long getSequenceId() {
    return sequenceId;
  }

  public void setSequenceId(Long sequenceId) {
    this.sequenceId = sequenceId;
  }

  public CreateInvoiceRequest sourceKind(SourceKind sourceKind) {
    this.sourceKind = sourceKind;
    return this;
  }

  /**
   * Get sourceKind
   * @return sourceKind
  */
  @NotNull @Valid 
  @Schema(name = "sourceKind", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("sourceKind")
  public SourceKind getSourceKind() {
    return sourceKind;
  }

  public void setSourceKind(SourceKind sourceKind) {
    this.sourceKind = sourceKind;
  }

  public CreateInvoiceRequest userId(Long userId) {
    this.userId = userId;
    return this;
  }

  /**
   * Get userId
   * minimum: 0
   * @return userId
  */
  @Min(0L) 
  @Schema(name = "userId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("userId")
  public Long getUserId() {
    return userId;
  }

  public void setUserId(Long userId) {
    this.userId = userId;
  }

  public CreateInvoiceRequest recipient(RecipientRequest recipient) {
    this.recipient = recipient;
    return this;
  }

  /**
   * Get recipient
   * @return recipient
  */
  @NotNull @Valid 
  @Schema(name = "recipient", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("recipient")
  public RecipientRequest getRecipient() {
    return recipient;
  }

  public void setRecipient(RecipientRequest recipient) {
    this.recipient = recipient;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CreateInvoiceRequest createInvoiceRequest = (CreateInvoiceRequest) o;
    return Objects.equals(this.transactionIds, createInvoiceRequest.transactionIds) &&
        Objects.equals(this.sourceId, createInvoiceRequest.sourceId) &&
        Objects.equals(this.sequenceId, createInvoiceRequest.sequenceId) &&
        Objects.equals(this.sourceKind, createInvoiceRequest.sourceKind) &&
        Objects.equals(this.userId, createInvoiceRequest.userId) &&
        Objects.equals(this.recipient, createInvoiceRequest.recipient);
  }

  @Override
  public int hashCode() {
    return Objects.hash(transactionIds, sourceId, sequenceId, sourceKind, userId, recipient);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CreateInvoiceRequest {\n");
    sb.append("    transactionIds: ").append(toIndentedString(transactionIds)).append("\n");
    sb.append("    sourceId: ").append(toIndentedString(sourceId)).append("\n");
    sb.append("    sequenceId: ").append(toIndentedString(sequenceId)).append("\n");
    sb.append("    sourceKind: ").append(toIndentedString(sourceKind)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    recipient: ").append(toIndentedString(recipient)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

