package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.RecipientAddress;
import com.cloudbeds.fiscaldocument.controller.model.RecipientContactDetails;
import com.cloudbeds.fiscaldocument.controller.model.RecipientDocument;
import com.cloudbeds.fiscaldocument.controller.model.RecipientTaxInfo;
import com.cloudbeds.fiscaldocument.controller.model.RecipientType;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.HashMap;
import java.util.Map;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * FiscalDocumentRecipient
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class FiscalDocumentRecipient implements Serializable {

  private static final long serialVersionUID = 1L;

  private String id;

  private String firstName;

  private String lastName;

  private String email;

  private RecipientType type;

  private RecipientAddress address;

  private RecipientTaxInfo tax;

  private RecipientContactDetails contactDetails;

  private RecipientDocument document;

  @Valid
  private Map<String, Object> countryData = new HashMap<>();

  public FiscalDocumentRecipient id(String id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  @Size(min = 1) 
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public FiscalDocumentRecipient firstName(String firstName) {
    this.firstName = firstName;
    return this;
  }

  /**
   * Get firstName
   * @return firstName
  */
  
  @Schema(name = "firstName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("firstName")
  public String getFirstName() {
    return firstName;
  }

  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public FiscalDocumentRecipient lastName(String lastName) {
    this.lastName = lastName;
    return this;
  }

  /**
   * Get lastName
   * @return lastName
  */
  
  @Schema(name = "lastName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("lastName")
  public String getLastName() {
    return lastName;
  }

  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public FiscalDocumentRecipient email(String email) {
    this.email = email;
    return this;
  }

  /**
   * Get email
   * @return email
  */
  @jakarta.validation.constraints.Email
  @Schema(name = "email", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("email")
  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public FiscalDocumentRecipient type(RecipientType type) {
    this.type = type;
    return this;
  }

  /**
   * Get type
   * @return type
  */
  @Valid 
  @Schema(name = "type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("type")
  public RecipientType getType() {
    return type;
  }

  public void setType(RecipientType type) {
    this.type = type;
  }

  public FiscalDocumentRecipient address(RecipientAddress address) {
    this.address = address;
    return this;
  }

  /**
   * Get address
   * @return address
  */
  @Valid 
  @Schema(name = "address", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("address")
  public RecipientAddress getAddress() {
    return address;
  }

  public void setAddress(RecipientAddress address) {
    this.address = address;
  }

  public FiscalDocumentRecipient tax(RecipientTaxInfo tax) {
    this.tax = tax;
    return this;
  }

  /**
   * Get tax
   * @return tax
  */
  @Valid 
  @Schema(name = "tax", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("tax")
  public RecipientTaxInfo getTax() {
    return tax;
  }

  public void setTax(RecipientTaxInfo tax) {
    this.tax = tax;
  }

  public FiscalDocumentRecipient contactDetails(RecipientContactDetails contactDetails) {
    this.contactDetails = contactDetails;
    return this;
  }

  /**
   * Get contactDetails
   * @return contactDetails
  */
  @Valid 
  @Schema(name = "contactDetails", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("contactDetails")
  public RecipientContactDetails getContactDetails() {
    return contactDetails;
  }

  public void setContactDetails(RecipientContactDetails contactDetails) {
    this.contactDetails = contactDetails;
  }

  public FiscalDocumentRecipient document(RecipientDocument document) {
    this.document = document;
    return this;
  }

  /**
   * Get document
   * @return document
  */
  @Valid 
  @Schema(name = "document", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("document")
  public RecipientDocument getDocument() {
    return document;
  }

  public void setDocument(RecipientDocument document) {
    this.document = document;
  }

  public FiscalDocumentRecipient countryData(Map<String, Object> countryData) {
    this.countryData = countryData;
    return this;
  }

  public FiscalDocumentRecipient putCountryDataItem(String key, Object countryDataItem) {
    if (this.countryData == null) {
      this.countryData = new HashMap<>();
    }
    this.countryData.put(key, countryDataItem);
    return this;
  }

  /**
   * Arbitrary country-specific fields from guest requirements. 
   * @return countryData
  */
  
  @Schema(name = "countryData", description = "Arbitrary country-specific fields from guest requirements. ", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("countryData")
  public Map<String, Object> getCountryData() {
    return countryData;
  }

  public void setCountryData(Map<String, Object> countryData) {
    this.countryData = countryData;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FiscalDocumentRecipient fiscalDocumentRecipient = (FiscalDocumentRecipient) o;
    return Objects.equals(this.id, fiscalDocumentRecipient.id) &&
        Objects.equals(this.firstName, fiscalDocumentRecipient.firstName) &&
        Objects.equals(this.lastName, fiscalDocumentRecipient.lastName) &&
        Objects.equals(this.email, fiscalDocumentRecipient.email) &&
        Objects.equals(this.type, fiscalDocumentRecipient.type) &&
        Objects.equals(this.address, fiscalDocumentRecipient.address) &&
        Objects.equals(this.tax, fiscalDocumentRecipient.tax) &&
        Objects.equals(this.contactDetails, fiscalDocumentRecipient.contactDetails) &&
        Objects.equals(this.document, fiscalDocumentRecipient.document) &&
        Objects.equals(this.countryData, fiscalDocumentRecipient.countryData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, firstName, lastName, email, type, address, tax, contactDetails, document, countryData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FiscalDocumentRecipient {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    tax: ").append(toIndentedString(tax)).append("\n");
    sb.append("    contactDetails: ").append(toIndentedString(contactDetails)).append("\n");
    sb.append("    document: ").append(toIndentedString(document)).append("\n");
    sb.append("    countryData: ").append(toIndentedString(countryData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

