package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.net.URI;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * GovernmentIntegration
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class GovernmentIntegration implements Serializable {

  private static final long serialVersionUID = 1L;

  private String number;

  private String series;

  private String status;

  private GovernmentIntegrationQr qr;

  private URI url;

  private String officialId;

  private String externalId;

  private String rectifyingInvoiceType;

  public GovernmentIntegration number(String number) {
    this.number = number;
    return this;
  }

  /**
   * Get number
   * @return number
  */
  
  @Schema(name = "number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("number")
  public String getNumber() {
    return number;
  }

  public void setNumber(String number) {
    this.number = number;
  }

  public GovernmentIntegration series(String series) {
    this.series = series;
    return this;
  }

  /**
   * Get series
   * @return series
  */
  
  @Schema(name = "series", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("series")
  public String getSeries() {
    return series;
  }

  public void setSeries(String series) {
    this.series = series;
  }

  public GovernmentIntegration status(String status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
  
  @Schema(name = "status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("status")
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public GovernmentIntegration qr(GovernmentIntegrationQr qr) {
    this.qr = qr;
    return this;
  }

  /**
   * Get qr
   * @return qr
  */
  @Valid 
  @Schema(name = "qr", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("qr")
  public GovernmentIntegrationQr getQr() {
    return qr;
  }

  public void setQr(GovernmentIntegrationQr qr) {
    this.qr = qr;
  }

  public GovernmentIntegration url(URI url) {
    this.url = url;
    return this;
  }

  /**
   * Get url
   * @return url
  */
  @Valid 
  @Schema(name = "url", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("url")
  public URI getUrl() {
    return url;
  }

  public void setUrl(URI url) {
    this.url = url;
  }

  public GovernmentIntegration officialId(String officialId) {
    this.officialId = officialId;
    return this;
  }

  /**
   * Get officialId
   * @return officialId
  */
  
  @Schema(name = "officialId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("officialId")
  public String getOfficialId() {
    return officialId;
  }

  public void setOfficialId(String officialId) {
    this.officialId = officialId;
  }

  public GovernmentIntegration externalId(String externalId) {
    this.externalId = externalId;
    return this;
  }

  /**
   * Get externalId
   * @return externalId
  */
  
  @Schema(name = "externalId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("externalId")
  public String getExternalId() {
    return externalId;
  }

  public void setExternalId(String externalId) {
    this.externalId = externalId;
  }

  public GovernmentIntegration rectifyingInvoiceType(String rectifyingInvoiceType) {
    this.rectifyingInvoiceType = rectifyingInvoiceType;
    return this;
  }

  /**
   * Get rectifyingInvoiceType
   * @return rectifyingInvoiceType
  */
  
  @Schema(name = "rectifyingInvoiceType", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("rectifyingInvoiceType")
  public String getRectifyingInvoiceType() {
    return rectifyingInvoiceType;
  }

  public void setRectifyingInvoiceType(String rectifyingInvoiceType) {
    this.rectifyingInvoiceType = rectifyingInvoiceType;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    GovernmentIntegration governmentIntegration = (GovernmentIntegration) o;
    return Objects.equals(this.number, governmentIntegration.number) &&
        Objects.equals(this.series, governmentIntegration.series) &&
        Objects.equals(this.status, governmentIntegration.status) &&
        Objects.equals(this.qr, governmentIntegration.qr) &&
        Objects.equals(this.url, governmentIntegration.url) &&
        Objects.equals(this.officialId, governmentIntegration.officialId) &&
        Objects.equals(this.externalId, governmentIntegration.externalId) &&
        Objects.equals(this.rectifyingInvoiceType, governmentIntegration.rectifyingInvoiceType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(number, series, status, qr, url, officialId, externalId, rectifyingInvoiceType);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class GovernmentIntegration {\n");
    sb.append("    number: ").append(toIndentedString(number)).append("\n");
    sb.append("    series: ").append(toIndentedString(series)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    qr: ").append(toIndentedString(qr)).append("\n");
    sb.append("    url: ").append(toIndentedString(url)).append("\n");
    sb.append("    officialId: ").append(toIndentedString(officialId)).append("\n");
    sb.append("    externalId: ").append(toIndentedString(externalId)).append("\n");
    sb.append("    rectifyingInvoiceType: ").append(toIndentedString(rectifyingInvoiceType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

