package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.CreationMethod;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Request to create a rectifying invoice. Only available for Spanish properties.  **Important:** The specified invoice must not have been previously rectified. If it has been rectified, you must rectify the most recent invoice in the rectification chain instead. 
 */

@Schema(name = "RectifyInvoiceNoteRequest", description = "Request to create a rectifying invoice. Only available for Spanish properties.  **Important:** The specified invoice must not have been previously rectified. If it has been rectified, you must rectify the most recent invoice in the rectification chain instead. ")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class RectifyInvoiceNoteRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long invoiceId;

  private String reason;

  private Long userId;

  private CreationMethod method;

  @Valid
  private List<Long> transactionIds;

  public RectifyInvoiceNoteRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public RectifyInvoiceNoteRequest(Long invoiceId, CreationMethod method) {
    this.invoiceId = invoiceId;
    this.method = method;
  }

  public RectifyInvoiceNoteRequest invoiceId(Long invoiceId) {
    this.invoiceId = invoiceId;
    return this;
  }

  /**
   * ID of the invoice to be rectified.  **Validation:** This invoice must not have been previously rectified according to Spanish fiscal regulations. 
   * @return invoiceId
  */
  @NotNull 
  @Schema(name = "invoiceId", description = "ID of the invoice to be rectified.  **Validation:** This invoice must not have been previously rectified according to Spanish fiscal regulations. ", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("invoiceId")
  public Long getInvoiceId() {
    return invoiceId;
  }

  public void setInvoiceId(Long invoiceId) {
    this.invoiceId = invoiceId;
  }

  public RectifyInvoiceNoteRequest reason(String reason) {
    this.reason = reason;
    return this;
  }

  /**
   * Reason for rectifying the invoice
   * @return reason
  */
  
  @Schema(name = "reason", description = "Reason for rectifying the invoice", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("reason")
  public String getReason() {
    return reason;
  }

  public void setReason(String reason) {
    this.reason = reason;
  }

  public RectifyInvoiceNoteRequest userId(Long userId) {
    this.userId = userId;
    return this;
  }

  /**
   * ID of the user creating the rectification
   * @return userId
  */
  
  @Schema(name = "userId", description = "ID of the user creating the rectification", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("userId")
  public Long getUserId() {
    return userId;
  }

  public void setUserId(Long userId) {
    this.userId = userId;
  }

  public RectifyInvoiceNoteRequest method(CreationMethod method) {
    this.method = method;
    return this;
  }

  /**
   * Get method
   * @return method
  */
  @NotNull @Valid 
  @Schema(name = "method", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("method")
  public CreationMethod getMethod() {
    return method;
  }

  public void setMethod(CreationMethod method) {
    this.method = method;
  }

  public RectifyInvoiceNoteRequest transactionIds(List<Long> transactionIds) {
    this.transactionIds = transactionIds;
    return this;
  }

  public RectifyInvoiceNoteRequest addTransactionIdsItem(Long transactionIdsItem) {
    if (this.transactionIds == null) {
      this.transactionIds = new ArrayList<>();
    }
    this.transactionIds.add(transactionIdsItem);
    return this;
  }

  /**
   * Get transactionIds
   * @return transactionIds
  */
  
  @Schema(name = "transactionIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("transactionIds")
  public List<Long> getTransactionIds() {
    return transactionIds;
  }

  public void setTransactionIds(List<Long> transactionIds) {
    this.transactionIds = transactionIds;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RectifyInvoiceNoteRequest rectifyInvoiceNoteRequest = (RectifyInvoiceNoteRequest) o;
    return Objects.equals(this.invoiceId, rectifyInvoiceNoteRequest.invoiceId) &&
        Objects.equals(this.reason, rectifyInvoiceNoteRequest.reason) &&
        Objects.equals(this.userId, rectifyInvoiceNoteRequest.userId) &&
        Objects.equals(this.method, rectifyInvoiceNoteRequest.method) &&
        Objects.equals(this.transactionIds, rectifyInvoiceNoteRequest.transactionIds);
  }

  @Override
  public int hashCode() {
    return Objects.hash(invoiceId, reason, userId, method, transactionIds);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RectifyInvoiceNoteRequest {\n");
    sb.append("    invoiceId: ").append(toIndentedString(invoiceId)).append("\n");
    sb.append("    reason: ").append(toIndentedString(reason)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    method: ").append(toIndentedString(method)).append("\n");
    sb.append("    transactionIds: ").append(toIndentedString(transactionIds)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

