package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Status of the fiscal document
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public enum FiscalDocumentStatus {
  
  COMPLETED("COMPLETED"),
  
  VOIDED("VOIDED"),
  
  PAID("PAID"),
  
  PENDING_INTEGRATION("PENDING_INTEGRATION"),
  
  COMPLETED_INTEGRATION("COMPLETED_INTEGRATION"),
  
  FAILED_INTEGRATION("FAILED_INTEGRATION"),
  
  CORRECTION_NEEDED("CORRECTION_NEEDED"),
  
  CANCELED("CANCELED"),
  
  OPEN("OPEN"),
  
  REQUESTED("REQUESTED"),
  
  VOID_REQUESTED("VOID_REQUESTED"),
  
  FAILED("FAILED"),
  
  MANUALLY_RECONCILED("MANUALLY_RECONCILED"),
  
  REJECTED("REJECTED");

  private String value;

  FiscalDocumentStatus(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  @JsonCreator
  public static FiscalDocumentStatus fromValue(String value) {
    for (FiscalDocumentStatus b : FiscalDocumentStatus.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }
}

