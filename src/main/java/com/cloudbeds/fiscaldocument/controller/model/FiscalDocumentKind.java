package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Kind of fiscal document
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public enum FiscalDocumentKind {
  
  INVOICE("INVOICE"),
  
  CREDIT_NOTE("CREDIT_NOTE"),
  
  RECEIPT("RECEIPT"),
  
  RECTIFY_INVOICE("RECTIFY_INVOICE");

  private String value;

  FiscalDocumentKind(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  @JsonCreator
  public static FiscalDocumentKind fromValue(String value) {
    for (FiscalDocumentKind b : FiscalDocumentKind.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }
}

