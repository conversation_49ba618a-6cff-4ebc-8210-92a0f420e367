package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.SourceKind;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * FiscalDocumentTransactionResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class FiscalDocumentTransactionResponse implements Serializable {

  private static final long serialVersionUID = 1L;

  private String id;

  private String propertyId;

  private String sourceId;

  private SourceKind sourceKind;

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private OffsetDateTime transactionDate;

  private String guestName;

  private String description;

  private String internalCode;

  private BigDecimal amount;

  private String folioId;

  public FiscalDocumentTransactionResponse id(String id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public FiscalDocumentTransactionResponse propertyId(String propertyId) {
    this.propertyId = propertyId;
    return this;
  }

  /**
   * Get propertyId
   * @return propertyId
  */
  
  @Schema(name = "propertyId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("propertyId")
  public String getPropertyId() {
    return propertyId;
  }

  public void setPropertyId(String propertyId) {
    this.propertyId = propertyId;
  }

  public FiscalDocumentTransactionResponse sourceId(String sourceId) {
    this.sourceId = sourceId;
    return this;
  }

  /**
   * Get sourceId
   * @return sourceId
  */
  
  @Schema(name = "sourceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("sourceId")
  public String getSourceId() {
    return sourceId;
  }

  public void setSourceId(String sourceId) {
    this.sourceId = sourceId;
  }

  public FiscalDocumentTransactionResponse sourceKind(SourceKind sourceKind) {
    this.sourceKind = sourceKind;
    return this;
  }

  /**
   * Get sourceKind
   * @return sourceKind
  */
  @Valid 
  @Schema(name = "sourceKind", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("sourceKind")
  public SourceKind getSourceKind() {
    return sourceKind;
  }

  public void setSourceKind(SourceKind sourceKind) {
    this.sourceKind = sourceKind;
  }

  public FiscalDocumentTransactionResponse transactionDate(OffsetDateTime transactionDate) {
    this.transactionDate = transactionDate;
    return this;
  }

  /**
   * Get transactionDate
   * @return transactionDate
  */
  @Valid 
  @Schema(name = "transactionDate", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("transactionDate")
  public OffsetDateTime getTransactionDate() {
    return transactionDate;
  }

  public void setTransactionDate(OffsetDateTime transactionDate) {
    this.transactionDate = transactionDate;
  }

  public FiscalDocumentTransactionResponse guestName(String guestName) {
    this.guestName = guestName;
    return this;
  }

  /**
   * Get guestName
   * @return guestName
  */
  
  @Schema(name = "guestName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("guestName")
  public String getGuestName() {
    return guestName;
  }

  public void setGuestName(String guestName) {
    this.guestName = guestName;
  }

  public FiscalDocumentTransactionResponse description(String description) {
    this.description = description;
    return this;
  }

  /**
   * Get description
   * @return description
  */
  
  @Schema(name = "description", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("description")
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public FiscalDocumentTransactionResponse internalCode(String internalCode) {
    this.internalCode = internalCode;
    return this;
  }

  /**
   * Get internalCode
   * @return internalCode
  */
  
  @Schema(name = "internalCode", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("internalCode")
  public String getInternalCode() {
    return internalCode;
  }

  public void setInternalCode(String internalCode) {
    this.internalCode = internalCode;
  }

  public FiscalDocumentTransactionResponse amount(BigDecimal amount) {
    this.amount = amount;
    return this;
  }

  /**
   * Get amount
   * @return amount
  */
  @Valid 
  @Schema(name = "amount", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("amount")
  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public FiscalDocumentTransactionResponse folioId(String folioId) {
    this.folioId = folioId;
    return this;
  }

  /**
   * Get folioId
   * @return folioId
  */
  
  @Schema(name = "folioId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("folioId")
  public String getFolioId() {
    return folioId;
  }

  public void setFolioId(String folioId) {
    this.folioId = folioId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FiscalDocumentTransactionResponse fiscalDocumentTransactionResponse = (FiscalDocumentTransactionResponse) o;
    return Objects.equals(this.id, fiscalDocumentTransactionResponse.id) &&
        Objects.equals(this.propertyId, fiscalDocumentTransactionResponse.propertyId) &&
        Objects.equals(this.sourceId, fiscalDocumentTransactionResponse.sourceId) &&
        Objects.equals(this.sourceKind, fiscalDocumentTransactionResponse.sourceKind) &&
        Objects.equals(this.transactionDate, fiscalDocumentTransactionResponse.transactionDate) &&
        Objects.equals(this.guestName, fiscalDocumentTransactionResponse.guestName) &&
        Objects.equals(this.description, fiscalDocumentTransactionResponse.description) &&
        Objects.equals(this.internalCode, fiscalDocumentTransactionResponse.internalCode) &&
        Objects.equals(this.amount, fiscalDocumentTransactionResponse.amount) &&
        Objects.equals(this.folioId, fiscalDocumentTransactionResponse.folioId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, propertyId, sourceId, sourceKind, transactionDate, guestName, description, internalCode, amount, folioId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FiscalDocumentTransactionResponse {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    propertyId: ").append(toIndentedString(propertyId)).append("\n");
    sb.append("    sourceId: ").append(toIndentedString(sourceId)).append("\n");
    sb.append("    sourceKind: ").append(toIndentedString(sourceKind)).append("\n");
    sb.append("    transactionDate: ").append(toIndentedString(transactionDate)).append("\n");
    sb.append("    guestName: ").append(toIndentedString(guestName)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    internalCode: ").append(toIndentedString(internalCode)).append("\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    folioId: ").append(toIndentedString(folioId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

