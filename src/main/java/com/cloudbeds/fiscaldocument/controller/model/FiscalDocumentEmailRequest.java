package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * FiscalDocumentEmailRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class FiscalDocumentEmailRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  @Valid
  private List<String> emails = new ArrayList<>();

  public FiscalDocumentEmailRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public FiscalDocumentEmailRequest(List<String> emails) {
    this.emails = emails;
  }

  public FiscalDocumentEmailRequest emails(List<String> emails) {
    this.emails = emails;
    return this;
  }

  public FiscalDocumentEmailRequest addEmailsItem(String emailsItem) {
    if (this.emails == null) {
      this.emails = new ArrayList<>();
    }
    this.emails.add(emailsItem);
    return this;
  }

  /**
   * Get emails
   * @return emails
  */
  @NotNull @Size(min = 1, max = 10) 
  @Schema(name = "emails", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("emails")
  public List<String> getEmails() {
    return emails;
  }

  public void setEmails(List<String> emails) {
    this.emails = emails;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FiscalDocumentEmailRequest fiscalDocumentEmailRequest = (FiscalDocumentEmailRequest) o;
    return Objects.equals(this.emails, fiscalDocumentEmailRequest.emails);
  }

  @Override
  public int hashCode() {
    return Objects.hash(emails);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FiscalDocumentEmailRequest {\n");
    sb.append("    emails: ").append(toIndentedString(emails)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

