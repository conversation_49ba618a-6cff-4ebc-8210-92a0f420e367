package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.HashMap;
import java.util.Map;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * ConfigsUpdateRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class ConfigsUpdateRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  private Boolean showDetailedTaxFee;

  private Boolean chargeBreakdown;

  private Boolean useGuestLang;

  private Integer dueDays = null;

  private String lang = null;

  private String prefix = null;

  private String suffix = null;

  private String legalCompanyName = null;

  @Valid
  private Map<String, String> title;

  private Boolean showLegalCompanyName;

  private Boolean includeRoomNumber;

  private Boolean useDocumentNumber;

  private Boolean isCompact;

  private String taxId1 = null;

  private String taxId2 = null;

  private String cpf = null;

  @Valid
  private Map<String, String> customText;

  public ConfigsUpdateRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public ConfigsUpdateRequest(Boolean showDetailedTaxFee, Boolean chargeBreakdown, Boolean useGuestLang, Boolean showLegalCompanyName, Boolean includeRoomNumber, Boolean useDocumentNumber, Boolean isCompact) {
    this.showDetailedTaxFee = showDetailedTaxFee;
    this.chargeBreakdown = chargeBreakdown;
    this.useGuestLang = useGuestLang;
    this.showLegalCompanyName = showLegalCompanyName;
    this.includeRoomNumber = includeRoomNumber;
    this.useDocumentNumber = useDocumentNumber;
    this.isCompact = isCompact;
  }

  public ConfigsUpdateRequest showDetailedTaxFee(Boolean showDetailedTaxFee) {
    this.showDetailedTaxFee = showDetailedTaxFee;
    return this;
  }

  /**
   * Get showDetailedTaxFee
   * @return showDetailedTaxFee
  */
  @NotNull 
  @Schema(name = "showDetailedTaxFee", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("showDetailedTaxFee")
  public Boolean isShowDetailedTaxFee() {
    return showDetailedTaxFee;
  }

  public void setShowDetailedTaxFee(Boolean showDetailedTaxFee) {
    this.showDetailedTaxFee = showDetailedTaxFee;
  }

  public ConfigsUpdateRequest chargeBreakdown(Boolean chargeBreakdown) {
    this.chargeBreakdown = chargeBreakdown;
    return this;
  }

  /**
   * Get chargeBreakdown
   * @return chargeBreakdown
  */
  @NotNull 
  @Schema(name = "chargeBreakdown", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("chargeBreakdown")
  public Boolean isChargeBreakdown() {
    return chargeBreakdown;
  }

  public void setChargeBreakdown(Boolean chargeBreakdown) {
    this.chargeBreakdown = chargeBreakdown;
  }

  public ConfigsUpdateRequest useGuestLang(Boolean useGuestLang) {
    this.useGuestLang = useGuestLang;
    return this;
  }

  /**
   * Get useGuestLang
   * @return useGuestLang
  */
  @NotNull 
  @Schema(name = "useGuestLang", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("useGuestLang")
  public Boolean isUseGuestLang() {
    return useGuestLang;
  }

  public void setUseGuestLang(Boolean useGuestLang) {
    this.useGuestLang = useGuestLang;
  }

  public ConfigsUpdateRequest dueDays(Integer dueDays) {
    this.dueDays = dueDays;
    return this;
  }

  /**
   * Get dueDays
   * @return dueDays
  */
  
  @Schema(name = "dueDays", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("dueDays")
  public Integer getDueDays() {
    return dueDays;
  }

  public void setDueDays(Integer dueDays) {
    this.dueDays = dueDays;
  }

  public ConfigsUpdateRequest lang(String lang) {
    this.lang = lang;
    return this;
  }

  /**
   * Get lang
   * @return lang
  */
  
  @Schema(name = "lang", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("lang")
  public String getLang() {
    return lang;
  }

  public void setLang(String lang) {
    this.lang = lang;
  }

  public ConfigsUpdateRequest prefix(String prefix) {
    this.prefix = prefix;
    return this;
  }

  /**
   * Get prefix
   * @return prefix
  */
  
  @Schema(name = "prefix", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("prefix")
  public String getPrefix() {
    return prefix;
  }

  public void setPrefix(String prefix) {
    this.prefix = prefix;
  }

  public ConfigsUpdateRequest suffix(String suffix) {
    this.suffix = suffix;
    return this;
  }

  /**
   * Get suffix
   * @return suffix
  */
  
  @Schema(name = "suffix", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("suffix")
  public String getSuffix() {
    return suffix;
  }

  public void setSuffix(String suffix) {
    this.suffix = suffix;
  }

  public ConfigsUpdateRequest legalCompanyName(String legalCompanyName) {
    this.legalCompanyName = legalCompanyName;
    return this;
  }

  /**
   * Get legalCompanyName
   * @return legalCompanyName
  */
  
  @Schema(name = "legalCompanyName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("legalCompanyName")
  public String getLegalCompanyName() {
    return legalCompanyName;
  }

  public void setLegalCompanyName(String legalCompanyName) {
    this.legalCompanyName = legalCompanyName;
  }

  public ConfigsUpdateRequest title(Map<String, String> title) {
    this.title = title;
    return this;
  }

  public ConfigsUpdateRequest putTitleItem(String key, String titleItem) {
    if (this.title == null) {
      this.title = new HashMap<>();
    }
    this.title.put(key, titleItem);
    return this;
  }

  /**
   * Get title
   * @return title
  */
  
  @Schema(name = "title", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("title")
  public Map<String, String> getTitle() {
    return title;
  }

  public void setTitle(Map<String, String> title) {
    this.title = title;
  }

  public ConfigsUpdateRequest showLegalCompanyName(Boolean showLegalCompanyName) {
    this.showLegalCompanyName = showLegalCompanyName;
    return this;
  }

  /**
   * Get showLegalCompanyName
   * @return showLegalCompanyName
  */
  @NotNull 
  @Schema(name = "showLegalCompanyName", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("showLegalCompanyName")
  public Boolean isShowLegalCompanyName() {
    return showLegalCompanyName;
  }

  public void setShowLegalCompanyName(Boolean showLegalCompanyName) {
    this.showLegalCompanyName = showLegalCompanyName;
  }

  public ConfigsUpdateRequest includeRoomNumber(Boolean includeRoomNumber) {
    this.includeRoomNumber = includeRoomNumber;
    return this;
  }

  /**
   * Get includeRoomNumber
   * @return includeRoomNumber
  */
  @NotNull 
  @Schema(name = "includeRoomNumber", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("includeRoomNumber")
  public Boolean isIncludeRoomNumber() {
    return includeRoomNumber;
  }

  public void setIncludeRoomNumber(Boolean includeRoomNumber) {
    this.includeRoomNumber = includeRoomNumber;
  }

  public ConfigsUpdateRequest useDocumentNumber(Boolean useDocumentNumber) {
    this.useDocumentNumber = useDocumentNumber;
    return this;
  }

  /**
   * Get useDocumentNumber
   * @return useDocumentNumber
  */
  @NotNull 
  @Schema(name = "useDocumentNumber", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("useDocumentNumber")
  public Boolean isUseDocumentNumber() {
    return useDocumentNumber;
  }

  public void setUseDocumentNumber(Boolean useDocumentNumber) {
    this.useDocumentNumber = useDocumentNumber;
  }

  public ConfigsUpdateRequest isCompact(Boolean isCompact) {
    this.isCompact = isCompact;
    return this;
  }

  /**
   * Get isCompact
   * @return isCompact
  */
  @NotNull 
  @Schema(name = "isCompact", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("isCompact")
  public Boolean isIsCompact() {
    return isCompact;
  }

  public void setIsCompact(Boolean isCompact) {
    this.isCompact = isCompact;
  }

  public ConfigsUpdateRequest taxId1(String taxId1) {
    this.taxId1 = taxId1;
    return this;
  }

  /**
   * Get taxId1
   * @return taxId1
  */
  
  @Schema(name = "taxId1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("taxId1")
  public String getTaxId1() {
    return taxId1;
  }

  public void setTaxId1(String taxId1) {
    this.taxId1 = taxId1;
  }

  public ConfigsUpdateRequest taxId2(String taxId2) {
    this.taxId2 = taxId2;
    return this;
  }

  /**
   * Get taxId2
   * @return taxId2
  */
  
  @Schema(name = "taxId2", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("taxId2")
  public String getTaxId2() {
    return taxId2;
  }

  public void setTaxId2(String taxId2) {
    this.taxId2 = taxId2;
  }

  public ConfigsUpdateRequest cpf(String cpf) {
    this.cpf = cpf;
    return this;
  }

  /**
   * Get cpf
   * @return cpf
  */
  
  @Schema(name = "cpf", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("cpf")
  public String getCpf() {
    return cpf;
  }

  public void setCpf(String cpf) {
    this.cpf = cpf;
  }

  public ConfigsUpdateRequest customText(Map<String, String> customText) {
    this.customText = customText;
    return this;
  }

  public ConfigsUpdateRequest putCustomTextItem(String key, String customTextItem) {
    if (this.customText == null) {
      this.customText = new HashMap<>();
    }
    this.customText.put(key, customTextItem);
    return this;
  }

  /**
   * Get customText
   * @return customText
  */
  
  @Schema(name = "customText", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("customText")
  public Map<String, String> getCustomText() {
    return customText;
  }

  public void setCustomText(Map<String, String> customText) {
    this.customText = customText;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ConfigsUpdateRequest configsUpdateRequest = (ConfigsUpdateRequest) o;
    return Objects.equals(this.showDetailedTaxFee, configsUpdateRequest.showDetailedTaxFee) &&
        Objects.equals(this.chargeBreakdown, configsUpdateRequest.chargeBreakdown) &&
        Objects.equals(this.useGuestLang, configsUpdateRequest.useGuestLang) &&
        Objects.equals(this.dueDays, configsUpdateRequest.dueDays) &&
        Objects.equals(this.lang, configsUpdateRequest.lang) &&
        Objects.equals(this.prefix, configsUpdateRequest.prefix) &&
        Objects.equals(this.suffix, configsUpdateRequest.suffix) &&
        Objects.equals(this.legalCompanyName, configsUpdateRequest.legalCompanyName) &&
        Objects.equals(this.title, configsUpdateRequest.title) &&
        Objects.equals(this.showLegalCompanyName, configsUpdateRequest.showLegalCompanyName) &&
        Objects.equals(this.includeRoomNumber, configsUpdateRequest.includeRoomNumber) &&
        Objects.equals(this.useDocumentNumber, configsUpdateRequest.useDocumentNumber) &&
        Objects.equals(this.isCompact, configsUpdateRequest.isCompact) &&
        Objects.equals(this.taxId1, configsUpdateRequest.taxId1) &&
        Objects.equals(this.taxId2, configsUpdateRequest.taxId2) &&
        Objects.equals(this.cpf, configsUpdateRequest.cpf) &&
        Objects.equals(this.customText, configsUpdateRequest.customText);
  }

  @Override
  public int hashCode() {
    return Objects.hash(showDetailedTaxFee, chargeBreakdown, useGuestLang, dueDays, lang, prefix, suffix, legalCompanyName, title, showLegalCompanyName, includeRoomNumber, useDocumentNumber, isCompact, taxId1, taxId2, cpf, customText);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ConfigsUpdateRequest {\n");
    sb.append("    showDetailedTaxFee: ").append(toIndentedString(showDetailedTaxFee)).append("\n");
    sb.append("    chargeBreakdown: ").append(toIndentedString(chargeBreakdown)).append("\n");
    sb.append("    useGuestLang: ").append(toIndentedString(useGuestLang)).append("\n");
    sb.append("    dueDays: ").append(toIndentedString(dueDays)).append("\n");
    sb.append("    lang: ").append(toIndentedString(lang)).append("\n");
    sb.append("    prefix: ").append(toIndentedString(prefix)).append("\n");
    sb.append("    suffix: ").append(toIndentedString(suffix)).append("\n");
    sb.append("    legalCompanyName: ").append(toIndentedString(legalCompanyName)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    showLegalCompanyName: ").append(toIndentedString(showLegalCompanyName)).append("\n");
    sb.append("    includeRoomNumber: ").append(toIndentedString(includeRoomNumber)).append("\n");
    sb.append("    useDocumentNumber: ").append(toIndentedString(useDocumentNumber)).append("\n");
    sb.append("    isCompact: ").append(toIndentedString(isCompact)).append("\n");
    sb.append("    taxId1: ").append(toIndentedString(taxId1)).append("\n");
    sb.append("    taxId2: ").append(toIndentedString(taxId2)).append("\n");
    sb.append("    cpf: ").append(toIndentedString(cpf)).append("\n");
    sb.append("    customText: ").append(toIndentedString(customText)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

