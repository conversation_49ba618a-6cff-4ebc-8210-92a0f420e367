package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import java.net.URI;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * GovernmentIntegrationQr
 */

@JsonTypeName("GovernmentIntegration_qr")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class GovernmentIntegrationQr implements Serializable {

  private static final long serialVersionUID = 1L;

  private URI url;

  private String string;

  public GovernmentIntegrationQr url(URI url) {
    this.url = url;
    return this;
  }

  /**
   * Get url
   * @return url
  */
  @Valid 
  @Schema(name = "url", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("url")
  public URI getUrl() {
    return url;
  }

  public void setUrl(URI url) {
    this.url = url;
  }

  public GovernmentIntegrationQr string(String string) {
    this.string = string;
    return this;
  }

  /**
   * Get string
   * @return string
  */
  
  @Schema(name = "string", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("string")
  public String getString() {
    return string;
  }

  public void setString(String string) {
    this.string = string;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    GovernmentIntegrationQr governmentIntegrationQr = (GovernmentIntegrationQr) o;
    return Objects.equals(this.url, governmentIntegrationQr.url) &&
        Objects.equals(this.string, governmentIntegrationQr.string);
  }

  @Override
  public int hashCode() {
    return Objects.hash(url, string);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class GovernmentIntegrationQr {\n");
    sb.append("    url: ").append(toIndentedString(url)).append("\n");
    sb.append("    string: ").append(toIndentedString(string)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

