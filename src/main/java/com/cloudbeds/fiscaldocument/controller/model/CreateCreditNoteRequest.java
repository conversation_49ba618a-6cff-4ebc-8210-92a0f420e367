package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.CreationMethod;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * CreateCreditNoteRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class CreateCreditNoteRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long sequenceId = null;

  private Long invoiceId;

  private String reason = null;

  private Long userId = null;

  private CreationMethod method;

  @Valid
  private List<Long> transactionIds;

  public CreateCreditNoteRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public CreateCreditNoteRequest(Long invoiceId, CreationMethod method) {
    this.invoiceId = invoiceId;
    this.method = method;
  }

  public CreateCreditNoteRequest sequenceId(Long sequenceId) {
    this.sequenceId = sequenceId;
    return this;
  }

  /**
   * Get sequenceId
   * minimum: 1
   * @return sequenceId
  */
  @Min(1L) 
  @Schema(name = "sequenceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("sequenceId")
  public Long getSequenceId() {
    return sequenceId;
  }

  public void setSequenceId(Long sequenceId) {
    this.sequenceId = sequenceId;
  }

  public CreateCreditNoteRequest invoiceId(Long invoiceId) {
    this.invoiceId = invoiceId;
    return this;
  }

  /**
   * Get invoiceId
   * minimum: 1
   * @return invoiceId
  */
  @NotNull @Min(1L) 
  @Schema(name = "invoiceId", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("invoiceId")
  public Long getInvoiceId() {
    return invoiceId;
  }

  public void setInvoiceId(Long invoiceId) {
    this.invoiceId = invoiceId;
  }

  public CreateCreditNoteRequest reason(String reason) {
    this.reason = reason;
    return this;
  }

  /**
   * Get reason
   * @return reason
  */
  
  @Schema(name = "reason", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("reason")
  public String getReason() {
    return reason;
  }

  public void setReason(String reason) {
    this.reason = reason;
  }

  public CreateCreditNoteRequest userId(Long userId) {
    this.userId = userId;
    return this;
  }

  /**
   * Get userId
   * minimum: 0
   * @return userId
  */
  @Min(0L) 
  @Schema(name = "userId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("userId")
  public Long getUserId() {
    return userId;
  }

  public void setUserId(Long userId) {
    this.userId = userId;
  }

  public CreateCreditNoteRequest method(CreationMethod method) {
    this.method = method;
    return this;
  }

  /**
   * Get method
   * @return method
  */
  @NotNull @Valid 
  @Schema(name = "method", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("method")
  public CreationMethod getMethod() {
    return method;
  }

  public void setMethod(CreationMethod method) {
    this.method = method;
  }

  public CreateCreditNoteRequest transactionIds(List<Long> transactionIds) {
    this.transactionIds = transactionIds;
    return this;
  }

  public CreateCreditNoteRequest addTransactionIdsItem(Long transactionIdsItem) {
    if (this.transactionIds == null) {
      this.transactionIds = new ArrayList<>();
    }
    this.transactionIds.add(transactionIdsItem);
    return this;
  }

  /**
   * Get transactionIds
   * @return transactionIds
  */
  
  @Schema(name = "transactionIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("transactionIds")
  public List<Long> getTransactionIds() {
    return transactionIds;
  }

  public void setTransactionIds(List<Long> transactionIds) {
    this.transactionIds = transactionIds;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CreateCreditNoteRequest createCreditNoteRequest = (CreateCreditNoteRequest) o;
    return Objects.equals(this.sequenceId, createCreditNoteRequest.sequenceId) &&
        Objects.equals(this.invoiceId, createCreditNoteRequest.invoiceId) &&
        Objects.equals(this.reason, createCreditNoteRequest.reason) &&
        Objects.equals(this.userId, createCreditNoteRequest.userId) &&
        Objects.equals(this.method, createCreditNoteRequest.method) &&
        Objects.equals(this.transactionIds, createCreditNoteRequest.transactionIds);
  }

  @Override
  public int hashCode() {
    return Objects.hash(sequenceId, invoiceId, reason, userId, method, transactionIds);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CreateCreditNoteRequest {\n");
    sb.append("    sequenceId: ").append(toIndentedString(sequenceId)).append("\n");
    sb.append("    invoiceId: ").append(toIndentedString(invoiceId)).append("\n");
    sb.append("    reason: ").append(toIndentedString(reason)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    method: ").append(toIndentedString(method)).append("\n");
    sb.append("    transactionIds: ").append(toIndentedString(transactionIds)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

