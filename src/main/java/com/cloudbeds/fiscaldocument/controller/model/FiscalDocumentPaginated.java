package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentDetailedResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * FiscalDocumentPaginated
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class FiscalDocumentPaginated implements Serializable {

  private static final long serialVersionUID = 1L;

  @Valid
  private List<@Valid FiscalDocumentDetailedResponse> fiscalDocuments;

  private String nextPageToken;

  public FiscalDocumentPaginated fiscalDocuments(List<@Valid FiscalDocumentDetailedResponse> fiscalDocuments) {
    this.fiscalDocuments = fiscalDocuments;
    return this;
  }

  public FiscalDocumentPaginated addFiscalDocumentsItem(FiscalDocumentDetailedResponse fiscalDocumentsItem) {
    if (this.fiscalDocuments == null) {
      this.fiscalDocuments = new ArrayList<>();
    }
    this.fiscalDocuments.add(fiscalDocumentsItem);
    return this;
  }

  /**
   * Get fiscalDocuments
   * @return fiscalDocuments
  */
  @Valid 
  @Schema(name = "fiscalDocuments", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("fiscalDocuments")
  public List<@Valid FiscalDocumentDetailedResponse> getFiscalDocuments() {
    return fiscalDocuments;
  }

  public void setFiscalDocuments(List<@Valid FiscalDocumentDetailedResponse> fiscalDocuments) {
    this.fiscalDocuments = fiscalDocuments;
  }

  public FiscalDocumentPaginated nextPageToken(String nextPageToken) {
    this.nextPageToken = nextPageToken;
    return this;
  }

  /**
   * Token for fetching the next page of results
   * @return nextPageToken
  */
  
  @Schema(name = "nextPageToken", description = "Token for fetching the next page of results", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("nextPageToken")
  public String getNextPageToken() {
    return nextPageToken;
  }

  public void setNextPageToken(String nextPageToken) {
    this.nextPageToken = nextPageToken;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FiscalDocumentPaginated fiscalDocumentPaginated = (FiscalDocumentPaginated) o;
    return Objects.equals(this.fiscalDocuments, fiscalDocumentPaginated.fiscalDocuments) &&
        Objects.equals(this.nextPageToken, fiscalDocumentPaginated.nextPageToken);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fiscalDocuments, nextPageToken);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FiscalDocumentPaginated {\n");
    sb.append("    fiscalDocuments: ").append(toIndentedString(fiscalDocuments)).append("\n");
    sb.append("    nextPageToken: ").append(toIndentedString(nextPageToken)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

