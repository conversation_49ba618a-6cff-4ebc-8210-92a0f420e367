package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus;
import com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * FiscalDocumentPatchRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class FiscalDocumentPatchRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  private FiscalDocumentStatus status;

  private String failReason;

  private GovernmentIntegration governmentIntegration;

  public FiscalDocumentPatchRequest status(FiscalDocumentStatus status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
  @Valid 
  @Schema(name = "status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("status")
  public FiscalDocumentStatus getStatus() {
    return status;
  }

  public void setStatus(FiscalDocumentStatus status) {
    this.status = status;
  }

  public FiscalDocumentPatchRequest failReason(String failReason) {
    this.failReason = failReason;
    return this;
  }

  /**
   * Get failReason
   * @return failReason
  */
  
  @Schema(name = "failReason", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("failReason")
  public String getFailReason() {
    return failReason;
  }

  public void setFailReason(String failReason) {
    this.failReason = failReason;
  }

  public FiscalDocumentPatchRequest governmentIntegration(GovernmentIntegration governmentIntegration) {
    this.governmentIntegration = governmentIntegration;
    return this;
  }

  /**
   * Get governmentIntegration
   * @return governmentIntegration
  */
  @Valid 
  @Schema(name = "governmentIntegration", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("governmentIntegration")
  public GovernmentIntegration getGovernmentIntegration() {
    return governmentIntegration;
  }

  public void setGovernmentIntegration(GovernmentIntegration governmentIntegration) {
    this.governmentIntegration = governmentIntegration;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FiscalDocumentPatchRequest fiscalDocumentPatchRequest = (FiscalDocumentPatchRequest) o;
    return Objects.equals(this.status, fiscalDocumentPatchRequest.status) &&
        Objects.equals(this.failReason, fiscalDocumentPatchRequest.failReason) &&
        Objects.equals(this.governmentIntegration, fiscalDocumentPatchRequest.governmentIntegration);
  }

  @Override
  public int hashCode() {
    return Objects.hash(status, failReason, governmentIntegration);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FiscalDocumentPatchRequest {\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    failReason: ").append(toIndentedString(failReason)).append("\n");
    sb.append("    governmentIntegration: ").append(toIndentedString(governmentIntegration)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

