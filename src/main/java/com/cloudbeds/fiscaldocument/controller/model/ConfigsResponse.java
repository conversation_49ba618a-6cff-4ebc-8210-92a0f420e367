package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.HashMap;
import java.util.Map;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * ConfigsResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class ConfigsResponse implements Serializable {

  private static final long serialVersionUID = 1L;

  private String propertyId;

  private FiscalDocumentKind documentKind;

  private Boolean showDetailedTaxFee;

  private Boolean chargeBreakdown;

  private Boolean useGuestLang;

  private Integer dueDays;

  private String lang;

  private String prefix;

  private String suffix;

  private String legalCompanyName;

  @Valid
  private Map<String, String> title = new HashMap<>();

  private Boolean showLegalCompanyName;

  private Boolean includeRoomNumber;

  private Boolean useDocumentNumber;

  private Boolean isCompact;

  private String taxId1;

  private String taxId2;

  private String cpf;

  @Valid
  private Map<String, String> customText = new HashMap<>();

  public ConfigsResponse propertyId(String propertyId) {
    this.propertyId = propertyId;
    return this;
  }

  /**
   * Get propertyId
   * @return propertyId
  */
  
  @Schema(name = "propertyId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("propertyId")
  public String getPropertyId() {
    return propertyId;
  }

  public void setPropertyId(String propertyId) {
    this.propertyId = propertyId;
  }

  public ConfigsResponse documentKind(FiscalDocumentKind documentKind) {
    this.documentKind = documentKind;
    return this;
  }

  /**
   * Get documentKind
   * @return documentKind
  */
  @Valid 
  @Schema(name = "documentKind", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("documentKind")
  public FiscalDocumentKind getDocumentKind() {
    return documentKind;
  }

  public void setDocumentKind(FiscalDocumentKind documentKind) {
    this.documentKind = documentKind;
  }

  public ConfigsResponse showDetailedTaxFee(Boolean showDetailedTaxFee) {
    this.showDetailedTaxFee = showDetailedTaxFee;
    return this;
  }

  /**
   * Get showDetailedTaxFee
   * @return showDetailedTaxFee
  */
  
  @Schema(name = "showDetailedTaxFee", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("showDetailedTaxFee")
  public Boolean isShowDetailedTaxFee() {
    return showDetailedTaxFee;
  }

  public void setShowDetailedTaxFee(Boolean showDetailedTaxFee) {
    this.showDetailedTaxFee = showDetailedTaxFee;
  }

  public ConfigsResponse chargeBreakdown(Boolean chargeBreakdown) {
    this.chargeBreakdown = chargeBreakdown;
    return this;
  }

  /**
   * Get chargeBreakdown
   * @return chargeBreakdown
  */
  
  @Schema(name = "chargeBreakdown", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("chargeBreakdown")
  public Boolean isChargeBreakdown() {
    return chargeBreakdown;
  }

  public void setChargeBreakdown(Boolean chargeBreakdown) {
    this.chargeBreakdown = chargeBreakdown;
  }

  public ConfigsResponse useGuestLang(Boolean useGuestLang) {
    this.useGuestLang = useGuestLang;
    return this;
  }

  /**
   * Get useGuestLang
   * @return useGuestLang
  */
  
  @Schema(name = "useGuestLang", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("useGuestLang")
  public Boolean isUseGuestLang() {
    return useGuestLang;
  }

  public void setUseGuestLang(Boolean useGuestLang) {
    this.useGuestLang = useGuestLang;
  }

  public ConfigsResponse dueDays(Integer dueDays) {
    this.dueDays = dueDays;
    return this;
  }

  /**
   * Get dueDays
   * @return dueDays
  */
  
  @Schema(name = "dueDays", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("dueDays")
  public Integer getDueDays() {
    return dueDays;
  }

  public void setDueDays(Integer dueDays) {
    this.dueDays = dueDays;
  }

  public ConfigsResponse lang(String lang) {
    this.lang = lang;
    return this;
  }

  /**
   * Get lang
   * @return lang
  */
  
  @Schema(name = "lang", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("lang")
  public String getLang() {
    return lang;
  }

  public void setLang(String lang) {
    this.lang = lang;
  }

  public ConfigsResponse prefix(String prefix) {
    this.prefix = prefix;
    return this;
  }

  /**
   * Get prefix
   * @return prefix
  */
  
  @Schema(name = "prefix", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("prefix")
  public String getPrefix() {
    return prefix;
  }

  public void setPrefix(String prefix) {
    this.prefix = prefix;
  }

  public ConfigsResponse suffix(String suffix) {
    this.suffix = suffix;
    return this;
  }

  /**
   * Get suffix
   * @return suffix
  */
  
  @Schema(name = "suffix", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("suffix")
  public String getSuffix() {
    return suffix;
  }

  public void setSuffix(String suffix) {
    this.suffix = suffix;
  }

  public ConfigsResponse legalCompanyName(String legalCompanyName) {
    this.legalCompanyName = legalCompanyName;
    return this;
  }

  /**
   * Get legalCompanyName
   * @return legalCompanyName
  */
  
  @Schema(name = "legalCompanyName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("legalCompanyName")
  public String getLegalCompanyName() {
    return legalCompanyName;
  }

  public void setLegalCompanyName(String legalCompanyName) {
    this.legalCompanyName = legalCompanyName;
  }

  public ConfigsResponse title(Map<String, String> title) {
    this.title = title;
    return this;
  }

  public ConfigsResponse putTitleItem(String key, String titleItem) {
    if (this.title == null) {
      this.title = new HashMap<>();
    }
    this.title.put(key, titleItem);
    return this;
  }

  /**
   * Get title
   * @return title
  */
  
  @Schema(name = "title", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("title")
  public Map<String, String> getTitle() {
    return title;
  }

  public void setTitle(Map<String, String> title) {
    this.title = title;
  }

  public ConfigsResponse showLegalCompanyName(Boolean showLegalCompanyName) {
    this.showLegalCompanyName = showLegalCompanyName;
    return this;
  }

  /**
   * Get showLegalCompanyName
   * @return showLegalCompanyName
  */
  
  @Schema(name = "showLegalCompanyName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("showLegalCompanyName")
  public Boolean isShowLegalCompanyName() {
    return showLegalCompanyName;
  }

  public void setShowLegalCompanyName(Boolean showLegalCompanyName) {
    this.showLegalCompanyName = showLegalCompanyName;
  }

  public ConfigsResponse includeRoomNumber(Boolean includeRoomNumber) {
    this.includeRoomNumber = includeRoomNumber;
    return this;
  }

  /**
   * Get includeRoomNumber
   * @return includeRoomNumber
  */
  
  @Schema(name = "includeRoomNumber", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("includeRoomNumber")
  public Boolean isIncludeRoomNumber() {
    return includeRoomNumber;
  }

  public void setIncludeRoomNumber(Boolean includeRoomNumber) {
    this.includeRoomNumber = includeRoomNumber;
  }

  public ConfigsResponse useDocumentNumber(Boolean useDocumentNumber) {
    this.useDocumentNumber = useDocumentNumber;
    return this;
  }

  /**
   * Get useDocumentNumber
   * @return useDocumentNumber
  */
  
  @Schema(name = "useDocumentNumber", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("useDocumentNumber")
  public Boolean isUseDocumentNumber() {
    return useDocumentNumber;
  }

  public void setUseDocumentNumber(Boolean useDocumentNumber) {
    this.useDocumentNumber = useDocumentNumber;
  }

  public ConfigsResponse isCompact(Boolean isCompact) {
    this.isCompact = isCompact;
    return this;
  }

  /**
   * Get isCompact
   * @return isCompact
  */
  
  @Schema(name = "isCompact", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("isCompact")
  public Boolean isIsCompact() {
    return isCompact;
  }

  public void setIsCompact(Boolean isCompact) {
    this.isCompact = isCompact;
  }

  public ConfigsResponse taxId1(String taxId1) {
    this.taxId1 = taxId1;
    return this;
  }

  /**
   * Get taxId1
   * @return taxId1
  */
  
  @Schema(name = "taxId1", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("taxId1")
  public String getTaxId1() {
    return taxId1;
  }

  public void setTaxId1(String taxId1) {
    this.taxId1 = taxId1;
  }

  public ConfigsResponse taxId2(String taxId2) {
    this.taxId2 = taxId2;
    return this;
  }

  /**
   * Get taxId2
   * @return taxId2
  */
  
  @Schema(name = "taxId2", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("taxId2")
  public String getTaxId2() {
    return taxId2;
  }

  public void setTaxId2(String taxId2) {
    this.taxId2 = taxId2;
  }

  public ConfigsResponse cpf(String cpf) {
    this.cpf = cpf;
    return this;
  }

  /**
   * Get cpf
   * @return cpf
  */
  
  @Schema(name = "cpf", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("cpf")
  public String getCpf() {
    return cpf;
  }

  public void setCpf(String cpf) {
    this.cpf = cpf;
  }

  public ConfigsResponse customText(Map<String, String> customText) {
    this.customText = customText;
    return this;
  }

  public ConfigsResponse putCustomTextItem(String key, String customTextItem) {
    if (this.customText == null) {
      this.customText = new HashMap<>();
    }
    this.customText.put(key, customTextItem);
    return this;
  }

  /**
   * Get customText
   * @return customText
  */
  
  @Schema(name = "customText", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("customText")
  public Map<String, String> getCustomText() {
    return customText;
  }

  public void setCustomText(Map<String, String> customText) {
    this.customText = customText;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ConfigsResponse configsResponse = (ConfigsResponse) o;
    return Objects.equals(this.propertyId, configsResponse.propertyId) &&
        Objects.equals(this.documentKind, configsResponse.documentKind) &&
        Objects.equals(this.showDetailedTaxFee, configsResponse.showDetailedTaxFee) &&
        Objects.equals(this.chargeBreakdown, configsResponse.chargeBreakdown) &&
        Objects.equals(this.useGuestLang, configsResponse.useGuestLang) &&
        Objects.equals(this.dueDays, configsResponse.dueDays) &&
        Objects.equals(this.lang, configsResponse.lang) &&
        Objects.equals(this.prefix, configsResponse.prefix) &&
        Objects.equals(this.suffix, configsResponse.suffix) &&
        Objects.equals(this.legalCompanyName, configsResponse.legalCompanyName) &&
        Objects.equals(this.title, configsResponse.title) &&
        Objects.equals(this.showLegalCompanyName, configsResponse.showLegalCompanyName) &&
        Objects.equals(this.includeRoomNumber, configsResponse.includeRoomNumber) &&
        Objects.equals(this.useDocumentNumber, configsResponse.useDocumentNumber) &&
        Objects.equals(this.isCompact, configsResponse.isCompact) &&
        Objects.equals(this.taxId1, configsResponse.taxId1) &&
        Objects.equals(this.taxId2, configsResponse.taxId2) &&
        Objects.equals(this.cpf, configsResponse.cpf) &&
        Objects.equals(this.customText, configsResponse.customText);
  }

  @Override
  public int hashCode() {
    return Objects.hash(propertyId, documentKind, showDetailedTaxFee, chargeBreakdown, useGuestLang, dueDays, lang, prefix, suffix, legalCompanyName, title, showLegalCompanyName, includeRoomNumber, useDocumentNumber, isCompact, taxId1, taxId2, cpf, customText);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ConfigsResponse {\n");
    sb.append("    propertyId: ").append(toIndentedString(propertyId)).append("\n");
    sb.append("    documentKind: ").append(toIndentedString(documentKind)).append("\n");
    sb.append("    showDetailedTaxFee: ").append(toIndentedString(showDetailedTaxFee)).append("\n");
    sb.append("    chargeBreakdown: ").append(toIndentedString(chargeBreakdown)).append("\n");
    sb.append("    useGuestLang: ").append(toIndentedString(useGuestLang)).append("\n");
    sb.append("    dueDays: ").append(toIndentedString(dueDays)).append("\n");
    sb.append("    lang: ").append(toIndentedString(lang)).append("\n");
    sb.append("    prefix: ").append(toIndentedString(prefix)).append("\n");
    sb.append("    suffix: ").append(toIndentedString(suffix)).append("\n");
    sb.append("    legalCompanyName: ").append(toIndentedString(legalCompanyName)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    showLegalCompanyName: ").append(toIndentedString(showLegalCompanyName)).append("\n");
    sb.append("    includeRoomNumber: ").append(toIndentedString(includeRoomNumber)).append("\n");
    sb.append("    useDocumentNumber: ").append(toIndentedString(useDocumentNumber)).append("\n");
    sb.append("    isCompact: ").append(toIndentedString(isCompact)).append("\n");
    sb.append("    taxId1: ").append(toIndentedString(taxId1)).append("\n");
    sb.append("    taxId2: ").append(toIndentedString(taxId2)).append("\n");
    sb.append("    cpf: ").append(toIndentedString(cpf)).append("\n");
    sb.append("    customText: ").append(toIndentedString(customText)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

