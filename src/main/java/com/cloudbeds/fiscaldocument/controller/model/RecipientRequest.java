package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * RecipientRequest
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class RecipientRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * Type of the recipient.
   */
  public enum TypeEnum {
    GUEST("GUEST"),
    
    CONTACT("CONTACT"),
    
    GROUP("GROUP"),
    
    COMPANY("COMPANY");

    private String value;

    TypeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static TypeEnum fromValue(String value) {
      for (TypeEnum b : TypeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  private TypeEnum type;

  private Long id;

  public RecipientRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public RecipientRequest(TypeEnum type, Long id) {
    this.type = type;
    this.id = id;
  }

  public RecipientRequest type(TypeEnum type) {
    this.type = type;
    return this;
  }

  /**
   * Type of the recipient.
   * @return type
  */
  @NotNull 
  @Schema(name = "type", description = "Type of the recipient.", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("type")
  public TypeEnum getType() {
    return type;
  }

  public void setType(TypeEnum type) {
    this.type = type;
  }

  public RecipientRequest id(Long id) {
    this.id = id;
    return this;
  }

  /**
   * ID of the recipient, references guestId, contactId, groupId, etc. depending on type.
   * minimum: 1
   * @return id
  */
  @NotNull @Min(1L) 
  @Schema(name = "id", description = "ID of the recipient, references guestId, contactId, groupId, etc. depending on type.", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("id")
  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RecipientRequest recipientRequest = (RecipientRequest) o;
    return Objects.equals(this.type, recipientRequest.type) &&
        Objects.equals(this.id, recipientRequest.id);
  }

  @Override
  public int hashCode() {
    return Objects.hash(type, id);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RecipientRequest {\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

