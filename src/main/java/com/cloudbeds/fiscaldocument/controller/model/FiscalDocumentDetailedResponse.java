package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.Action;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus;
import com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration;
import com.cloudbeds.fiscaldocument.controller.model.LatestLinkedDocument;
import com.cloudbeds.fiscaldocument.controller.model.RecipientDetails;
import com.cloudbeds.fiscaldocument.controller.model.SourceKind;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * FiscalDocumentDetailedResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class FiscalDocumentDetailedResponse implements Serializable {

  private static final long serialVersionUID = 1L;

  private String id;

  private String number;

  private String propertyId;

  private String userId;

  private String userFullName;

  private String sourceId;

  private SourceKind sourceKind;

  private FiscalDocumentKind kind;

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
  private LocalDate invoiceDate;

  private String fileName;

  private BigDecimal amount;

  private BigDecimal balance;

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
  private LocalDate dueDate;

  @Valid
  private List<@Valid RecipientDetails> recipients;

  private FiscalDocumentStatus status;

  private String origin;

  private String externalId;

  private String failReason;

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private OffsetDateTime createdAt;

  private String parentId;

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private OffsetDateTime updatedAt;

  private GovernmentIntegration governmentIntegration;

  private LatestLinkedDocument latestLinkedDocument;

  @Valid
  private List<@Valid Action> actions;

  public FiscalDocumentDetailedResponse id(String id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public FiscalDocumentDetailedResponse number(String number) {
    this.number = number;
    return this;
  }

  /**
   * Get number
   * @return number
  */
  
  @Schema(name = "number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("number")
  public String getNumber() {
    return number;
  }

  public void setNumber(String number) {
    this.number = number;
  }

  public FiscalDocumentDetailedResponse propertyId(String propertyId) {
    this.propertyId = propertyId;
    return this;
  }

  /**
   * Get propertyId
   * @return propertyId
  */
  
  @Schema(name = "propertyId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("propertyId")
  public String getPropertyId() {
    return propertyId;
  }

  public void setPropertyId(String propertyId) {
    this.propertyId = propertyId;
  }

  public FiscalDocumentDetailedResponse userId(String userId) {
    this.userId = userId;
    return this;
  }

  /**
   * Get userId
   * @return userId
  */
  
  @Schema(name = "userId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("userId")
  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public FiscalDocumentDetailedResponse userFullName(String userFullName) {
    this.userFullName = userFullName;
    return this;
  }

  /**
   * Get userFullName
   * @return userFullName
  */
  
  @Schema(name = "userFullName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("userFullName")
  public String getUserFullName() {
    return userFullName;
  }

  public void setUserFullName(String userFullName) {
    this.userFullName = userFullName;
  }

  public FiscalDocumentDetailedResponse sourceId(String sourceId) {
    this.sourceId = sourceId;
    return this;
  }

  /**
   * Get sourceId
   * @return sourceId
  */
  
  @Schema(name = "sourceId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("sourceId")
  public String getSourceId() {
    return sourceId;
  }

  public void setSourceId(String sourceId) {
    this.sourceId = sourceId;
  }

  public FiscalDocumentDetailedResponse sourceKind(SourceKind sourceKind) {
    this.sourceKind = sourceKind;
    return this;
  }

  /**
   * Get sourceKind
   * @return sourceKind
  */
  @Valid 
  @Schema(name = "sourceKind", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("sourceKind")
  public SourceKind getSourceKind() {
    return sourceKind;
  }

  public void setSourceKind(SourceKind sourceKind) {
    this.sourceKind = sourceKind;
  }

  public FiscalDocumentDetailedResponse kind(FiscalDocumentKind kind) {
    this.kind = kind;
    return this;
  }

  /**
   * Get kind
   * @return kind
  */
  @Valid 
  @Schema(name = "kind", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("kind")
  public FiscalDocumentKind getKind() {
    return kind;
  }

  public void setKind(FiscalDocumentKind kind) {
    this.kind = kind;
  }

  public FiscalDocumentDetailedResponse invoiceDate(LocalDate invoiceDate) {
    this.invoiceDate = invoiceDate;
    return this;
  }

  /**
   * Get invoiceDate
   * @return invoiceDate
  */
  @Valid 
  @Schema(name = "invoiceDate", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("invoiceDate")
  public LocalDate getInvoiceDate() {
    return invoiceDate;
  }

  public void setInvoiceDate(LocalDate invoiceDate) {
    this.invoiceDate = invoiceDate;
  }

  public FiscalDocumentDetailedResponse fileName(String fileName) {
    this.fileName = fileName;
    return this;
  }

  /**
   * Get fileName
   * @return fileName
  */
  
  @Schema(name = "fileName", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("fileName")
  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public FiscalDocumentDetailedResponse amount(BigDecimal amount) {
    this.amount = amount;
    return this;
  }

  /**
   * Get amount
   * @return amount
  */
  @Valid 
  @Schema(name = "amount", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("amount")
  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public FiscalDocumentDetailedResponse balance(BigDecimal balance) {
    this.balance = balance;
    return this;
  }

  /**
   * Get balance
   * @return balance
  */
  @Valid 
  @Schema(name = "balance", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("balance")
  public BigDecimal getBalance() {
    return balance;
  }

  public void setBalance(BigDecimal balance) {
    this.balance = balance;
  }

  public FiscalDocumentDetailedResponse dueDate(LocalDate dueDate) {
    this.dueDate = dueDate;
    return this;
  }

  /**
   * Get dueDate
   * @return dueDate
  */
  @Valid 
  @Schema(name = "dueDate", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("dueDate")
  public LocalDate getDueDate() {
    return dueDate;
  }

  public void setDueDate(LocalDate dueDate) {
    this.dueDate = dueDate;
  }

  public FiscalDocumentDetailedResponse recipients(List<@Valid RecipientDetails> recipients) {
    this.recipients = recipients;
    return this;
  }

  public FiscalDocumentDetailedResponse addRecipientsItem(RecipientDetails recipientsItem) {
    if (this.recipients == null) {
      this.recipients = new ArrayList<>();
    }
    this.recipients.add(recipientsItem);
    return this;
  }

  /**
   * Get recipients
   * @return recipients
  */
  @Valid 
  @Schema(name = "recipients", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("recipients")
  public List<@Valid RecipientDetails> getRecipients() {
    return recipients;
  }

  public void setRecipients(List<@Valid RecipientDetails> recipients) {
    this.recipients = recipients;
  }

  public FiscalDocumentDetailedResponse status(FiscalDocumentStatus status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
  @Valid 
  @Schema(name = "status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("status")
  public FiscalDocumentStatus getStatus() {
    return status;
  }

  public void setStatus(FiscalDocumentStatus status) {
    this.status = status;
  }

  public FiscalDocumentDetailedResponse origin(String origin) {
    this.origin = origin;
    return this;
  }

  /**
   * Get origin
   * @return origin
  */
  
  @Schema(name = "origin", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("origin")
  public String getOrigin() {
    return origin;
  }

  public void setOrigin(String origin) {
    this.origin = origin;
  }

  public FiscalDocumentDetailedResponse externalId(String externalId) {
    this.externalId = externalId;
    return this;
  }

  /**
   * Get externalId
   * @return externalId
  */
  
  @Schema(name = "externalId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("externalId")
  public String getExternalId() {
    return externalId;
  }

  public void setExternalId(String externalId) {
    this.externalId = externalId;
  }

  public FiscalDocumentDetailedResponse failReason(String failReason) {
    this.failReason = failReason;
    return this;
  }

  /**
   * Get failReason
   * @return failReason
  */
  
  @Schema(name = "failReason", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("failReason")
  public String getFailReason() {
    return failReason;
  }

  public void setFailReason(String failReason) {
    this.failReason = failReason;
  }

  public FiscalDocumentDetailedResponse createdAt(OffsetDateTime createdAt) {
    this.createdAt = createdAt;
    return this;
  }

  /**
   * Get createdAt
   * @return createdAt
  */
  @Valid 
  @Schema(name = "createdAt", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("createdAt")
  public OffsetDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(OffsetDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public FiscalDocumentDetailedResponse parentId(String parentId) {
    this.parentId = parentId;
    return this;
  }

  /**
   * Get parentId
   * @return parentId
  */
  
  @Schema(name = "parentId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("parentId")
  public String getParentId() {
    return parentId;
  }

  public void setParentId(String parentId) {
    this.parentId = parentId;
  }

  public FiscalDocumentDetailedResponse updatedAt(OffsetDateTime updatedAt) {
    this.updatedAt = updatedAt;
    return this;
  }

  /**
   * Get updatedAt
   * @return updatedAt
  */
  @Valid 
  @Schema(name = "updatedAt", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("updatedAt")
  public OffsetDateTime getUpdatedAt() {
    return updatedAt;
  }

  public void setUpdatedAt(OffsetDateTime updatedAt) {
    this.updatedAt = updatedAt;
  }

  public FiscalDocumentDetailedResponse governmentIntegration(GovernmentIntegration governmentIntegration) {
    this.governmentIntegration = governmentIntegration;
    return this;
  }

  /**
   * Get governmentIntegration
   * @return governmentIntegration
  */
  @Valid 
  @Schema(name = "governmentIntegration", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("governmentIntegration")
  public GovernmentIntegration getGovernmentIntegration() {
    return governmentIntegration;
  }

  public void setGovernmentIntegration(GovernmentIntegration governmentIntegration) {
    this.governmentIntegration = governmentIntegration;
  }

  public FiscalDocumentDetailedResponse latestLinkedDocument(LatestLinkedDocument latestLinkedDocument) {
    this.latestLinkedDocument = latestLinkedDocument;
    return this;
  }

  /**
   * Get latestLinkedDocument
   * @return latestLinkedDocument
  */
  @Valid 
  @Schema(name = "latestLinkedDocument", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("latestLinkedDocument")
  public LatestLinkedDocument getLatestLinkedDocument() {
    return latestLinkedDocument;
  }

  public void setLatestLinkedDocument(LatestLinkedDocument latestLinkedDocument) {
    this.latestLinkedDocument = latestLinkedDocument;
  }

  public FiscalDocumentDetailedResponse actions(List<@Valid Action> actions) {
    this.actions = actions;
    return this;
  }

  public FiscalDocumentDetailedResponse addActionsItem(Action actionsItem) {
    if (this.actions == null) {
      this.actions = new ArrayList<>();
    }
    this.actions.add(actionsItem);
    return this;
  }

  /**
   * Returns the list of actions available for the transaction
   * @return actions
  */
  @Valid 
  @Schema(name = "actions", description = "Returns the list of actions available for the transaction", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("actions")
  public List<@Valid Action> getActions() {
    return actions;
  }

  public void setActions(List<@Valid Action> actions) {
    this.actions = actions;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FiscalDocumentDetailedResponse fiscalDocumentDetailedResponse = (FiscalDocumentDetailedResponse) o;
    return Objects.equals(this.id, fiscalDocumentDetailedResponse.id) &&
        Objects.equals(this.number, fiscalDocumentDetailedResponse.number) &&
        Objects.equals(this.propertyId, fiscalDocumentDetailedResponse.propertyId) &&
        Objects.equals(this.userId, fiscalDocumentDetailedResponse.userId) &&
        Objects.equals(this.userFullName, fiscalDocumentDetailedResponse.userFullName) &&
        Objects.equals(this.sourceId, fiscalDocumentDetailedResponse.sourceId) &&
        Objects.equals(this.sourceKind, fiscalDocumentDetailedResponse.sourceKind) &&
        Objects.equals(this.kind, fiscalDocumentDetailedResponse.kind) &&
        Objects.equals(this.invoiceDate, fiscalDocumentDetailedResponse.invoiceDate) &&
        Objects.equals(this.fileName, fiscalDocumentDetailedResponse.fileName) &&
        Objects.equals(this.amount, fiscalDocumentDetailedResponse.amount) &&
        Objects.equals(this.balance, fiscalDocumentDetailedResponse.balance) &&
        Objects.equals(this.dueDate, fiscalDocumentDetailedResponse.dueDate) &&
        Objects.equals(this.recipients, fiscalDocumentDetailedResponse.recipients) &&
        Objects.equals(this.status, fiscalDocumentDetailedResponse.status) &&
        Objects.equals(this.origin, fiscalDocumentDetailedResponse.origin) &&
        Objects.equals(this.externalId, fiscalDocumentDetailedResponse.externalId) &&
        Objects.equals(this.failReason, fiscalDocumentDetailedResponse.failReason) &&
        Objects.equals(this.createdAt, fiscalDocumentDetailedResponse.createdAt) &&
        Objects.equals(this.parentId, fiscalDocumentDetailedResponse.parentId) &&
        Objects.equals(this.updatedAt, fiscalDocumentDetailedResponse.updatedAt) &&
        Objects.equals(this.governmentIntegration, fiscalDocumentDetailedResponse.governmentIntegration) &&
        Objects.equals(this.latestLinkedDocument, fiscalDocumentDetailedResponse.latestLinkedDocument) &&
        Objects.equals(this.actions, fiscalDocumentDetailedResponse.actions);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, number, propertyId, userId, userFullName, sourceId, sourceKind, kind, invoiceDate, fileName, amount, balance, dueDate, recipients, status, origin, externalId, failReason, createdAt, parentId, updatedAt, governmentIntegration, latestLinkedDocument, actions);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FiscalDocumentDetailedResponse {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    number: ").append(toIndentedString(number)).append("\n");
    sb.append("    propertyId: ").append(toIndentedString(propertyId)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    userFullName: ").append(toIndentedString(userFullName)).append("\n");
    sb.append("    sourceId: ").append(toIndentedString(sourceId)).append("\n");
    sb.append("    sourceKind: ").append(toIndentedString(sourceKind)).append("\n");
    sb.append("    kind: ").append(toIndentedString(kind)).append("\n");
    sb.append("    invoiceDate: ").append(toIndentedString(invoiceDate)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    balance: ").append(toIndentedString(balance)).append("\n");
    sb.append("    dueDate: ").append(toIndentedString(dueDate)).append("\n");
    sb.append("    recipients: ").append(toIndentedString(recipients)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    origin: ").append(toIndentedString(origin)).append("\n");
    sb.append("    externalId: ").append(toIndentedString(externalId)).append("\n");
    sb.append("    failReason: ").append(toIndentedString(failReason)).append("\n");
    sb.append("    createdAt: ").append(toIndentedString(createdAt)).append("\n");
    sb.append("    parentId: ").append(toIndentedString(parentId)).append("\n");
    sb.append("    updatedAt: ").append(toIndentedString(updatedAt)).append("\n");
    sb.append("    governmentIntegration: ").append(toIndentedString(governmentIntegration)).append("\n");
    sb.append("    latestLinkedDocument: ").append(toIndentedString(latestLinkedDocument)).append("\n");
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

