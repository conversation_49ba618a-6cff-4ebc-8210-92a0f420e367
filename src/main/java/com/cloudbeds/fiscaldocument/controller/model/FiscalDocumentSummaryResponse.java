package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus;
import com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * FiscalDocumentSummaryResponse
 */

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class FiscalDocumentSummaryResponse implements Serializable {

  private static final long serialVersionUID = 1L;

  private String id;

  private FiscalDocumentKind kind;

  private FiscalDocumentStatus status;

  private GovernmentIntegration governmentIntegration;

  private String linkedTo;

  public FiscalDocumentSummaryResponse id(String id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
  */
  
  @Schema(name = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public FiscalDocumentSummaryResponse kind(FiscalDocumentKind kind) {
    this.kind = kind;
    return this;
  }

  /**
   * Get kind
   * @return kind
  */
  @Valid 
  @Schema(name = "kind", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("kind")
  public FiscalDocumentKind getKind() {
    return kind;
  }

  public void setKind(FiscalDocumentKind kind) {
    this.kind = kind;
  }

  public FiscalDocumentSummaryResponse status(FiscalDocumentStatus status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
  @Valid 
  @Schema(name = "status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("status")
  public FiscalDocumentStatus getStatus() {
    return status;
  }

  public void setStatus(FiscalDocumentStatus status) {
    this.status = status;
  }

  public FiscalDocumentSummaryResponse governmentIntegration(GovernmentIntegration governmentIntegration) {
    this.governmentIntegration = governmentIntegration;
    return this;
  }

  /**
   * Get governmentIntegration
   * @return governmentIntegration
  */
  @Valid 
  @Schema(name = "governmentIntegration", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("governmentIntegration")
  public GovernmentIntegration getGovernmentIntegration() {
    return governmentIntegration;
  }

  public void setGovernmentIntegration(GovernmentIntegration governmentIntegration) {
    this.governmentIntegration = governmentIntegration;
  }

  public FiscalDocumentSummaryResponse linkedTo(String linkedTo) {
    this.linkedTo = linkedTo;
    return this;
  }

  /**
   * Get linkedTo
   * @return linkedTo
  */
  
  @Schema(name = "linkedTo", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("linkedTo")
  public String getLinkedTo() {
    return linkedTo;
  }

  public void setLinkedTo(String linkedTo) {
    this.linkedTo = linkedTo;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FiscalDocumentSummaryResponse fiscalDocumentSummaryResponse = (FiscalDocumentSummaryResponse) o;
    return Objects.equals(this.id, fiscalDocumentSummaryResponse.id) &&
        Objects.equals(this.kind, fiscalDocumentSummaryResponse.kind) &&
        Objects.equals(this.status, fiscalDocumentSummaryResponse.status) &&
        Objects.equals(this.governmentIntegration, fiscalDocumentSummaryResponse.governmentIntegration) &&
        Objects.equals(this.linkedTo, fiscalDocumentSummaryResponse.linkedTo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, kind, status, governmentIntegration, linkedTo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FiscalDocumentSummaryResponse {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    kind: ").append(toIndentedString(kind)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    governmentIntegration: ").append(toIndentedString(governmentIntegration)).append("\n");
    sb.append("    linkedTo: ").append(toIndentedString(linkedTo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

