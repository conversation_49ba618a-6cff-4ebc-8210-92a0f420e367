package com.cloudbeds.fiscaldocument.controller.model;

import java.net.URI;
import java.util.Objects;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.OffsetDateTime;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Information about the latest document in a rectification chain
 */

@Schema(name = "LatestLinkedDocument", description = "Information about the latest document in a rectification chain")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen")
public class LatestLinkedDocument implements Serializable {

  private static final long serialVersionUID = 1L;

  private String id;

  private String number;

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private OffsetDateTime createdAt;

  private FiscalDocumentKind kind;

  private FiscalDocumentStatus status;

  public LatestLinkedDocument id(String id) {
    this.id = id;
    return this;
  }

  /**
   * ID of the latest linked document
   * @return id
  */
  
  @Schema(name = "id", description = "ID of the latest linked document", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public LatestLinkedDocument number(String number) {
    this.number = number;
    return this;
  }

  /**
   * Number of the latest linked document
   * @return number
  */
  
  @Schema(name = "number", description = "Number of the latest linked document", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("number")
  public String getNumber() {
    return number;
  }

  public void setNumber(String number) {
    this.number = number;
  }

  public LatestLinkedDocument createdAt(OffsetDateTime createdAt) {
    this.createdAt = createdAt;
    return this;
  }

  /**
   * Creation date of the latest linked document
   * @return createdAt
  */
  @Valid 
  @Schema(name = "createdAt", description = "Creation date of the latest linked document", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("createdAt")
  public OffsetDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(OffsetDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LatestLinkedDocument kind(FiscalDocumentKind kind) {
    this.kind = kind;
    return this;
  }

  /**
   * Get kind
   * @return kind
  */
  @Valid 
  @Schema(name = "kind", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("kind")
  public FiscalDocumentKind getKind() {
    return kind;
  }

  public void setKind(FiscalDocumentKind kind) {
    this.kind = kind;
  }

  public LatestLinkedDocument status(FiscalDocumentStatus status) {
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
  */
  @Valid 
  @Schema(name = "status", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("status")
  public FiscalDocumentStatus getStatus() {
    return status;
  }

  public void setStatus(FiscalDocumentStatus status) {
    this.status = status;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LatestLinkedDocument latestLinkedDocument = (LatestLinkedDocument) o;
    return Objects.equals(this.id, latestLinkedDocument.id) &&
        Objects.equals(this.number, latestLinkedDocument.number) &&
        Objects.equals(this.createdAt, latestLinkedDocument.createdAt) &&
        Objects.equals(this.kind, latestLinkedDocument.kind) &&
        Objects.equals(this.status, latestLinkedDocument.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, number, createdAt, kind, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LatestLinkedDocument {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    number: ").append(toIndentedString(number)).append("\n");
    sb.append("    createdAt: ").append(toIndentedString(createdAt)).append("\n");
    sb.append("    kind: ").append(toIndentedString(kind)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

