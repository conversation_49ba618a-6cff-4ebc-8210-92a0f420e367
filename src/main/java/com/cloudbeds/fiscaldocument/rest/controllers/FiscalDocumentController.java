package com.cloudbeds.fiscaldocument.rest.controllers;

import com.cloudbeds.fiscaldocument.controller.api.FiscalDocumentsApi;
import com.cloudbeds.fiscaldocument.controller.model.CreateCreditNoteRequest;
import com.cloudbeds.fiscaldocument.controller.model.CreateInvoiceRequest;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentEmailRequest;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPaginated;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPatchRequest;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentSummaryResponse;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentTransactionsPaginated;
import com.cloudbeds.fiscaldocument.controller.model.RectifyInvoiceNoteRequest;
import com.cloudbeds.fiscaldocument.controller.model.SourceKind;
import com.cloudbeds.fiscaldocument.converters.PageRequestConverter;
import com.cloudbeds.fiscaldocument.converters.SourceKindConverter;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.mappers.FiscalDocumentMapper;
import com.cloudbeds.fiscaldocument.services.EmailService;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentFileService;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentRecipientService;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentService;
import com.cloudbeds.fiscaldocument.specifications.FiscalDocumentSpecifications;
import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import com.cloudbeds.fiscaldocument.support.features.aspect.ServiceEnabledCheck;
import com.cloudbeds.fiscaldocument.utils.EnumConverterUtil;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
@ServiceEnabledCheck
public class FiscalDocumentController implements FiscalDocumentsApi {

    private final List<Integer> allowedStatuses = List.of(
        HttpStatus.OK.value(),
        HttpStatus.CREATED.value(),
        HttpStatus.ACCEPTED.value()
    );

    private final EmailService emailService;
    private final FiscalDocumentService fiscalDocumentService;
    private final FiscalDocumentMapper fiscalDocumentMapper;
    private final PageRequestConverter pageRequestConverter;
    private final FiscalDocumentFileService fiscalDocumentFileService;
    private final FiscalDocumentRecipientService fiscalDocumentRecipientService;

    /**
     * Creates a credit note for a given invoice.
     *
     * @param propertyId property ID header
     * @param request the request object containing credit note information
     * @return ResponseEntity with the response object containing the ID of the created credit note
     */
    @Override
    @PreAuthorize("hasPermission('user | partner', 'write:reservation')")
    public ResponseEntity<FiscalDocumentSummaryResponse> createCreditNote(
        Long propertyId,
        CreateCreditNoteRequest request
    ) {
        var fiscalDocument = fiscalDocumentService.createCreditNote(request, propertyId);

        return ResponseEntity.ok(fiscalDocumentMapper.toSummaryResponse(fiscalDocument));
    }

    /**
     * Receives a message and stores it in the database.
     *
     * @param propertyId property id header
     * @param request the message to store
     * @return the stored message
     */
    @Override
    @PreAuthorize("hasPermission('user | partner', 'write:reservation')")
    public ResponseEntity<FiscalDocumentSummaryResponse> createInvoice(
        Long propertyId,
        CreateInvoiceRequest request
    ) {
        var fiscalDocument = fiscalDocumentService.createInvoice(request, propertyId);

        return ResponseEntity.ok(fiscalDocumentMapper.toSummaryResponse(fiscalDocument));
    }

    @Override
    @PreAuthorize("hasPermission('user | partner', 'write:reservation')")
    public ResponseEntity<FiscalDocumentSummaryResponse> createRectifyInvoice(
        Long propertyId,
        RectifyInvoiceNoteRequest request
    ) {
        var fiscalDocument = fiscalDocumentService.createRectifyInvoice(request, propertyId);

        return ResponseEntity.ok(fiscalDocumentMapper.toSummaryResponse(fiscalDocument));
    }

    @Override
    @PreAuthorize("hasPermission('user | partner', 'read:reservation')")
    public ResponseEntity<Resource> downloadFiscalDocument(String id, Long propertyId) {
        try {
            Optional<FiscalDocument> fiscalDocument = fiscalDocumentService.findOneBySpecification(
                FiscalDocumentSpecifications.idAndPropertyId(id, propertyId)
            );

            if (fiscalDocument.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            var document = fiscalDocument.get();

            if (!fiscalDocumentFileService.isDownloadable(document)) {
                log.warn("Fiscal Document {} is not downloadable (missing URL or file path)", document.getId());
                return ResponseEntity.notFound().build();
            }

            return fiscalDocumentFileService.downloadFile(document);

        } catch (FiscalDocumentException fiscalDocumentException) {
            throw fiscalDocumentException;
        } catch (Exception e) {
            log.error("Error generating download file for fiscal document ID {}: {}", id, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @Override
    @PreAuthorize("hasPermission('user | partner', 'write:reservation')")
    public ResponseEntity<Object> emailFiscalDocument(
        String id,
        Long propertyId,
        FiscalDocumentEmailRequest fiscalDocumentEmailRequest
    ) {
        try {
            if (!fiscalDocumentEmailRequest
                .getEmails()
                .stream()
                .allMatch(email -> email.matches("^(.+)@(\\S+)$"))
            ) {
                throw new FiscalDocumentException(
                    ErrorCode.INVALID_REQUEST,
                    "One or more email addresses are invalid"
                );
            }


            Optional<FiscalDocument> fiscalDocument = fiscalDocumentService.findOneBySpecification(
                FiscalDocumentSpecifications.idAndPropertyId(id, propertyId)
            );

            if (fiscalDocument.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            var document = fiscalDocument.get();

            if (!fiscalDocumentFileService.isDownloadable(document)) {
                log.warn("Fiscal Document {} can not be send via email (missing URL or file path)", document.getId());
                return ResponseEntity.notFound().build();
            }

            var response = emailService.send(
                fiscalDocumentEmailRequest.getEmails(),
                document.getNumber(),
                fiscalDocumentFileService.getExtensionFromPath(document),
                fiscalDocumentFileService.getContent(document)
            );

            if (!allowedStatuses.contains(response)) {
                log.error("Error sending email with status code: {}", response);
                return ResponseEntity.internalServerError().build();
            }

            return ResponseEntity.ok().build();

        } catch (FiscalDocumentException fiscalDocumentException) {
            throw fiscalDocumentException;
        } catch (Exception e) {
            log.error("Error sending email: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @Override
    public ResponseEntity<List<FiscalDocumentRecipient>> getFiscalDocumentRecipientsById(String id, Long propertyId) {
        return ResponseEntity.ok(fiscalDocumentRecipientService.findRecipientsByFiscalDocumentId(Long.parseLong(id)));
    }

    @Override
    @PreAuthorize("hasPermission('user | partner', 'read:reservation')")
    public ResponseEntity<FiscalDocumentTransactionsPaginated> getFiscalDocumentTransactions(
        Long propertyId,
        FiscalDocumentKind documentKind,
        Long sourceId,
        SourceKind sourceKind,
        String pageToken,
        Integer limit
    ) {
        return ResponseEntity.ok(fiscalDocumentService.getSourceAvailableTransactions(
            propertyId,
            sourceId,
            SourceKindConverter.toDomain(sourceKind),
            EnumConverterUtil.convert(documentKind),
            pageToken,
            limit
        ));
    }

    @Override
    @PreAuthorize("hasPermission('user | partner', 'read:reservation')")
    public ResponseEntity<FiscalDocumentTransactionsPaginated> getFiscalDocumentTransactionsById(
        String fiscalDocumentId,
        Long propertyId,
        String pageToken,
        Boolean includeLinkedDocumentTransactions,
        Integer limit
    ) {

        return ResponseEntity.ok(fiscalDocumentService.getFiscalDocumentTransactions(
            propertyId,
            Long.valueOf(fiscalDocumentId),
            includeLinkedDocumentTransactions,
            pageToken,
            limit
        ));
    }


    @Override
    @PreAuthorize("hasPermission('user | partner', 'read:reservation')")
    public ResponseEntity<FiscalDocumentPaginated> getFiscalDocuments(
        Long propertyId,
        String pageToken,
        String sort,
        Integer limit,
        List<String> ids,
        List<String> sourceIds,
        List<String> sourceIdentifiers,
        SourceKind sourceKind,
        String numberContains,
        List<FiscalDocumentStatus> statuses,
        List<FiscalDocumentKind> kinds
    ) {
        try {
            if (sourceIds != null && !sourceIds.isEmpty()
                && sourceIdentifiers != null && !sourceIdentifiers.isEmpty()) {
                throw new FiscalDocumentException(
                    ErrorCode.INVALID_REQUEST,
                    "Source Id and Source Identifier can not be filtered together. Please choose 1"
                );
            }

            Specification<FiscalDocument> specification = FiscalDocumentSpecifications.fromFilters(
                propertyId, ids, sourceIds, sourceIdentifiers, SourceKindConverter.toDomain(sourceKind),
                statuses, kinds, numberContains
            );

            var response = fiscalDocumentService.getFiscalDocumentDetailedPaginated(
                specification,
                pageRequestConverter.createPageRequest(
                    limit,
                    pageToken,
                    sort
                )
            );

            return ResponseEntity.ok(response);

        } catch (FiscalDocumentException fiscalDocumentException) {
            throw fiscalDocumentException;
        } catch (Exception e) {
            log.error("Error fetching fiscal documents: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @Override
    @PreAuthorize("hasPermission('user | partner', 'write:reservation')")
    public ResponseEntity<FiscalDocumentSummaryResponse> putFiscalDocument(
        Long id,
        Long propertyId,
        FiscalDocumentPatchRequest request
    ) {
        return fiscalDocumentService.putFiscalDocument(id, propertyId, request)
            .map(fiscalDocumentMapper::toSummaryResponse)
            .map(ResponseEntity::ok)
            .orElseGet(() -> ResponseEntity.notFound().build());
    }
}
