package com.cloudbeds.fiscaldocument.rest.controllers;

import com.cloudbeds.fiscaldocument.controller.api.ConfigsApi;
import com.cloudbeds.fiscaldocument.controller.model.ConfigsResponse;
import com.cloudbeds.fiscaldocument.controller.model.ConfigsUpdateRequest;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.converters.FiscalDocumentSettingsToRestConverter;
import com.cloudbeds.fiscaldocument.services.DocumentConfigService;
import com.cloudbeds.fiscaldocument.support.features.aspect.ServiceEnabledCheck;
import com.cloudbeds.fiscaldocument.utils.EnumConverterUtil;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
@ServiceEnabledCheck
public class FiscalDocumentConfigsController implements ConfigsApi {

    private final DocumentConfigService documentConfigService;
    private final FiscalDocumentSettingsToRestConverter fiscalDocumentSettingsToRestConverter;

    @Override
    @PreAuthorize("hasPermission('user | partner', 'read:reservation')")
    public ResponseEntity<List<ConfigsResponse>> getConfigs(Long propertyId) {
        var configs = documentConfigService.getOrCreateAllConfigs(List.of(propertyId));

        return ResponseEntity.ok(fiscalDocumentSettingsToRestConverter.convert(configs));
    }

    @Override
    @PreAuthorize("hasPermission('user | partner', 'write:reservation')")
    public ResponseEntity<ConfigsResponse> updateConfigs(
        Long propertyId,
        FiscalDocumentKind documentKind,
        ConfigsUpdateRequest updateRequest
    ) {
        var config = documentConfigService.updateDocumentConfig(
            propertyId,
            updateRequest,
            EnumConverterUtil.convert(documentKind)
        );
        return ResponseEntity.ok(fiscalDocumentSettingsToRestConverter.convert(config));
    }
}
