package com.cloudbeds.fiscaldocument.listeners;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventType;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import com.cloudbeds.fiscaldocument.entity.Recipient;
import com.cloudbeds.fiscaldocument.models.InvoiceTransactionCdcModel;
import com.cloudbeds.fiscaldocument.models.InvoiceTransactionModel;
import com.cloudbeds.fiscaldocument.producers.FiscalDocumentEventProducer;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentRecipientService;
import com.cloudbeds.fiscaldocument.support.utils.GenericRecordHelper;
import com.cloudbeds.fiscaldocument.utils.Currency;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class InvoiceListener {

    private final FiscalDocumentEventProducer fiscalDocumentEventProducer;
    private final FiscalDocumentRecipientService fiscalDocumentRecipientService;
    private final ObjectMapper objectMapper;

    /**
     * Kafka listener for invoice cdc topic.
     *
     * @param messages invoice messages
     * @param acknowledgment acknowledgment
     */
    @KafkaListener(
        autoStartup = "${consumer.invoices.enabled}",
        topics = "${topics.invoices}",
        groupId = "${consumer.invoices.group_id}",
        containerFactory = "invoiceMessagesListenerContainerFactory"
    )
    public void listenMessages(
        List<ConsumerRecord<GenericRecord, GenericRecord>> messages,
        Acknowledgment acknowledgment
    ) {
        try {
            var futures = messages.stream()
                .map(ConsumerRecord::value)
                .filter(Objects::nonNull)
                .map(this::getFiscalDocumentEventValue)
                .filter(Objects::nonNull)
                .map(fiscalDocumentEventProducer::send)
                .filter(Objects::nonNull)
                .toList();

            for (var future : futures) {
                future.get();
            }
        } catch (Exception e) {
            throw new RuntimeException("Error while sending MFD Invoice message.", e);
        }

        acknowledgment.acknowledge();
    }

    private FiscalDocumentEventValue getFiscalDocumentEventValue(GenericRecord record) {
        var reservationId = GenericRecordHelper.toLong(record, "reservation_id");
        var groupProfileId = GenericRecordHelper.longOrNull(record, "group_profile_id");
        var sourceId = groupProfileId != null ? groupProfileId : reservationId;
        var sourceKind = groupProfileId != null
            ? com.cloudbeds.FiscalDocumentService.SourceKind.GROUP_PROFILE :
            com.cloudbeds.FiscalDocumentService.SourceKind.RESERVATION;

        var transactionsCdcModel = getInvoiceTransactionCdcModels(record);

        var amount = calculateTotalAmount(transactionsCdcModel);
        var paidAmount = calculatePaidAmount(transactionsCdcModel);

        String currency = getCurrency(transactionsCdcModel);
        var id = GenericRecordHelper.toLong(record, "id");
        var scale = 0;
        if (currency != null) {
            try {
                scale = Currency.valueOf(currency).getScale();
            } catch (IllegalArgumentException e) {
                // TODO remove this catch once we migrate old data
                log.warn("Unsupported currency: {}", currency);
            }
        } else {
            log.warn("No currency found for mfd invoice: {}", id);
        }

        var amountInDecimal = (long) (amount * Math.pow(10, scale));
        var paidAmountInDecimal = (long) (paidAmount * Math.pow(10, scale));
        var externalSettingsId = GenericRecordHelper.longOrNull(record, "invoice_setup_id");
        return FiscalDocumentEventValue.newBuilder()
            .setType(FiscalDocumentEventType.MFD_INVOICE_EVENT)
            .setSourceId(sourceId)
            .setSourceKind(sourceKind)
            .setPropertyId(GenericRecordHelper.toLong(record, "property_id"))
            .setMfdInvoiceEvent(
                com.cloudbeds.FiscalDocumentService.MfdInvoiceEventValue.newBuilder()
                    .setId(id)
                    .setGroupProfileId(groupProfileId)
                    .setReservationId(reservationId)
                    .setPropertyId(GenericRecordHelper.toLong(record, "property_id"))
                    .setInvoiceSetupId(GenericRecordHelper.longOrNull(record, "invoice_setup_id"))
                    .setInvoiceNumber(GenericRecordHelper.longOrNull(record, "invoice_number"))
                    .setType(GenericRecordHelper.stringOrNull(record, "type"))
                    .setTransactions(getTransactions(transactionsCdcModel))
                    .setGenerateDate(GenericRecordHelper.toInstant(record, "generate_date"))
                    .setPdfUrl(GenericRecordHelper.stringOrNull(record, "pdf_url"))
                    .setEmail(GenericRecordHelper.stringOrNull(record, "email"))
                    .setSent(Integer.valueOf(1).equals(GenericRecordHelper.intOrNull(record, "sent")))
                    .setCreditNoteDate(GenericRecordHelper.toInstant(record, "credit_note_date"))
                    .setCreditNoteUrl(GenericRecordHelper.stringOrNull(record, "credit_note_url"))
                    .setCreditNoteReason(GenericRecordHelper.stringOrNull(record, "credit_note_reason"))
                    .setCreditNoteNumber(GenericRecordHelper.longOrNull(record, "credit_note_number"))
                    .setCreditNoteSetupId(GenericRecordHelper.longOrNull(record, "credit_note_setup_id"))
                    .setStatus(GenericRecordHelper.stringOrNull(record, "status"))
                    .setUserId(GenericRecordHelper.longOrNull(record, "user_id"))
                    .setAmount(amountInDecimal)
                    .setBalance(amountInDecimal - paidAmountInDecimal)
                    .setCurrency(currency)
                    .setExternalSettingsId(externalSettingsId)
                    .setRecipients(getRecipients(record))
                    .build()
            )
            .build();
    }

    private String getTransactions(List<InvoiceTransactionCdcModel> transactionsCdcModel) {
        try {
            if (transactionsCdcModel == null || transactionsCdcModel.isEmpty()) {
                return objectMapper.writeValueAsString(List.of());
            }

            var invoiceModelTransactions = transactionsCdcModel.stream()
                .map(tx -> objectMapper.convertValue(tx, InvoiceTransactionModel.class))
                .toList();

            return objectMapper.writeValueAsString(invoiceModelTransactions);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error serializing transactions to json string", e);
        }
    }

    private List<InvoiceTransactionCdcModel> getInvoiceTransactionCdcModels(GenericRecord record) {
        var transactions = GenericRecordHelper.stringOrNull(record, "transactions");

        if (!StringUtils.hasText(transactions)) {
            return null;
        }

        try {
            List<InvoiceTransactionCdcModel> result = objectMapper.readValue(transactions, new TypeReference<>() {});
            if (result == null) {
                return null;
            }

            return result.stream()
                .filter(Objects::nonNull)
                .toList();
        } catch (JsonProcessingException e) {
            // TODO reenable after processing historical data
            // throw new RuntimeException("Error parsing transactions", e);
            log.warn("Error parsing transactions: {}", e.getMessage(), e);
            return null;
        }
    }

    private double calculateTotalAmount(List<InvoiceTransactionCdcModel> transactionsCdcModel) {
        if (transactionsCdcModel == null) {
            return 0.0;
        }
        return transactionsCdcModel.stream()
            .mapToDouble(InvoiceTransactionCdcModel::getCreditNotFormated)
            .sum();
    }

    private double calculatePaidAmount(List<InvoiceTransactionCdcModel> transactionsCdcModel) {
        if (transactionsCdcModel == null) {
            return 0.0;
        }
        return transactionsCdcModel.stream()
            .mapToDouble(InvoiceTransactionCdcModel::getDebitNotFormated)
            .sum();
    }

    private String getCurrency(List<InvoiceTransactionCdcModel> transactionsCdcModel) {
        if (transactionsCdcModel == null || transactionsCdcModel.isEmpty()) {
            return null;
        }
        return transactionsCdcModel.stream()
            .map(InvoiceTransactionCdcModel::getCurrency)
            .filter(Objects::nonNull)
            .findFirst().orElse(null);
    }

    private String getRecipients(GenericRecord record) {
        try {
            String reservationJson = GenericRecordHelper.stringOrNull(record, "reservation");
            String groupProfileJson = GenericRecordHelper.stringOrNull(record, "group_profile");

            try {
                if (reservationJson != null
                        && objectMapper.readTree(reservationJson).getNodeType() != JsonNodeType.OBJECT) {
                    log.warn("Unsupported reservation JSON, node is not OBJECT: {}", reservationJson);
                    return objectMapper.writeValueAsString(List.of());
                }
            } catch (JsonProcessingException e) {
                log.error("Invalid reservation JSON string: {}", reservationJson);
                return objectMapper.writeValueAsString(List.of());
            }

            Map<String, Object> reservation = StringUtils.hasText(reservationJson)
                ? objectMapper.readValue(reservationJson, new TypeReference<>() {})
                : null;

            if (reservation != null && reservation.get("reservation_id") == null) {
                log.warn("Unsupported reservation JSON, missing 'reservation_id': {}", reservationJson);
                return objectMapper.writeValueAsString(List.of());
            }

            try {
                if (groupProfileJson != null
                        && objectMapper.readTree(groupProfileJson).getNodeType() != JsonNodeType.OBJECT) {
                    log.warn("Unsupported groupProfile JSON, node is not OBJECT: {}", groupProfileJson);
                    return objectMapper.writeValueAsString(List.of());
                }
            } catch (JsonProcessingException e) {
                log.error("Invalid groupProfile JSON string: {}", groupProfileJson);
                return objectMapper.writeValueAsString(List.of());
            }

            Map<String, Object> groupProfile = StringUtils.hasText(groupProfileJson)
                ? objectMapper.readValue(groupProfileJson, new TypeReference<>() {})
                : null;

            if (groupProfile != null && groupProfile.get("id") == null) {
                log.warn("Unsupported groupProfile JSON, missing 'id': {}", groupProfileJson);
                return objectMapper.writeValueAsString(List.of());
            }

            List<Recipient> recipients = fiscalDocumentRecipientService
                .getRecipientsFromCdc(reservation, groupProfile);

            return objectMapper.writeValueAsString(recipients);

        } catch (JsonProcessingException e) {
            log.error("Error parsing reservation or group profile: {}", e.getMessage(), e);
            throw new RuntimeException("Error parsing recipient data", e);
        }
    }
}
