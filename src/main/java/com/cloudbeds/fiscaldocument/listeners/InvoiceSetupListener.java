package com.cloudbeds.fiscaldocument.listeners;

import com.cloudbeds.fiscaldocument.entity.DocumentConfig;
import com.cloudbeds.fiscaldocument.entity.DocumentContent;
import com.cloudbeds.fiscaldocument.entity.DocumentSequence;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.services.DocumentConfigService;
import com.cloudbeds.fiscaldocument.services.DocumentSequenceService;
import com.cloudbeds.fiscaldocument.support.utils.GenericRecordHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class InvoiceSetupListener {

    private final DocumentSequenceService documentSequenceService;
    private final DocumentConfigService documentConfigService;
    private final TransactionTemplate transactionTemplate;
    private final ObjectMapper objectMapper;

    /**
     * Kafka listener for invoice setup cdc topic.
     *
     * @param message invoice setup message
     * @param acknowledgment acknowledgment
     */
    @KafkaListener(
        autoStartup = "${consumer.invoice_setup.enabled}",
        topics = "${topics.invoice_setup}",
        groupId = "${consumer.invoice_setup.group_id}",
        containerFactory = "invoiceSetupMessagesListenerContainerFactory"
    )
    public void listenMessages(
        ConsumerRecord<GenericRecord, GenericRecord> message,
        Acknowledgment acknowledgment
    ) {
        if (message.value() == null) {
            acknowledgment.acknowledge();
            return;
        }

        var value = message.value();
        var id = GenericRecordHelper.toLong(value, "id");
        var propertyId = GenericRecordHelper.toLong(value, "property_id");

        transactionTemplate.executeWithoutResult(status -> {
            var isCompact = checkForFlag(GenericRecordHelper.intOrNull(value, "is_compact"));
            var prefix = GenericRecordHelper.stringOrNull(value, "prefix");
            var suffix = GenericRecordHelper.stringOrNull(value, "suffix");
            var isCreditNoteSameSettings = checkForFlag(
                GenericRecordHelper.intOrNull(value, "credit_note_same_settings")
            );
            var startNumber = GenericRecordHelper.longOrNull(value, "start_number");
            var creditNoteStartNumber = GenericRecordHelper.longOrNull(value, "credit_note_start_number");

            var invoiceConfigOptional = documentConfigService.getDocumentConfigOptional(
                propertyId,
                DocumentKind.INVOICE
            );
            var creditNoteConfigOptional = documentConfigService.getDocumentConfigOptional(
                propertyId,
                DocumentKind.CREDIT_NOTE
            );

            DocumentSequence invoiceSequence = invoiceConfigOptional
                .flatMap(config -> getSequenceForKind(config, DocumentKind.INVOICE))
                .orElseGet(() -> createSequenceWithNumber(propertyId, startNumber));

            DocumentSequence creditNoteSequence = creditNoteConfigOptional
                .flatMap(config -> getSequenceForKind(config, DocumentKind.CREDIT_NOTE))
                .orElseGet(() -> isCreditNoteSameSettings
                    ? invoiceSequence
                    : createSequenceWithNumber(propertyId, creditNoteStartNumber));

            var invoiceConfig = invoiceConfigOptional.orElseGet(() -> createEmptyConfig(
                propertyId, DocumentKind.INVOICE
            ));

            var creditNoteConfig = creditNoteConfigOptional.orElseGet(() -> createEmptyConfig(
                propertyId, DocumentKind.CREDIT_NOTE)
            );

            // ----- Update Invoice Content -----
            updateContentInvoice(invoiceConfig, isCompact, prefix, suffix, id, invoiceSequence);

            // If credit note needs new sequence
            if (!isCreditNoteSameSettings && invoiceSequence.getId().equals(creditNoteSequence.getId())) {
                creditNoteSequence = createSequenceWithNumber(propertyId, creditNoteStartNumber);
                updateContentCreditNote(creditNoteConfig, isCompact, prefix, suffix, id, creditNoteSequence);
            } else if (creditNoteConfigOptional.isEmpty()) {
                updateContentCreditNote(creditNoteConfig, isCompact, prefix, suffix, id, creditNoteSequence);
            }

            // Handle compact flag changes
            if (invoiceConfig.isCompact() != isCompact) {
                updateContentInvoice(invoiceConfig, isCompact, prefix, suffix, id, invoiceSequence);
            }

            if (creditNoteConfig.isCompact() != isCompact) {
                updateContentCreditNote(creditNoteConfig, isCompact, prefix, suffix, id, creditNoteSequence);
            }

            // ----- Update Fields from Message -----
            try {
                updateInvoiceConfigFromMessage(value, invoiceConfig);
                updateCreditNoteConfigFromMessage(value, creditNoteConfig, isCreditNoteSameSettings);
            } catch (JsonProcessingException e) {
                log.error("Error parsing JSON from message: {}", value, e);
                throw new RuntimeException("Failed to parse JSON from message", e);
            }

            documentConfigService.saveAll(List.of(invoiceConfig, creditNoteConfig));
        });

        acknowledgment.acknowledge();
    }

    private DocumentSequence createSequenceWithNumber(Long propertyId, Long number) {
        var sequence = documentSequenceService.createDefaultSequence(propertyId);
        if (number != null) {
            sequence.setNumber(number);
        }
        return sequence;
    }

    private DocumentConfig createEmptyConfig(Long propertyId, DocumentKind kind) {
        var config = new DocumentConfig();
        config.setPropertyId(propertyId);
        config.setDocumentKind(kind);
        return config;
    }

    private Optional<DocumentSequence> getSequenceForKind(DocumentConfig config, DocumentKind kind) {
        return config.getDocumentContents().stream()
            .filter(DocumentContent::isActive)
            .filter(content -> content.getDocumentKind() == kind)
            .map(DocumentContent::getDocumentSequence)
            .findFirst();
    }

    private void updateContentCreditNote(
        DocumentConfig creditNoteConfig,
        boolean isCompact,
        String prefix,
        String suffix,
        Long externalId,
        DocumentSequence creditNoteSequence
    ) {
        creditNoteConfig.setCompact(isCompact);
        creditNoteConfig.addContent(
            documentConfigService.createDefaultContentsCreditNote(
                creditNoteConfig.getPropertyId(),
                prefix,
                suffix,
                externalId,
                creditNoteSequence,
                SourceKind.RESERVATION
            )
        );

        creditNoteConfig.addContent(
            documentConfigService.createDefaultContentsCreditNote(
                creditNoteConfig.getPropertyId(),
                prefix,
                suffix,
                externalId,
                creditNoteSequence,
                SourceKind.GROUP_PROFILE
            )
        );
    }

    private void updateContentInvoice(
        DocumentConfig invoiceConfig,
        boolean isCompact,
        String prefix,
        String suffix,
        Long externalId,
        DocumentSequence invoiceSequence
    ) {
        invoiceConfig.setCompact(isCompact);
        invoiceConfig.setCompact(isCompact);
        invoiceConfig.setPrefix(prefix);
        invoiceConfig.setSuffix(suffix);

        var existingReservationContentOptional = invoiceConfig.getDocumentContents().stream()
            .filter(dc -> Objects.equals(externalId, dc.getExternalId())
                && Objects.equals(SourceKind.RESERVATION, dc.getSourceKind()))
            .findFirst();

        if (existingReservationContentOptional.isPresent()) {
            var existingContent = existingReservationContentOptional.get();
            existingContent.setPrefix(prefix);
            existingContent.setSuffix(suffix);
        } else {
            invoiceConfig.addContent(
                documentConfigService.createDefaultContentsInvoice(
                    invoiceConfig.getPropertyId(),
                    prefix,
                    suffix,
                    externalId,
                    invoiceSequence,
                    SourceKind.RESERVATION
                )
            );
        }

        var existingGroupContentOptional = invoiceConfig.getDocumentContents().stream()
            .filter(dc -> Objects.equals(externalId, dc.getExternalId())
                && Objects.equals(SourceKind.GROUP_PROFILE, dc.getSourceKind()))
            .findFirst();

        if (existingGroupContentOptional.isPresent()) {
            var existingContent = existingGroupContentOptional.get();
            existingContent.setPrefix(prefix);
            existingContent.setSuffix(suffix);
        } else {
            invoiceConfig.addContent(
                documentConfigService.createDefaultContentsInvoice(
                    invoiceConfig.getPropertyId(),
                    prefix,
                    suffix,
                    externalId,
                    invoiceSequence,
                    SourceKind.GROUP_PROFILE
                )
            );
        }
    }

    private void updateCreditNoteConfigFromMessage(
        GenericRecord newValue,
        DocumentConfig creditNoteConfig,
        boolean isCreditNoteSameSettings
    ) throws JsonProcessingException {
        updateInvoiceConfigFromMessage(newValue, creditNoteConfig);
        if (!isCreditNoteSameSettings) {
            creditNoteConfig.setPrefix(GenericRecordHelper.stringOrNull(newValue, "credit_note_prefix"));
            creditNoteConfig.setSuffix(GenericRecordHelper.stringOrNull(newValue, "credit_note_suffix"));
            var titleString = GenericRecordHelper.stringOrNull(newValue, "credit_note_title");
            if (StringUtils.hasText(titleString)) {
                Map<String, String> title = objectMapper.readValue(titleString, new TypeReference<>() {
                });
                creditNoteConfig.setTitle(title);
            } else {
                creditNoteConfig.setTitle(null);
            }
        }
    }

    private void updateInvoiceConfigFromMessage(GenericRecord newValue, DocumentConfig config)
        throws JsonProcessingException {
        config.setCpf(GenericRecordHelper.stringOrNull(newValue, "cpf"));

        var customTextString = GenericRecordHelper.stringOrNull(newValue, "custom_text");
        if (StringUtils.hasText(customTextString)) {
            try {
                Map<String, String> customText = objectMapper.readValue(customTextString, new TypeReference<>() {});
                config.setCustomText(customText);
            } catch (JsonProcessingException e) {
                var setupId = GenericRecordHelper.toLong(newValue, "id");
                log.error("Error parsing invoice setup {}, 'custom_text' is invalid JSON", setupId);
            }
        }
        var dueDays = GenericRecordHelper.intOrNull(newValue, "due_date");
        config.setDueDays(dueDays == null ? 0 : dueDays);
        config.setPrefix(GenericRecordHelper.stringOrNull(newValue, "prefix"));
        config.setSuffix(GenericRecordHelper.stringOrNull(newValue, "suffix"));
        config.setUseDocumentNumber(
            checkForFlag(GenericRecordHelper.intOrNull(newValue, "use_document_number"))
        );
        config.setIncludeRoomNumber(
            checkForFlag(GenericRecordHelper.intOrNull(newValue, "room_number"))
        );
        config.setShowDetailedTaxFee(
            checkForFlag(GenericRecordHelper.intOrNull(newValue, "tax_specifics"))
        );
        config.setShowLegalCompanyName(
            checkForFlag(GenericRecordHelper.intOrNull(newValue, "show_legal_company_name"))
        );
        config.setLegalCompanyName(GenericRecordHelper.stringOrNull(newValue, "legal_company_name"));
        config.setTaxId1(GenericRecordHelper.stringOrNull(newValue, "tax_id1"));
        config.setTaxId2(GenericRecordHelper.stringOrNull(newValue, "tax_id2"));
        var titleString = GenericRecordHelper.stringOrNull(newValue, "title");
        if (StringUtils.hasText(titleString)) {
            Map<String, String> title = objectMapper.readValue(titleString, new TypeReference<>() {
            });
            config.setTitle(title);
        }

        config.setChargeBreakdown(
            checkForFlag(GenericRecordHelper.intOrNull(newValue, "charge_breakdown"))
        );
        config.setUseGuestLang(
            checkForFlag(GenericRecordHelper.intOrNull(newValue, "guest_lang"))
        );
        config.setLang(GenericRecordHelper.stringOrNull(newValue, "invoice_lang"));
    }

    private boolean checkForFlag(Integer flag) {
        return flag != null && flag.equals(1);
    }
}
