package com.cloudbeds.fiscaldocument.listeners;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventType;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import com.cloudbeds.fiscaldocument.eventhandlers.fiscaldocument.CdcEventHandler;
import com.cloudbeds.fiscaldocument.eventhandlers.fiscaldocument.FiscalDocumentEventHandler;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class FiscalDocumentEventListener {

    private final CdcEventHandler cdcEventHandler;
    private final FiscalDocumentEventHandler fiscalDocumentEventHandler;

    /**
     * Kafka listener for invoice cdc topic.
     *
     * @param messages invoice messages
     * @param acknowledgment acknowledgment
     */
    @KafkaListener(
        autoStartup = "${consumer.fiscal_document_events.enabled}",
        topics = "${topics.fiscal_document_events}",
        groupId = "${consumer.fiscal_document_events.group_id}",
        containerFactory = "fiscalDocumentEventListenerContainerFactory"
    )
    public void listenMessages(
        List<ConsumerRecord<FiscalDocumentEventKey, FiscalDocumentEventValue>> messages,
        Acknowledgment acknowledgment
    ) {
        var cdcRecords = messages.stream()
            .map(ConsumerRecord::value)
            .filter(Objects::nonNull)
            .filter(value -> value.getType().equals(FiscalDocumentEventType.MFD_INVOICE_EVENT))
            .toList();

        var nonCdcRecords = messages.stream()
            .map(ConsumerRecord::value)
            .filter(Objects::nonNull)
            .filter(value -> !value.getType().equals(FiscalDocumentEventType.MFD_INVOICE_EVENT))
            .toList();

        fiscalDocumentEventHandler.processEvents(nonCdcRecords);
        cdcEventHandler.processCdcMessages(cdcRecords);

        acknowledgment.acknowledge();
    }
}
