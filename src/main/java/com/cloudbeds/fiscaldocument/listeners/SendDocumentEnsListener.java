package com.cloudbeds.fiscaldocument.listeners;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import com.cloudbeds.fiscaldocument.ens.EnsService;
import com.cloudbeds.fiscaldocument.ens.enums.FiscalDocumentEnsEventType;
import com.cloudbeds.fiscaldocument.ens.events.FiscalDocumentEnsEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SendDocumentEnsListener {

    private final EnsService ensService;

    /**
     * Kafka listener for invoice topic to send ENS event.
     *
     * @param messages invoice messages
     * @param acknowledgment acknowledgment
     */
    @KafkaListener(
        autoStartup = "${consumer.fiscal_documents_ens.enabled}",
        topics = "${topics.fiscal_documents}",
        groupId = "${consumer.fiscal_documents_ens.group_id}",
        containerFactory = "fiscalDocumentEnsListenerContainerFactory"
    )
    public void listenMessages(
        List<ConsumerRecord<FiscalDocumentKey, FiscalDocumentValue>> messages,
        Acknowledgment acknowledgment
    ) {
        var records = messages.stream()
            .map(ConsumerRecord::value)
            .filter(Objects::nonNull)
            .toList();

        var eventsByPropertyId = new HashMap<Long, ArrayList<FiscalDocumentEnsEvent>>();

        records.forEach(fiscalDocument -> {
            FiscalDocumentEnsEventType eventType;
            if (fiscalDocument.getVersion() == 0) {
                eventType = FiscalDocumentEnsEventType.EVENT_TYPE_CREATE;
            } else {
                eventType = FiscalDocumentEnsEventType.EVENT_TYPE_UPDATE;
            }

            var ensEvent = new FiscalDocumentEnsEvent(
                String.valueOf(fiscalDocument.getId()),
                fiscalDocument.getKind(),
                String.valueOf(fiscalDocument.getPropertyId()),
                eventType
            );

            var map = eventsByPropertyId.getOrDefault(fiscalDocument.getPropertyId(), new ArrayList<>());
            map.add(ensEvent);
            eventsByPropertyId.put(fiscalDocument.getPropertyId(), map);
        });

        eventsByPropertyId.forEach(ensService::sendFiscalDocumentEvents);
        acknowledgment.acknowledge();
    }
}
