package com.cloudbeds.fiscaldocument.listeners.config;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

import static com.cloudbeds.fiscaldocument.support.consumers.ConsumerUtils.getDefaultConcurrentBatchListenerFactory;
import static io.confluent.kafka.serializers.KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG;

@EnableKafka
@Configuration
@RequiredArgsConstructor
public class FiscalDocumentEnsConsumerConfig {
    private final KafkaProperties kafkaProperties;
    @Value("${consumer.default.backoff.ms}")
    Long backOffMs;

    @Profile("!local")
    @Bean("fiscalDocumentEnsListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<FiscalDocumentKey, FiscalDocumentValue>
        fiscalDocumentEnsListenerContainerFactory() {
        return getDefaultConcurrentBatchListenerFactory(fiscalDocumentEnsConsumerFactory(), 1, backOffMs);
    }

    @Profile("local")
    @Bean("fiscalDocumentEnsListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<FiscalDocumentKey, FiscalDocumentValue>
        fiscalDocumentEnsListenerLocalContainerFactory() {
        return getDefaultConcurrentBatchListenerFactory(fiscalDocumentEnsLocalConsumerFactory(), 1, backOffMs);
    }

    private Map<String, Object> fiscalDocumentEnsConsumerConfig() {
        var prop = kafkaProperties.buildConsumerProperties(null);
        prop.put(SPECIFIC_AVRO_READER_CONFIG, true);
        return prop;
    }

    private ConsumerFactory<FiscalDocumentKey, FiscalDocumentValue> fiscalDocumentEnsConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(fiscalDocumentEnsConsumerConfig());
    }

    private ConsumerFactory<FiscalDocumentKey, FiscalDocumentValue>
        fiscalDocumentEnsLocalConsumerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        // Override bootstrap servers
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "http://127.0.0.1:9092");
        // Schema Registry settings
        configProps.put("schema.registry.url", "http://localhost:8081/");
        // Custom group ID
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, "custom-group");
        // Custom deserializers
        configProps.put(
            ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, io.confluent.kafka.serializers.KafkaAvroDeserializer.class);
        configProps.put(
            ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, io.confluent.kafka.serializers.KafkaAvroDeserializer.class);
        configProps.put(SPECIFIC_AVRO_READER_CONFIG, true);
        // Custom offset reset strategy
        configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");

        return new DefaultKafkaConsumerFactory<>(configProps);
    }
}
