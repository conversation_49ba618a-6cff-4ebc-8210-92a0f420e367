package com.cloudbeds.fiscaldocument.listeners.config;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

import static com.cloudbeds.fiscaldocument.support.consumers.ConsumerUtils.getDefaultConcurrentBatchListenerFactory;
import static io.confluent.kafka.serializers.KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.MAX_POLL_RECORDS_CONFIG;

@EnableKafka
@Configuration
@RequiredArgsConstructor
public class FiscalDocumentEventConsumerConfig {
    private final KafkaProperties kafkaProperties;
    @org.springframework.beans.factory.annotation.Value("${consumer.default.backoff.ms}")
    Long backOffMs;

    @Value("${consumer.fiscal_document_events.poll_records}")
    private String maxPollRecords;

    @Profile("!local")
    @Bean("fiscalDocumentEventListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<FiscalDocumentEventKey, FiscalDocumentEventValue>
        fiscalDocumentEventListenerContainerFactory() {
        return getDefaultConcurrentBatchListenerFactory(fiscalDocumentEventConsumerFactory(), 1, backOffMs);
    }

    @Profile("local")
    @Bean("fiscalDocumentEventListenerContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<FiscalDocumentEventKey, FiscalDocumentEventValue>
        fiscalDocumentEventListenerLocalContainerFactory() {
        return getDefaultConcurrentBatchListenerFactory(fiscalDocumentEventLocalConsumerFactory(), 1, backOffMs);
    }

    private Map<String, Object> fiscalDocumentEventConsumerConfig() {
        var prop = kafkaProperties.buildConsumerProperties(null);
        prop.put(SPECIFIC_AVRO_READER_CONFIG, true);
        prop.put(MAX_POLL_RECORDS_CONFIG, maxPollRecords);
        return prop;
    }

    private ConsumerFactory<FiscalDocumentEventKey, FiscalDocumentEventValue> fiscalDocumentEventConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(fiscalDocumentEventConsumerConfig());
    }

    private ConsumerFactory<FiscalDocumentEventKey, FiscalDocumentEventValue>
        fiscalDocumentEventLocalConsumerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        // Override bootstrap servers
        configProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "http://127.0.0.1:9092");
        // Schema Registry settings
        configProps.put("schema.registry.url", "http://localhost:8081/");
        // Custom group ID
        configProps.put(ConsumerConfig.GROUP_ID_CONFIG, "custom-group");
        // Custom deserializers
        configProps.put(
            ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, io.confluent.kafka.serializers.KafkaAvroDeserializer.class);
        configProps.put(
            ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, io.confluent.kafka.serializers.KafkaAvroDeserializer.class);
        configProps.put(SPECIFIC_AVRO_READER_CONFIG, true);
        // Custom offset reset strategy
        configProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        configProps.put(MAX_POLL_RECORDS_CONFIG, maxPollRecords);

        return new DefaultKafkaConsumerFactory<>(configProps);
    }
}
