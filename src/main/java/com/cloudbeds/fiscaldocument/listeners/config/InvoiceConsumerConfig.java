package com.cloudbeds.fiscaldocument.listeners.config;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.avro.generic.GenericRecord;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

import static com.cloudbeds.fiscaldocument.support.consumers.ConsumerUtils.getDefaultConcurrentBatchListenerFactory;
import static io.confluent.kafka.serializers.KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG;

@EnableKafka
@Configuration
@RequiredArgsConstructor
public class InvoiceConsumerConfig {
    private final KafkaProperties kafkaProperties;
    @org.springframework.beans.factory.annotation.Value("${consumer.default.backoff.ms}")
    Long backOffMs;

    public ConsumerFactory<GenericRecord, GenericRecord> invoiceMessagesConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(invoiceMessagesConsumerConfig());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<GenericRecord, GenericRecord>
        invoiceMessagesListenerContainerFactory() {
        return getDefaultConcurrentBatchListenerFactory(invoiceMessagesConsumerFactory(), 1, backOffMs);
    }

    private Map<String, Object> invoiceMessagesConsumerConfig() {
        var prop = kafkaProperties.buildConsumerProperties(null);
        prop.put(SPECIFIC_AVRO_READER_CONFIG, false);
        return prop;
    }
}
