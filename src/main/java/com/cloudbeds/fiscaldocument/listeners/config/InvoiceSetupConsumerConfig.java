package com.cloudbeds.fiscaldocument.listeners.config;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.avro.generic.GenericRecord;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

import static com.cloudbeds.fiscaldocument.support.consumers.ConsumerUtils.getDefaultListenerFactory;
import static io.confluent.kafka.serializers.KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG;

@EnableKafka
@Configuration
@RequiredArgsConstructor
public class InvoiceSetupConsumerConfig {
    private final KafkaProperties kafkaProperties;
    @org.springframework.beans.factory.annotation.Value("${consumer.default.backoff.ms}")
    Long backOffMs;

    public ConsumerFactory<GenericRecord, GenericRecord> invoiceSetupMessagesConsumerFactory() {
        return new DefaultKafkaConsumerFactory<>(invoiceSetupMessagesConsumerConfig());
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<GenericRecord, GenericRecord>
        invoiceSetupMessagesListenerContainerFactory() {
        return getDefaultListenerFactory(invoiceSetupMessagesConsumerFactory(), 1, backOffMs);
    }

    private Map<String, Object> invoiceSetupMessagesConsumerConfig() {
        var prop = kafkaProperties.buildConsumerProperties(null);
        prop.put(SPECIFIC_AVRO_READER_CONFIG, false);
        return prop;
    }
}
