package com.cloudbeds.fiscaldocument.listeners;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentKind;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import com.cloudbeds.FiscalDocumentService.Method;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.enums.Origin;
import com.cloudbeds.fiscaldocument.myfrontdesk.PrivateApiMfdService;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentService;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

@Slf4j
@Component
@RequiredArgsConstructor
public class DualWritesListener {
    private final FiscalDocumentService fiscalDocumentService;
    private static final Set<FiscalDocumentKind> ALLOWED_DOCUMENT_KINDS = Set.of(
        FiscalDocumentKind.INVOICE,
        FiscalDocumentKind.CREDIT_NOTE
    );
    private final PrivateApiMfdService privateApiMfdService;
    private final TransactionTemplate transactionTemplate;

    /**
     * Kafka listener for a fiscal documents topic to send an invoice / credit note to MFD.
     *
     * @param messages invoice messages
     * @param acknowledgment acknowledgment
     */
    @KafkaListener(
        autoStartup = "${consumer.dual_writes.enabled}",
        topics = "${topics.fiscal_documents}",
        groupId = "${consumer.dual_writes.group_id}",
        containerFactory = "dualWritesListenerContainerFactory"
    )
    public void listenMessages(
        List<ConsumerRecord<FiscalDocumentKey, FiscalDocumentValue>> messages,
        Acknowledgment acknowledgment
    ) {
        var records = messages.stream()
            .map(ConsumerRecord::value)
            .filter(Objects::nonNull)
            .filter(value ->
                !Objects.equals(value.getOrigin(), Origin.MFD.toString())
                && ALLOWED_DOCUMENT_KINDS.contains(value.getKind())
                && value.getNumber() != null
                && !Objects.equals(value.getMethod(), Method.ADJUSTMENT)
            )
            .toList();

        if (!records.isEmpty()) {
            var recordsMap = records
                .stream()
                .collect(
                    Collectors.toMap(
                        FiscalDocumentValue::getId,
                        item -> item,
                        (first, second) -> second,
                        LinkedHashMap::new
                    )
                );

            transactionTemplate.executeWithoutResult(status -> {
                var fiscalDocumentsMap = fiscalDocumentService
                    .readonlyFindByIds(recordsMap.keySet())
                    .stream()
                    .collect(Collectors.toMap(FiscalDocument::getId, Function.identity()));

                recordsMap.forEach((id, fiscalDocumentValue) -> {
                    var fiscalDocument = fiscalDocumentsMap.get(id);

                    if (fiscalDocument != null) {
                        privateApiMfdService.postFiscalDocument(fiscalDocument);
                    }
                });
            });
        }

        acknowledgment.acknowledge();
    }
}
