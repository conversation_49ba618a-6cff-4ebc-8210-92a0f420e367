package com.cloudbeds.fiscaldocument.mappers;

import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentDetailedResponse;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentSummaryResponse;
import com.cloudbeds.fiscaldocument.controller.model.LatestLinkedDocument;
import com.cloudbeds.fiscaldocument.controller.model.RecipientDetails;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.entity.GovernmentIntegration;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.utils.Currency;
import com.cloudbeds.fiscaldocument.utils.EnumConverterUtil;
import com.cloudbeds.fiscaldocument.utils.MoneyUtils;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;

import static com.cloudbeds.fiscaldocument.utils.EnumConverterUtil.convert;

@Mapper(config = MapperConfig.class)
public interface FiscalDocumentMapper {

    /**
     * Map a FiscalEntity to a FiscalDocumentResponse.
     *
     * @param fiscalDocument document
     * @return FiscalDocumentResponse
     */
    default FiscalDocumentSummaryResponse toSummaryResponse(FiscalDocument fiscalDocument) {

        var response = new FiscalDocumentSummaryResponse();

        response.setId(Long.toString(fiscalDocument.getId()));

        response.setKind(convert(fiscalDocument.getKind()));

        response.setStatus(convert(fiscalDocument.getStatus()));
        response.setGovernmentIntegration(mapGovernmentIntegration(fiscalDocument.getGovernmentIntegration()));
        if (!fiscalDocument.getLinkedDocuments().isEmpty()) {
            response.setLinkedTo(fiscalDocument.getLinkedDocuments().getFirst().getLinkedDocumentId().toString());
        }

        return response;

    }

    /**
     * Map a GovernmentIntegration to a response.
     *
     * @param governmentIntegration governmentIntegration
     * @return GovernmentIntegration
     */
    default com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration mapGovernmentIntegration(
        GovernmentIntegration governmentIntegration
    ) {
        if (governmentIntegration == null) {
            return null;
        }

        var response = new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegration();
        response.setNumber(governmentIntegration.getNumber());
        response.setStatus(governmentIntegration.getStatus());
        response.setSeries(governmentIntegration.getSeries());
        response.setOfficialId(governmentIntegration.getOfficialId());
        try {
            response.setUrl(governmentIntegration.getUrl() != null
                ? java.net.URI.create(governmentIntegration.getUrl()) : null);
        } catch (IllegalArgumentException e) {
            // If URL is not valid URI format, set to null
            response.setUrl(null);
        }
        response.setExternalId(governmentIntegration.getExternalId());
        response.setRectifyingInvoiceType(governmentIntegration.getRectifyingInvoiceType());

        if (governmentIntegration.getQr() != null) {
            var qr = new com.cloudbeds.fiscaldocument.controller.model.GovernmentIntegrationQr();
            try {
                qr.setUrl(governmentIntegration.getQr().getUrl() != null
                    ? java.net.URI.create(governmentIntegration.getQr().getUrl()) : null);
            } catch (IllegalArgumentException e) {
                // If URL is not valid URI format, set to null
                qr.setUrl(null);
            }
            qr.setString(governmentIntegration.getQr().getString());
            response.setQr(qr);
        }

        return response;
    }

    /**
     * Map a FiscalEntity to a FiscalDocumentResponse.
     *
     * @param fiscalDocument fiscalDocument
     * @return FiscalDocumentResponse
     */
    default FiscalDocumentDetailedResponse toDetailedResponse(FiscalDocument fiscalDocument) {
        var response = new FiscalDocumentDetailedResponse();

        response.setId(Long.toString(fiscalDocument.getId()));
        response.setNumber(fiscalDocument.getNumber());
        response.setPropertyId(Long.toString(fiscalDocument.getPropertyId()));
        response.setUserId(Long.toString(fiscalDocument.getUserId()));
        response.setSourceId(Long.toString(fiscalDocument.getSourceId()));
        response.setSourceKind(EnumConverterUtil.convert(fiscalDocument.getSourceKind()));

        response.setKind(EnumConverterUtil.convert(fiscalDocument.getKind()));

        response.setInvoiceDate(fiscalDocument.getInvoiceDate());

        response.setStatus(convert(fiscalDocument.getStatus()));
        response.setFailReason(fiscalDocument.getFailReason());

        response.setDueDate(fiscalDocument.getDueDate());

        response.setOrigin(
            fiscalDocument.getOrigin() != null ? fiscalDocument.getOrigin().name() : null
        );

        if (
            fiscalDocument.getAmount() != null
                && fiscalDocument.getBalance() != null
                && fiscalDocument.getCurrency() != null
        ) {
            var currency = Currency.valueOf(fiscalDocument.getCurrency());
            response.setAmount(MoneyUtils.applyCurrencyScale(fiscalDocument.getAmount(), currency));

            response.setBalance(MoneyUtils.applyCurrencyScale(fiscalDocument.getBalance(), currency));
        }

        if (!fiscalDocument.getRecipients().isEmpty()) {
            response.setRecipients(
                fiscalDocument.getRecipients().stream()
                    .map(this::toResponse)
                    .collect(Collectors.toList())
            );
        }

        response.setExternalId(fiscalDocument.getExternalId());

        if (
            Set.of(DocumentKind.CREDIT_NOTE, DocumentKind.RECTIFY_INVOICE).contains(fiscalDocument.getKind())
                && fiscalDocument.getLinkedDocuments() != null
                && !fiscalDocument.getLinkedDocuments().isEmpty()
        ) {
            response.setParentId(String.valueOf(fiscalDocument.getLinkedDocuments().get(0).getLinkedDocumentId()));
        }

        response.setCreatedAt(fiscalDocument.getCreatedAt().atOffset(ZoneOffset.UTC));
        response.setUpdatedAt(fiscalDocument.getUpdatedAt().atOffset(ZoneOffset.UTC));
        response.setGovernmentIntegration(mapGovernmentIntegration(fiscalDocument.getGovernmentIntegration()));

        return response;
    }

    /** Default implementation for mapping a FiscalEntity to a FiscalDocumentResponse. */
    default List<FiscalDocumentDetailedResponse> toDetailedResponse(List<FiscalDocument> documents) {
        return documents.stream().map(this::toDetailedResponse).collect(Collectors.toList());
    }

    /**
     * Maps a FiscalDocument to a LatestLinkedDocument response.
     *
     * @param fiscalDocument the fiscal document to map
     * @return LatestLinkedDocument response
     */
    default LatestLinkedDocument mapLatestLinkedDocument(FiscalDocument fiscalDocument) {
        if (fiscalDocument == null) {
            return null;
        }

        var response = new LatestLinkedDocument();
        response.setId(Long.toString(fiscalDocument.getId()));
        response.setNumber(fiscalDocument.getNumber());
        response.setCreatedAt(fiscalDocument.getCreatedAt().atOffset(ZoneOffset.UTC));
        response.setKind(EnumConverterUtil.convert(fiscalDocument.getKind()));
        response.setStatus(EnumConverterUtil.convert(fiscalDocument.getStatus()));

        return response;
    }

    /**
     * Convert a FiscalDocumentRecipient to a GuestDetails response.
     *
     * @param recipient recipient
     * @return GuestDetails
     */
    default RecipientDetails toResponse(FiscalDocumentRecipient recipient) {
        var result = new RecipientDetails();
        result.setId(recipient.getRecipient().getId());
        result.setFirstName(recipient.getRecipient().getFirstName());
        result.setLastName(recipient.getRecipient().getLastName());
        result.setEmail(recipient.getRecipient().getEmail());
        result.setType(EnumConverterUtil.convert(recipient.getRecipient().getType()));

        if (recipient.getRecipient().getTax() != null) {
            result.setCompanyName(recipient.getRecipient().getTax().getCompanyName());
        }

        return result;
    }
}
