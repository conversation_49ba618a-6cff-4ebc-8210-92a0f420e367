package com.cloudbeds.fiscaldocument.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PropertyFeature {
    ACCOUNTS_RECEIVABLE_LEDGER("accounts_receivable_ledger"),
    INVOICING_V1_ENABLED("invoicing_enabled"),
    INVOICING_V2_ENABLED("invoicing_v2_enabled"),
    GROUP_PROFILE("group_housing_enabled"),
    HOUSE_ACCOUNT("house_accounts_enabled");

    private final String value;
}