package com.cloudbeds.fiscaldocument.enums;

import java.util.List;

public enum DocumentStatus {
    COMPLETED,
    VOIDED,
    PENDING_INTEGRATION,
    COMPLETED_INTEGRATION,
    FAILED_INTEGRATION,
    CORRECTION_NEEDED,
    PA<PERSON>,
    CA<PERSON><PERSON>ED,
    OPEN,
    REQUESTED,
    VOID_REQUESTED,
    FAILED,
    MA<PERSON><PERSON>LY_RECONCILED,
    REJECTED;

    public static List<DocumentStatus> getActiveStatuses() {
        return List.of(COMPLETED, PAID, OPEN, PENDING_INTEGRATION, FAILED_INTEGRATION, CORRECTION_NEEDED);
    }

    public static List<DocumentStatus> getBlockingTransactionsStatuses() {
        return List.of(REQUESTED, COMPLETED, PAID, OPEN, PENDING_INTEGRATION, FAILED_INTEGRATION, CORRECTION_NEEDED);
    }

    public static List<DocumentStatus> getIntegrationStatusForSendEvent() {
        return List.of(COMPLETED_INTEGRATION, FAILED_INTEGRATION);
    }

    /**
     * Returns a list of allowed status changes based on the current status.
     *
     * @param status the current document status
     * @return a list of allowed status changes
     */
    public static List<DocumentStatus> getAllowedStatusChanges(DocumentStatus status) {
        return switch (status) {
            case PENDING_INTEGRATION -> List.of(COMPLETED_INTEGRATION, FAILED, FAILED_INTEGRATION);
            case FAILED_INTEGRATION -> List.of(CORRECTION_NEEDED);
            case COMPLETED_INTEGRATION -> List.of(OPEN, FAILED);
            case OPEN -> List.of(VOID_REQUESTED, PAID, VOIDED, COMPLETED);
            case COMPLETED, CORRECTION_NEEDED -> List.of(VOID_REQUESTED, VOIDED);
            case REQUESTED -> List.of(OPEN, PENDING_INTEGRATION, FAILED);
            case VOID_REQUESTED -> List.of(VOIDED);
            case PAID -> List.of(VOIDED, COMPLETED, VOID_REQUESTED);
            case REJECTED, MANUALLY_RECONCILED, FAILED, VOIDED, CANCELED -> List.of();
        };
    }
}
