package com.cloudbeds.fiscaldocument.enums;

import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument_;
import com.cloudbeds.fiscaldocument.support.dynamicquery.NamedSortFieldInterface;
import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;
import lombok.Getter;

@Getter
public enum FiscalDocumentSortField implements NamedSortFieldInterface<FiscalDocument> {
    ID(0, "ID", List.of(FiscalDocument_.ID), FiscalDocument::getId, Long.class, "id"),
    CREATED_AT(1, "CREATED_AT", List.of(FiscalDocument_.CREATED_AT),
        FiscalDocument::getCreatedAt, LocalDateTime.class, "createdAt");

    private final int value;
    private final String fieldName;
    private final List<String> path;
    private final Function<FiscalDocument, ?> valueFunction;
    private final Class<? extends Comparable> dbClass;
    private final String alternativeFieldName;

    <T extends Comparable> FiscalDocumentSortField(
        int value,
        String fieldName,
        List<String> path,
        Function<FiscalDocument, T> getValueFunction,
        Class<T> dbClass,
        String alternativeFieldName
    ) {
        this.value = value;
        this.fieldName = fieldName;
        this.path = path;
        this.valueFunction = getValueFunction;
        this.dbClass = dbClass;
        this.alternativeFieldName = alternativeFieldName;
    }

    /**
     * Get Sort Field from proto value.
     *
     * @param value proto value
     * @return Sort field
     */
    public static FiscalDocumentSortField fromValue(int value) {
        for (FiscalDocumentSortField field : FiscalDocumentSortField.values()) {
            if (field.value == value) {
                return field;
            }
        }
        throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }

    @Override
    public String getAlternativeName() {
        return getAlternativeFieldName();
    }
}

