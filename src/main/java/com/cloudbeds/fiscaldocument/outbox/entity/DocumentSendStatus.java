package com.cloudbeds.fiscaldocument.outbox.entity;

import com.cloudbeds.fiscaldocument.outbox.model.DocumentEventMessage;
import com.cloudbeds.fiscaldocument.outbox.usertype.DocumentEventMessageUserType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;

@Entity
@Getter
@Setter
@Table(name = DocumentSendStatus.TABLE_NAME)
public class DocumentSendStatus {
    public static final String TABLE_NAME = "document_send_status";
    @Id
    @GeneratedValue(
        strategy = GenerationType.SEQUENCE,
        generator = "seq_common_fiscal_document_id"
    )
    @SequenceGenerator(
        name = "seq_common_fiscal_document_id",
        sequenceName = "seq_common_fiscal_document_id",
        allocationSize = 10000
    )
    @Column(name = "id", unique = true)
    private Long id;

    @Column(name = "payload")
    @Type(DocumentEventMessageUserType.class)
    private DocumentEventMessage payload;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false, nullable = false, columnDefinition = "timestamp")
    private LocalDateTime createdAt;
}
