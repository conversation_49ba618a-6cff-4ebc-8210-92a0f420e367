package com.cloudbeds.fiscaldocument.outbox.entity;

import com.cloudbeds.fiscaldocument.outbox.model.CreditNoteCreateEventMessage;
import com.cloudbeds.fiscaldocument.outbox.model.IntegrationCreateEventMessage;
import com.cloudbeds.fiscaldocument.outbox.model.InvoiceCreateEventMessage;
import com.cloudbeds.fiscaldocument.outbox.usertype.CreditNoteCreateEventMessageUserType;
import com.cloudbeds.fiscaldocument.outbox.usertype.IntegrationCreateEventMessageUserType;
import com.cloudbeds.fiscaldocument.outbox.usertype.InvoiceCreateEventMessageUserType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;

@Entity
@Getter
@Setter
@Table(name = DocumentEventSendStatus.TABLE_NAME)
public class DocumentEventSendStatus {
    public static final String TABLE_NAME = "document_event_send_status";
    @Id
    @GeneratedValue(
        strategy = GenerationType.SEQUENCE,
        generator = "seq_common_fiscal_document_id"
    )
    @SequenceGenerator(
        name = "seq_common_fiscal_document_id",
        sequenceName = "seq_common_fiscal_document_id",
        allocationSize = 10000
    )
    @Column(name = "id", unique = true)
    private Long id;

    @Column(name = "invoice_create_event")
    @Type(InvoiceCreateEventMessageUserType.class)
    private InvoiceCreateEventMessage invoiceCreateEvent;

    @Column(name = "integration_create_event")
    @Type(IntegrationCreateEventMessageUserType.class)
    private IntegrationCreateEventMessage integrationCreateEvent;

    @Column(name = "credit_note_create_event")
    @Type(CreditNoteCreateEventMessageUserType.class)
    private CreditNoteCreateEventMessage creditNoteCreateEvent;

    @Column(name = "rectify_invoice_create_event")
    @Type(CreditNoteCreateEventMessageUserType.class)
    private CreditNoteCreateEventMessage rectifyInvoiceCreateEvent;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false, nullable = false, columnDefinition = "timestamp")
    private LocalDateTime createdAt;
}
