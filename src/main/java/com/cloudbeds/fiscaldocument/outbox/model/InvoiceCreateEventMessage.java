package com.cloudbeds.fiscaldocument.outbox.model;

import com.cloudbeds.fiscaldocument.enums.SourceKind;
import java.time.LocalDate;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class InvoiceCreateEventMessage {
    private Long id;

    private Long propertyId;

    private Long userId;

    private Long sourceId;

    private SourceKind sourceKind;

    private LocalDate invoiceDate;

    private Long sequenceId;
    private List<Long> transactionIds;
}
