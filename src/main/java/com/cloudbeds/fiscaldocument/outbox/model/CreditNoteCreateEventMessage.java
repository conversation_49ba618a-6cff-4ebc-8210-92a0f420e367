package com.cloudbeds.fiscaldocument.outbox.model;

import com.cloudbeds.fiscaldocument.enums.CreationMethod;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreditNoteCreateEventMessage {
    private Long id;

    private Long propertyId;

    private Long userId;

    private Long sourceId;

    private SourceKind sourceKind;

    private Long sequenceId;

    private Long invoiceId;

    private String reason;
    private List<Long> transactionIds = new ArrayList<>();
    private CreationMethod method;
}
