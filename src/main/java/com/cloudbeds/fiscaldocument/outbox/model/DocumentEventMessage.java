package com.cloudbeds.fiscaldocument.outbox.model;

import com.cloudbeds.fiscaldocument.entity.GovernmentIntegration;
import com.cloudbeds.fiscaldocument.entity.Recipient;
import com.cloudbeds.fiscaldocument.enums.CreationMethod;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import com.cloudbeds.fiscaldocument.enums.Origin;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DocumentEventMessage {
    private Long id;
    private String number;
    private Long propertyId;
    private Long userId;
    private Long sourceId;
    private SourceKind sourceKind;
    private DocumentKind kind;
    private LocalDate invoiceDate;
    private LocalDate dueDate;
    private DocumentStatus status;
    private Origin origin;
    private String externalId;
    private String filePath;
    private String url;
    private String failReason;
    private String currency;
    private CreationMethod method;
    private Long amount;
    private Long balance;
    private GovernmentIntegration governmentIntegration;
    private List<Recipient> recipients = new ArrayList<>();
    private List<Long> transactionIds = new ArrayList<>();
    private List<Long> linkedDocumentIds = new ArrayList<>();
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private int version;
}
