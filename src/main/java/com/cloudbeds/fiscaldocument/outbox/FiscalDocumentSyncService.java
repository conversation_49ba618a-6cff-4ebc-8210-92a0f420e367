package com.cloudbeds.fiscaldocument.outbox;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import com.cloudbeds.fiscaldocument.converters.RecipientConverter;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentLinkedDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentTransaction;
import com.cloudbeds.fiscaldocument.entity.GovernmentIntegration;
import com.cloudbeds.fiscaldocument.entity.Recipient;
import com.cloudbeds.fiscaldocument.outbox.entity.DocumentSendStatus;
import com.cloudbeds.fiscaldocument.outbox.model.DocumentEventMessage;
import com.cloudbeds.fiscaldocument.outbox.repository.DocumentSendStatusRepository;
import com.cloudbeds.fiscaldocument.producers.FiscalDocumentProducer;
import com.cloudbeds.fiscaldocument.support.locks.TableLockService;
import com.cloudbeds.fiscaldocument.utils.EnumConverterUtil;
import com.cloudbeds.fiscaldocument.utils.RandomUtils;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import static com.cloudbeds.fiscaldocument.outbox.entity.DocumentSendStatus.TABLE_NAME;


@Service
@RequiredArgsConstructor
@Slf4j
public class FiscalDocumentSyncService {
    private final RecipientConverter recipientConverter;
    private final TransactionTemplate transactionTemplate;
    private final TableLockService tableLockService;
    private final DocumentSendStatusRepository documentSendStatusRepository;
    private final FiscalDocumentProducer fiscalDocumentProducer;

    private ScheduledExecutorService executorService;

    @Value(value = "${sync.documents.interval-ms}")
    private int checkIntervalMilliseconds;

    @Value(value = "${sync.documents.initial-delay-ms}")
    private int delayMilliseconds;

    /**
     * Create scheduler to send fiscal documents to Kafka.
     */
    @PostConstruct
    public void scheduleMessagesForSyncCheck() {
        executorService = Executors.newSingleThreadScheduledExecutor();

        executorService.scheduleAtFixedRate(
            this::publishMessages,
            RandomUtils.nextInt(delayMilliseconds) + (delayMilliseconds / 2),
            checkIntervalMilliseconds,
            TimeUnit.MILLISECONDS
        );
    }

    @PreDestroy
    public void tearDown() {
        executorService.shutdown();
    }

    /**
     * Add record to table for sync for fiscal documents.
     *
     * @param fiscalDocument fiscalDocument
     */
    @Transactional
    public void save(FiscalDocument fiscalDocument) {
        fiscalDocument.setUpdatedAt(LocalDateTime.now());
        documentSendStatusRepository.save(createDocumentSendStatus(fiscalDocument));
    }

    private DocumentSendStatus createDocumentSendStatus(FiscalDocument fiscalDocument) {
        var documentSendStatus = new DocumentSendStatus();
        documentSendStatus.setPayload(createDocumentEventMessage(fiscalDocument));

        return documentSendStatus;
    }

    private DocumentEventMessage createDocumentEventMessage(FiscalDocument fiscalDocument) {
        var result = new DocumentEventMessage();
        result.setId(fiscalDocument.getId());
        result.setPropertyId(fiscalDocument.getPropertyId());
        result.setUserId(fiscalDocument.getUserId());
        result.setSourceId(fiscalDocument.getSourceId());
        result.setSourceKind(fiscalDocument.getSourceKind());
        result.setKind(fiscalDocument.getKind());
        result.setInvoiceDate(fiscalDocument.getInvoiceDate());
        result.setDueDate(fiscalDocument.getDueDate());
        result.setStatus(fiscalDocument.getStatus());
        result.setCreatedAt(fiscalDocument.getCreatedAt());
        result.setNumber(fiscalDocument.getNumber());
        result.setFailReason(fiscalDocument.getFailReason());
        result.setExternalId(fiscalDocument.getExternalId());
        result.setCurrency(fiscalDocument.getCurrency());
        result.setMethod(fiscalDocument.getMethod());
        result.setAmount(fiscalDocument.getAmount());
        result.setBalance(fiscalDocument.getBalance());
        result.setOrigin(fiscalDocument.getOrigin());
        result.setGovernmentIntegration(fiscalDocument.getGovernmentIntegration());
        result.setFilePath(fiscalDocument.getFilePath());
        result.setLinkedDocumentIds(fiscalDocument.getLinkedDocuments().stream().map(
            FiscalDocumentLinkedDocument::getLinkedDocumentId).toList()
        );
        result.setTransactionIds(fiscalDocument.getTransactionList().stream().map(
            FiscalDocumentTransaction::getTransactionId).toList()
        );
        result.setRecipients(
            fiscalDocument.getRecipients().stream().map(FiscalDocumentRecipient::getRecipient).toList()
        );
        result.setUpdatedAt(fiscalDocument.getUpdatedAt());
        result.setUrl(fiscalDocument.getUrl());
        result.setCreatedAt(fiscalDocument.getCreatedAt());
        result.setVersion(fiscalDocument.getVersion());
        return result;
    }

    void publishMessages() {
        try {
            transactionTemplate.executeWithoutResult((transactionStatus -> {
                if (tableLockService.isTableLockedNonBlocking(TABLE_NAME)) {
                    return;
                }

                var unsentMessages = getUnsentMessages();

                if (!unsentMessages.isEmpty()) {
                    try {
                        var messages = unsentMessages
                            .stream()
                            .map(DocumentSendStatus::getPayload)
                            .map(this::buildFiscalDocumentEvent)
                            .toList();

                        var futures = messages.stream()
                            .map(fiscalDocumentProducer::send)
                            .filter(Objects::nonNull)
                            .toList();

                        for (var future : futures) {
                            future.get();
                        }

                        removeAfterSentById(
                            unsentMessages.stream().map(DocumentSendStatus::getId).toList()
                        );
                    } catch (Exception e) {
                        log.error("Error while sending message", e);
                        throw new RuntimeException(
                            "Error while sending message."
                        );
                    }
                }
            }));
        } catch (Exception e) {
            log.error("Error while sending Transaction message", e);
        }
    }

    private FiscalDocumentValue buildFiscalDocumentEvent(DocumentEventMessage eventMessage) {
        return FiscalDocumentValue.newBuilder()
            .setId(eventMessage.getId())
            .setPropertyId(eventMessage.getPropertyId())
            .setUserId(eventMessage.getUserId())
            .setSourceId(eventMessage.getSourceId())
            .setSourceKind(EnumConverterUtil.convertToAvro(eventMessage.getSourceKind()))
            .setKind(EnumConverterUtil.convertToAvro(eventMessage.getKind()))
            .setInvoiceDate(eventMessage.getInvoiceDate())
            .setDueDate(eventMessage.getDueDate())
            .setCurrency(eventMessage.getCurrency())
            .setAmount(eventMessage.getAmount())
            .setBalance(eventMessage.getBalance())
            .setStatus(EnumConverterUtil.convertToAvro(eventMessage.getStatus()))
            .setCreatedAt(eventMessage.getCreatedAt().toInstant(ZoneOffset.UTC))
            .setNumber(eventMessage.getNumber())
            .setMethod(EnumConverterUtil.convertToAvro(eventMessage.getMethod()))
            .setFailReason(eventMessage.getFailReason())
            .setExternalId(eventMessage.getExternalId())
            .setOrigin(eventMessage.getOrigin() != null
                ? eventMessage.getOrigin().name() : null
            )
            .setFilePath(eventMessage.getFilePath())
            .setLinkedDocumentIds(eventMessage.getLinkedDocumentIds())
            .setTransactionIds(eventMessage.getTransactionIds())
            .setUpdatedAt(eventMessage.getUpdatedAt().toInstant(ZoneOffset.UTC))
            .setUrl(eventMessage.getUrl())
            .setGovernmentIntegration(
                buildGovernmentIntegration(eventMessage.getGovernmentIntegration())
            )
            .setRecipients(buildRecipients(eventMessage.getRecipients()))
            .setVersion(eventMessage.getVersion())
            .build();
    }

    private com.cloudbeds.FiscalDocumentService.GovernmentIntegration buildGovernmentIntegration(
        GovernmentIntegration governmentIntegration
    ) {
        if (governmentIntegration == null) {
            return null;
        }

        var builder = com.cloudbeds.FiscalDocumentService.GovernmentIntegration.newBuilder()
            .setNumber(governmentIntegration.getNumber())
            .setSeries(governmentIntegration.getSeries())
            .setOfficialId(governmentIntegration.getOfficialId())
            .setExternalId(governmentIntegration.getExternalId())
            .setRectifyingInvoiceType(governmentIntegration.getRectifyingInvoiceType())
            .setStatus(governmentIntegration.getStatus())
            .setUrl(governmentIntegration.getUrl());

        if (governmentIntegration.getQr() != null) {
            builder.setQr(
                com.cloudbeds.FiscalDocumentService.Qr.newBuilder()
                    .setUrl(governmentIntegration.getQr().getUrl())
                    .setText(governmentIntegration.getQr().getString())
                    .build()
            );
        } else {
            builder.setQr(null);
        }

        return builder.build();

    }

    private List<com.cloudbeds.FiscalDocumentService.Recipient> buildRecipients(
        List<Recipient> recipients
    ) {
        if (recipients == null || recipients.isEmpty()) {
            return List.of();
        }

        return recipients.stream().map(recipientConverter::toAvro).toList();
    }

    private List<DocumentSendStatus> getUnsentMessages() {
        return documentSendStatusRepository.findFirst1000ByOrderByCreatedAt();
    }

    private void removeAfterSentById(List<Long> ids) {
        documentSendStatusRepository.removeMessagesByIds(ids);
    }
}
