package com.cloudbeds.fiscaldocument.outbox;

import com.cloudbeds.FiscalDocumentService.CreditNoteCreateEventValue;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventType;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import com.cloudbeds.FiscalDocumentService.IntegrationCreateEventValue;
import com.cloudbeds.FiscalDocumentService.InvoiceCreateEventValue;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentTransaction;
import com.cloudbeds.fiscaldocument.outbox.entity.DocumentEventSendStatus;
import com.cloudbeds.fiscaldocument.outbox.model.CreditNoteCreateEventMessage;
import com.cloudbeds.fiscaldocument.outbox.model.IntegrationCreateEventMessage;
import com.cloudbeds.fiscaldocument.outbox.model.InvoiceCreateEventMessage;
import com.cloudbeds.fiscaldocument.outbox.repository.DocumentEventSendStatusRepository;
import com.cloudbeds.fiscaldocument.producers.FiscalDocumentEventProducer;
import com.cloudbeds.fiscaldocument.support.locks.TableLockService;
import com.cloudbeds.fiscaldocument.utils.EnumConverterUtil;
import com.cloudbeds.fiscaldocument.utils.RandomUtils;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import static com.cloudbeds.fiscaldocument.outbox.entity.DocumentEventSendStatus.TABLE_NAME;


@Service
@RequiredArgsConstructor
@Slf4j
public class FiscalDocumentEventSyncService {
    private final TransactionTemplate transactionTemplate;
    private final TableLockService tableLockService;
    private final DocumentEventSendStatusRepository documentEventSendStatusRepository;
    private final FiscalDocumentEventProducer fiscalDocumentEventProducer;

    private ScheduledExecutorService executorService;

    @Value(value = "${sync.documents.interval-ms}")
    private int checkIntervalMilliseconds;

    @Value(value = "${sync.documents.initial-delay-ms}")
    private int delayMilliseconds;

    /**
     * Create scheduler to send transaction events to Kafka.
     */
    @PostConstruct
    public void scheduleMessagesForSyncCheck() {
        executorService = Executors.newSingleThreadScheduledExecutor();

        executorService.scheduleAtFixedRate(
            this::publishEvents,
            RandomUtils.nextInt(delayMilliseconds) + (delayMilliseconds / 2),
            checkIntervalMilliseconds,
            TimeUnit.MILLISECONDS
        );
    }

    @PreDestroy
    public void tearDown() {
        executorService.shutdown();
    }

    /**
     * Add record to table for sync for fiscal documents.
     *
     * @param fiscalDocument fiscalDocument
     */
    @Transactional
    public void save(FiscalDocument fiscalDocument) {
        switch (fiscalDocument.getKind()) {
            case INVOICE -> {
                var status = new DocumentEventSendStatus();
                status.setInvoiceCreateEvent(createInvoiceCreateEvent(fiscalDocument));
                documentEventSendStatusRepository.save(status);
            }
            case CREDIT_NOTE -> {
                var status = new DocumentEventSendStatus();
                status.setCreditNoteCreateEvent(createCreditNoteEvent(fiscalDocument));
                documentEventSendStatusRepository.save(status);
            }
            case RECTIFY_INVOICE -> {
                var status = new DocumentEventSendStatus();
                status.setCreditNoteCreateEvent(createCreditNoteEvent(fiscalDocument));
                documentEventSendStatusRepository.save(status);

            }
            case RECEIPT -> {
                //TODO: Implement
            }
            default -> {
                log.error("Unknown document kind: {}", fiscalDocument.getKind());
            }
        }
    }

    /**
     * Add record to table for sync for fiscal documents with integration.
     *
     * @param fiscalDocument fiscalDocument
     */
    @Transactional
    public void saveIntegrationEvent(FiscalDocument fiscalDocument) {
        var status = new DocumentEventSendStatus();
        status.setIntegrationCreateEvent(createIntegrationCreateEventMessage(fiscalDocument));
        documentEventSendStatusRepository.save(status);
    }

    void publishEvents() {
        try {
            transactionTemplate.executeWithoutResult((transactionStatus -> {
                if (tableLockService.isTableLockedNonBlocking(TABLE_NAME)) {
                    return;
                }

                var unsentMessages = getUnsentMessages();

                if (!unsentMessages.isEmpty()) {
                    try {
                        var messages = unsentMessages
                            .stream()
                            .map(sendStatus -> {
                                if (sendStatus.getInvoiceCreateEvent() != null) {
                                    return buildCreateInvoiceEvent(sendStatus.getInvoiceCreateEvent());
                                }
                                if (sendStatus.getCreditNoteCreateEvent() != null) {
                                    return buildCreateCreditNoteEvent(sendStatus.getCreditNoteCreateEvent());
                                }
                                if (sendStatus.getIntegrationCreateEvent() != null) {
                                    return buildIntegrationCreateEvent(sendStatus.getIntegrationCreateEvent());
                                }
                                return null;
                            })
                            .filter(Objects::nonNull)
                            .toList();

                        var futures = messages.stream()
                            .map(fiscalDocumentEventProducer::send)
                            .filter(Objects::nonNull)
                            .toList();

                        for (var future : futures) {
                            future.get();
                        }

                        removeAfterSentById(
                            unsentMessages.stream().map(DocumentEventSendStatus::getId).toList()
                        );
                    } catch (Exception e) {
                        log.error("Error while sending message", e);
                        throw new RuntimeException(
                            "Error while sending message."
                        );
                    }
                }
            }));
        } catch (Exception e) {
            log.error("Error while sending Transaction message", e);
        }
    }

    private FiscalDocumentEventValue buildIntegrationCreateEvent(IntegrationCreateEventMessage message) {
        var builder = FiscalDocumentEventValue.newBuilder()
            .setType(FiscalDocumentEventType.INTEGRATION_CREATE_EVENT)
            .setPropertyId(message.getPropertyId())
            .setSourceId(message.getSourceId())
            .setSourceKind(EnumConverterUtil.convertToAvro(message.getSourceKind()));
        if (message.getGovernmentIntegration() != null) {
            var integrationBuilder = IntegrationCreateEventValue.newBuilder();
            integrationBuilder.setId(message.getId())
                .setNumber(message.getGovernmentIntegration().getNumber())
                .setSeries(message.getGovernmentIntegration().getSeries())
                .setUrl(message.getGovernmentIntegration().getUrl())
                .setOfficialId(message.getGovernmentIntegration().getOfficialId())
                .setExternalId(message.getGovernmentIntegration().getExternalId())

                .setStatus(message.getGovernmentIntegration().getStatus())
                .setRectifyingInvoiceType(
                    message.getGovernmentIntegration().getRectifyingInvoiceType()
                );
            if (message.getGovernmentIntegration().getQr() != null) {
                integrationBuilder
                    .setQrUrl(message.getGovernmentIntegration().getQr().getUrl())
                    .setQrString(message.getGovernmentIntegration().getQr().getString());
            }

            builder.setIntegrationCreateEvent(integrationBuilder.build());
        }
        
        return builder.build();
    }

    private FiscalDocumentEventValue buildCreateInvoiceEvent(InvoiceCreateEventMessage message) {

        return FiscalDocumentEventValue.newBuilder()
            .setType(FiscalDocumentEventType.INVOICE_CREATE_EVENT)
            .setPropertyId(message.getPropertyId())
            .setSourceId(message.getSourceId())
            .setSourceKind(EnumConverterUtil.convertToAvro(message.getSourceKind()))
            .setInvoiceCreateEvent(
                InvoiceCreateEventValue.newBuilder()
                    .setId(message.getId())
                    .setUserId(message.getUserId())
                    .setSequenceId(message.getSequenceId())
                    .setTransactionIds(message.getTransactionIds())
                    .build()
            )
            .build();

    }

    private FiscalDocumentEventValue buildCreateCreditNoteEvent(CreditNoteCreateEventMessage message) {

        return FiscalDocumentEventValue.newBuilder()
            .setType(FiscalDocumentEventType.CREDIT_NOTE_CREATE_EVENT)
            .setPropertyId(message.getPropertyId())
            .setSourceId(message.getSourceId())
            .setSourceKind(EnumConverterUtil.convertToAvro(message.getSourceKind()))
            .setCreditNoteCreateEvent(
                CreditNoteCreateEventValue.newBuilder()
                    .setId(message.getId())
                    .setUserId(message.getUserId())
                    .setSequenceId(message.getSequenceId())
                    .setInvoiceId(message.getInvoiceId())
                    .setReason(message.getReason())
                    .setMethod(EnumConverterUtil.convertToAvroCreditNote(message.getMethod()))
                    .setTransactionIds(message.getTransactionIds())
                .build()
            )
            .build();
    }

    private List<DocumentEventSendStatus> getUnsentMessages() {
        return documentEventSendStatusRepository.findFirst1000ByOrderByCreatedAt();
    }

    private void removeAfterSentById(List<Long> ids) {
        documentEventSendStatusRepository.removeMessagesByIds(ids);
    }

    private InvoiceCreateEventMessage createInvoiceCreateEvent(FiscalDocument fiscalDocument) {
        var message = new InvoiceCreateEventMessage();
        message.setId(fiscalDocument.getId());
        message.setPropertyId(fiscalDocument.getPropertyId());
        message.setSourceId(fiscalDocument.getSourceId());
        message.setSourceKind(fiscalDocument.getSourceKind());
        message.setUserId(fiscalDocument.getUserId());
        message.setInvoiceDate(fiscalDocument.getInvoiceDate());
        message.setSequenceId(fiscalDocument.getSequenceId());
        message.setTransactionIds(
            fiscalDocument.getTransactionList().stream().map(FiscalDocumentTransaction::getTransactionId).toList()
        );

        return message;
    }

    private CreditNoteCreateEventMessage createCreditNoteEvent(FiscalDocument fiscalDocument) {
        var message = new CreditNoteCreateEventMessage();
        message.setId(fiscalDocument.getId());
        message.setPropertyId(fiscalDocument.getPropertyId());
        message.setSourceId(fiscalDocument.getSourceId());
        message.setSourceKind(fiscalDocument.getSourceKind());
        message.setUserId(fiscalDocument.getUserId());
        message.setSequenceId(fiscalDocument.getSequenceId());
        message.setInvoiceId(fiscalDocument.getLinkedDocuments().getFirst().getLinkedDocumentId());
        message.setReason(fiscalDocument.getReason());
        message.setMethod(fiscalDocument.getMethod());
        message.setTransactionIds(
            fiscalDocument.getTransactionList().stream().map(FiscalDocumentTransaction::getTransactionId).toList()
        );

        return message;
    }

    private IntegrationCreateEventMessage createIntegrationCreateEventMessage(FiscalDocument fiscalDocument) {
        var message = new IntegrationCreateEventMessage();
        message.setId(fiscalDocument.getId());
        message.setPropertyId(fiscalDocument.getPropertyId());
        message.setSourceId(fiscalDocument.getSourceId());
        message.setSourceKind(fiscalDocument.getSourceKind());
        message.setGovernmentIntegration(fiscalDocument.getGovernmentIntegration());
        return message;
    }
}
