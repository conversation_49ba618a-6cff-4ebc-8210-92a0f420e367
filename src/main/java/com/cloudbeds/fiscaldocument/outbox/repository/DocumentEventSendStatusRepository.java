package com.cloudbeds.fiscaldocument.outbox.repository;

import com.cloudbeds.fiscaldocument.outbox.entity.DocumentEventSendStatus;
import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

public interface DocumentEventSendStatusRepository extends CrudRepository<DocumentEventSendStatus, Long> {
    List<DocumentEventSendStatus> findFirst1000ByOrderByCreatedAt();

    @Modifying
    @Query("DELETE FROM DocumentEventSendStatus WHERE id IN :ids")
    void removeMessagesByIds(@Param("ids") List<Long> ids);
}
