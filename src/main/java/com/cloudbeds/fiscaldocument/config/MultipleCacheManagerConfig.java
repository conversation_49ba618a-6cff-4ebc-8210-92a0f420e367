package com.cloudbeds.fiscaldocument.config;


import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.support.NoOpCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Slf4j
@Configuration
@EnableCaching
@ConditionalOnClass(RedisConnectionFactory.class)
@ConditionalOnProperty(name = "spring.data.redis.host")
public class MultipleCacheManagerConfig {

    public static final int ONE_MINUTE = 60;
    public static final int FIVE_MINUTES = 60 * 5;
    public static final int ONE_HOUR = 60 * 60;
    public static final int ONE_DAY = 60 * 60 * 24;

    @Bean
    RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // Use JDK serialization for values to handle protobuf, Avro, etc.
        org.springframework.data.redis.serializer.JdkSerializationRedisSerializer jdkSerializer =
            new org.springframework.data.redis.serializer.JdkSerializationRedisSerializer();

        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(jdkSerializer);
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(jdkSerializer);

        return template;
    }

    /**
     * Creates cache manager with Redis support and graceful fallback.
     */
    @Bean
    @Primary
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        try {
            connectionFactory.getConnection().ping();
            log.info("Redis connection successful - using RedisCacheManager");

            // Use JDK serialization for complex objects (protobuf, Avro, etc.)
            // This handles all serializable objects including protobuf and Avro
            org.springframework.data.redis.serializer.JdkSerializationRedisSerializer jdkSerializer =
                new org.springframework.data.redis.serializer.JdkSerializationRedisSerializer();

            RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .serializeKeysWith(org.springframework.data.redis.serializer.RedisSerializationContext
                    .SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(org.springframework.data.redis.serializer.RedisSerializationContext
                    .SerializationPair.fromSerializer(jdkSerializer))
                .entryTtl(Duration.ofSeconds(FIVE_MINUTES))
                .disableCachingNullValues();

            return RedisCacheManager.RedisCacheManagerBuilder
                .fromConnectionFactory(connectionFactory)
                .withCacheConfiguration("propertyDetails",
                    defaultConfig.entryTtl(Duration.ofSeconds(FIVE_MINUTES))
                )
                .withCacheConfiguration("propertyCurrencies",
                    defaultConfig.entryTtl(Duration.ofSeconds(ONE_HOUR))
                )
                .withCacheConfiguration("propertyCurrencyFormats",
                    defaultConfig.entryTtl(Duration.ofSeconds(ONE_HOUR))
                )
                .withCacheConfiguration("marketplaceIntegrations",
                    defaultConfig.entryTtl(Duration.ofSeconds(FIVE_MINUTES))
                )
                .withCacheConfiguration("guestDetails",
                    defaultConfig.entryTtl(Duration.ofSeconds(ONE_MINUTE))
                )
                .withCacheConfiguration("fiscalDocumentResponses",
                    defaultConfig.entryTtl(Duration.ofSeconds(ONE_MINUTE))
                )
                .cacheDefaults(defaultConfig)
                .build();
        } catch (Exception e) {
            log.warn("Redis connection failed - falling back to NoOpCacheManager: {}", e.getMessage());
            return new NoOpCacheManager();
        }
    }
}
