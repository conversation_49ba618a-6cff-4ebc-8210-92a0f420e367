package com.cloudbeds.fiscaldocument.utils;

import com.samskivert.mustache.Mustache;
import java.io.StringWriter;
import java.util.Map;

public class TemplateRenderer {

    /**
     * Render a template with the given values.
     *
     * @param template template content
     * @param values values to render
     * @return rendered template
     */
    public static String renderTemplate(String template, Map<String, Object> values) {
        var compiler = Mustache.compiler();
        StringWriter writer = new StringWriter();
        compiler.compile(template).execute(values, writer);

        return writer.toString();
    }
}