package com.cloudbeds.fiscaldocument.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.qrcode.QRCodeWriter;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import javax.imageio.ImageIO;

public class QrCodeGenerator {

    /**
     * Generates a QR code image as a Base64 encoded string.
     *
     * @param qrCodeText qrCodeText
     * @param size size
     * @return String
     * @throws IOException IOException
     * @throws WriterException WriterException
     */
    public static String getCodeAsBase64Image(String qrCodeText, int size) throws IOException, WriterException {
        var image = createQrCodeImage(qrCodeText, size);
        var byteArrayOutputStream = new ByteArrayOutputStream();

        ImageIO.write(image, "PNG", byteArrayOutputStream);

        var imageBytes = byteArrayOutputStream.toByteArray();

        return Base64.getEncoder().encodeToString(imageBytes);
    }

    /**
     * Creates a QR code image.
     *
     * @param qrCodeText qrCodeText
     * @param size size
     * @return BufferedImage
     * @throws WriterException WriterException
     */
    public static BufferedImage createQrCodeImage(String qrCodeText, int size) throws WriterException {
        var image = new BufferedImage(size, size, BufferedImage.TYPE_INT_RGB);
        image.createGraphics();

        var graphics = (Graphics2D) image.getGraphics();
        graphics.setColor(Color.WHITE);
        graphics.fillRect(0, 0, size, size);
        graphics.setColor(Color.BLACK);

        var qrCodeWriter = new QRCodeWriter();
        var bitMatrix = qrCodeWriter.encode(qrCodeText, BarcodeFormat.QR_CODE, size, size);
        for (int i = 0; i < size; i++) {
            for (int j = 0; j < size; j++) {
                if (bitMatrix.get(i, j)) {
                    graphics.fillRect(i, j, 1, 1);
                }
            }
        }

        graphics.dispose();
        return image;
    }
}