package com.cloudbeds.fiscaldocument.utils.constants;

import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import java.util.Map;

public class DocumentTemplateLists {

    public static final Map<DocumentKind, DocumentTemplates> SPAIN_DOCUMENT_TEMPLATES = Map.of(
        DocumentKind.INVOICE, new DocumentTemplates(
            "templates/es/reservation_invoice.html",
            "templates/es/reservation_invoice_compacted.html",
            "templates/es/group_profile_invoice.html",
            "templates/es/group_profile_invoice_compacted.html"
        ),
        DocumentKind.RECTIFY_INVOICE, new DocumentTemplates(
            "templates/es/reservation_rectify_invoice.html",
            "templates/es/reservation_rectify_invoice_compacted.html",
            "templates/es/group_profile_rectify_invoice.html",
            "templates/es/group_profile_rectify_invoice_compacted.html"
        ),
        DocumentKind.RECEIPT, new DocumentTemplates(
            "templates/es/reservation_receipt.html",
            "templates/es/reservation_receipt_compacted.html",
            "templates/es/group_profile_receipt.html",
            "templates/es/group_profile_receipt_compacted.html"
        )
    );

    public static final Map<DocumentKind, DocumentTemplates> DEFAULT_DOCUMENT_TEMPLATES = Map.of(
        DocumentKind.INVOICE, new DocumentTemplates(
            "templates/reservation_invoice.html",
            "templates/reservation_invoice_compacted.html",
            "templates/group_profile_invoice.html",
            "templates/group_profile_invoice_compacted.html"
        ),
        DocumentKind.CREDIT_NOTE, new DocumentTemplates(
            "templates/reservation_credit_note.html",
            "templates/reservation_credit_note_compacted.html",
            "templates/group_profile_credit_note.html",
            "templates/group_profile_credit_note_compacted.html"
        ),
        DocumentKind.RECEIPT, new DocumentTemplates(
            "templates/reservation_receipt.html",
            "templates/reservation_receipt_compacted.html",
            "templates/group_profile_receipt.html",
            "templates/group_profile_receipt_compacted.html"
        )
    );

    /**
     * Returns the default template based on the source kind, document kind, compact flag, and country code.
     *
     * @param sourceKind The source kind (e.g., RESERVATION or GROUP).
     * @param documentKind The document kind (e.g., INVOICE, CREDIT_NOTE, RECEIPT).
     * @param isCompact Whether to return the compact version of the template.
     * @param countryCode The country code to determine if Spain-specific templates should be used.
     * @return The path to the default template.
     */
    public static String getDefaultTemplate(
        SourceKind sourceKind,
        DocumentKind documentKind,
        boolean isCompact,
        String countryCode
    ) {
        DocumentTemplates templates;
        if (CountryCodes.SPAIN.getCode().equals(countryCode.toLowerCase())) {
            templates = SPAIN_DOCUMENT_TEMPLATES.get(documentKind);
        } else {
            templates = DEFAULT_DOCUMENT_TEMPLATES.get(documentKind);
        }
        if (isCompact) {
            if (sourceKind.equals(SourceKind.RESERVATION)) {
                return templates.reservationCompactTemplate();
            } else {
                return templates.groupCompactTemplate();
            }
        } else {
            if (sourceKind.equals(SourceKind.RESERVATION)) {
                return templates.reservationTemplate();
            } else {
                return templates.groupTemplate();
            }
        }
    }



    public record DocumentTemplates(
        String reservationTemplate,
        String reservationCompactTemplate,
        String groupTemplate,
        String groupCompactTemplate
    ) {}
}
