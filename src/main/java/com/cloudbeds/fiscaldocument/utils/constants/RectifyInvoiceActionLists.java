package com.cloudbeds.fiscaldocument.utils.constants;

import com.cloudbeds.fiscaldocument.enums.DocumentAction;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import java.util.List;
import java.util.Map;

public class RectifyInvoiceActionLists {
    public static final Map<DocumentStatus, Map<String, List<DocumentAction>>> ACTIONS_PER_COUNTRY = Map.of(
        DocumentStatus.COMPLETED,
        Map.of("es",
            List.of(
                DocumentAction.ADD_PAYMENT,
                DocumentAction.DOWNLOAD
            )
        ),
        DocumentStatus.OPEN,
        Map.of("es",
            List.of(
                DocumentAction.ADD_PAYMENT,
                DocumentAction.DOWNLOAD
            )
        ),
        DocumentStatus.PAID,
        Map.of("es",
            List.of(
                DocumentAction.DOWNLOAD
            )
        ),
        DocumentStatus.CANCELED, Map.of(),
        DocumentStatus.VOIDED, Map.of(),
        DocumentStatus.REQUESTED, Map.of(),
        DocumentStatus.VOID_REQUESTED, Map.of(),
        DocumentStatus.FAILED, Map.of(),
        DocumentStatus.MANUALLY_RECONCILED, Map.of(),
        DocumentStatus.REJECTED, Map.of()
    );

    public static final Map<DocumentStatus, List<DocumentAction>> DEFAULT_ACTIONS = Map.of(
        DocumentStatus.COMPLETED, List.of(
            DocumentAction.ADD_PAYMENT,
            DocumentAction.DOWNLOAD
        ),
        DocumentStatus.OPEN, List.of(
            DocumentAction.ADD_PAYMENT,
            DocumentAction.DOWNLOAD
        ),
        DocumentStatus.PAID, List.of(
            DocumentAction.DOWNLOAD
        ),
        DocumentStatus.CANCELED, List.of(
            DocumentAction.DOWNLOAD
        ),
        DocumentStatus.VOIDED, List.of(
            DocumentAction.DOWNLOAD
        ),
        DocumentStatus.REQUESTED, List.of(),
        DocumentStatus.VOID_REQUESTED, List.of(
            DocumentAction.DOWNLOAD
        ),
        DocumentStatus.FAILED, List.of(),
        DocumentStatus.MANUALLY_RECONCILED, List.of(),
        DocumentStatus.REJECTED, List.of()
    );
}
