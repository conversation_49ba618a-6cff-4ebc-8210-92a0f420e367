package com.cloudbeds.fiscaldocument.utils.constants;

import com.cloudbeds.fiscaldocument.enums.DocumentAction;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import java.util.List;
import java.util.Map;

public class InvoiceActionLists {
    public static final Map<DocumentStatus, Map<String, List<DocumentAction>>> ACTIONS_PER_COUNTRY = Map.ofEntries(
        Map.entry(
            DocumentStatus.COMPLETED,
            Map.of("es",
                List.of(
                    DocumentAction.RECTIFY,
                    DocumentAction.ADD_PAYMENT,
                    DocumentAction.DOWNLOAD
                )
            )
        ),
        Map.entry(
            DocumentStatus.OPEN,
            Map.of("es",
                List.of(
                    DocumentAction.RECTIFY,
                    DocumentAction.ADD_PAYMENT,
                    DocumentAction.DOWNLOAD
                )
            )
        ),
        Map.entry(
            DocumentStatus.PAID,
            Map.of("es",
                List.of(
                    DocumentAction.RECTIFY,
                    DocumentAction.DOWNLOAD
                )
            )
        ),
        Map.entry(
            DocumentStatus.CANCELED, Map.of()
        ),
        Map.entry(
            DocumentStatus.VOIDED, Map.of()
        ),
        Map.entry(
            DocumentStatus.REQUESTED, Map.of()
        ),
        Map.entry(
            DocumentStatus.VOID_REQUESTED, Map.of()
        ),
        Map.entry(
            DocumentStatus.FAILED, Map.of()
        ),
        Map.entry(
            DocumentStatus.MANUALLY_RECONCILED, Map.of()
        ),
        Map.entry(
            DocumentStatus.REJECTED, Map.of()
        ),
        Map.entry(
            DocumentStatus.CORRECTION_NEEDED,
            Map.of("es",
                List.of(
                    DocumentAction.RECTIFY,
                    DocumentAction.DOWNLOAD
                )
            )
        )
    );

    public static final Map<DocumentStatus, List<DocumentAction>> DEFAULT_ACTIONS = Map.ofEntries(
        Map.entry(
            DocumentStatus.COMPLETED, List.of(
                DocumentAction.CREDIT_NOTE,
                DocumentAction.ADD_PAYMENT,
                DocumentAction.DOWNLOAD
            )
        ),
        Map.entry(
            DocumentStatus.OPEN, List.of(
                DocumentAction.CREDIT_NOTE,
                DocumentAction.ADD_PAYMENT,
                DocumentAction.DOWNLOAD
            )
        ),
        Map.entry(
            DocumentStatus.CORRECTION_NEEDED, List.of(
                DocumentAction.CREDIT_NOTE,
                DocumentAction.DOWNLOAD
            )
        ),
        Map.entry(
            DocumentStatus.PAID, List.of(
                DocumentAction.CREDIT_NOTE,
                DocumentAction.DOWNLOAD
            )
        ),
        Map.entry(
            DocumentStatus.CANCELED, List.of(
                DocumentAction.DOWNLOAD
            )
        ),
        Map.entry(
            DocumentStatus.VOIDED, List.of(
                DocumentAction.DOWNLOAD
            )
        ),
        Map.entry(
            DocumentStatus.REQUESTED, List.of()
        ),
        Map.entry(
            DocumentStatus.VOID_REQUESTED, List.of(
                DocumentAction.DOWNLOAD
            )
        ),
        Map.entry(
            DocumentStatus.FAILED, List.of()
        ),
        Map.entry(
            DocumentStatus.MANUALLY_RECONCILED, List.of()
        ),
        Map.entry(
            DocumentStatus.REJECTED, List.of()
        )
    );
}
