package com.cloudbeds.fiscaldocument.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import org.springframework.stereotype.Component;

@Component
public class DateTimeService {
    /**
     * Get the local date.
     *
     * @return current time in UTC as a string
     */
    public LocalDate getLocalDate(ZoneId zoneId) {
        return ZonedDateTime.now().withZoneSameInstant(zoneId).toLocalDate();
    }

    /**
     * Gets local date time as object.
     *
     * @return LocalDateTime object
     */
    public LocalDateTime getCurrentDatetime() {
        return LocalDateTime.now();
    }
}
