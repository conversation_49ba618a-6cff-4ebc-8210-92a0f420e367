package com.cloudbeds.fiscaldocument.utils;

import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import java.io.File;
import java.io.FileOutputStream;
import java.time.Instant;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class PdfBuilder {

    public static File buildPdfFromHtml(String html) {
        return buildPdfFromHtml(html, null, null);
    }

    /**
     * Build a PDF file from HTML content.
     *
     * @param html HTML content
     * @param width page width in millimeters
     * @param height page height in millimeters
     * @return PDF file
     */
    public static File buildPdfFromHtml(String html, Float width, Float height) {
        try {
            var pdfFile = File.createTempFile("generated-" + Instant.now().toEpochMilli(), ".pdf");

            try (var os = new FileOutputStream(pdfFile)) {
                PdfRendererBuilder builder = new PdfRendererBuilder();
                builder.withHtmlContent(html, null);
                if (width != null && height != null) {
                    builder.useDefaultPageSize(width, height, PdfRendererBuilder.PageSizeUnits.MM);
                }
                builder.toStream(os);
                builder.run();
            } catch (Exception e) {
                log.error("Error building PDF", e);
                throw new FiscalDocumentException(ErrorCode.UNEXPECTED_ERROR, "Error building PDF");
            }
            return pdfFile;
        } catch (Exception e) {
            log.error("Error building PDF", e);
            throw new FiscalDocumentException(ErrorCode.UNEXPECTED_ERROR, "Error building PDF");
        }
    }
}