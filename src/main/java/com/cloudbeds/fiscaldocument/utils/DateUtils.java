package com.cloudbeds.fiscaldocument.utils;

import com.google.protobuf.Timestamp;
import com.google.type.Date;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public class DateUtils {
    public static LocalDate toLocalDate(Date date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }

    public static String formatProtobufTimestamp(Timestamp timestamp, DateTimeFormatter dateFormatter, ZoneId zoneId) {
        return Instant.ofEpochSecond(timestamp.getSeconds())
            .atZone(zoneId).format(dateFormatter);
    }

    public static String formatProtobufDate(Date date, DateTimeFormatter dateFormatter) {
        return toLocalDate(date).format(dateFormatter);
    }
}
