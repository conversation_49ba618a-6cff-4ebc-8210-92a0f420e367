package com.cloudbeds.fiscaldocument.utils;

import com.cloudbeds.FiscalDocumentService.CreditNoteMethod;
import com.cloudbeds.FiscalDocumentService.Method;
import com.cloudbeds.FiscalDocumentService.RecipientType;
import com.cloudbeds.FiscalDocumentService.SourceKind;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus;
import com.cloudbeds.fiscaldocument.entity.Recipient;
import com.cloudbeds.fiscaldocument.enums.CreationMethod;
import com.cloudbeds.fiscaldocument.enums.DocumentAction;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import com.cloudbeds.fiscaldocument.models.FiscalDocumentCdcModel;

public class EnumConverterUtil {

    /**
     * Convert DocumentKind to FiscalDocumentKind.
     *
     * @param documentKind DocumentKind
     * @return FiscalDocumentKind
     */
    public static FiscalDocumentKind convert(DocumentKind documentKind) {
        if (documentKind == null) {
            return null;
        }

        return switch (documentKind) {
            case INVOICE -> com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind.INVOICE;
            case CREDIT_NOTE -> com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind.CREDIT_NOTE;
            case RECEIPT -> com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind.RECEIPT;
            case RECTIFY_INVOICE -> com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind.RECTIFY_INVOICE;
        };
    }

    /**
     * Convert FiscalDocumentKind to DocumentKind.
     *
     * @param fiscalDocumentKind FiscalDocumentKind
     * @return DocumentKind
     */
    public static DocumentKind convert(FiscalDocumentKind fiscalDocumentKind) {
        if (fiscalDocumentKind == null) {
            return null;
        }

        return switch (fiscalDocumentKind) {
            case INVOICE -> DocumentKind.INVOICE;
            case CREDIT_NOTE -> DocumentKind.CREDIT_NOTE;
            case RECEIPT -> DocumentKind.RECEIPT;
            case RECTIFY_INVOICE -> DocumentKind.RECTIFY_INVOICE;
        };
    }

    /**
     * Convert DocumentStatus to FiscalDocumentStatus.
     *
     * @param status status
     * @return FiscalDocumentStatus
     */
    public static com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus convert(DocumentStatus status) {
        if (status == null) {
            return null;
        }

        return switch (status) {
            case COMPLETED -> FiscalDocumentStatus.COMPLETED;
            case VOIDED -> FiscalDocumentStatus.VOIDED;
            case PENDING_INTEGRATION -> FiscalDocumentStatus.PENDING_INTEGRATION;
            case COMPLETED_INTEGRATION -> FiscalDocumentStatus.COMPLETED_INTEGRATION;
            case FAILED_INTEGRATION -> FiscalDocumentStatus.FAILED_INTEGRATION;
            case CORRECTION_NEEDED -> FiscalDocumentStatus.CORRECTION_NEEDED;
            case PAID -> FiscalDocumentStatus.PAID;
            case CANCELED -> FiscalDocumentStatus.CANCELED;
            case OPEN -> FiscalDocumentStatus.OPEN;
            case REQUESTED -> FiscalDocumentStatus.REQUESTED;
            case VOID_REQUESTED -> FiscalDocumentStatus.VOID_REQUESTED;
            case FAILED -> FiscalDocumentStatus.FAILED;
            case MANUALLY_RECONCILED -> FiscalDocumentStatus.MANUALLY_RECONCILED;
            case REJECTED -> FiscalDocumentStatus.REJECTED;
        };
    }


    /**
     * Convert FiscalDocumentStatus to DocumentStatus.
     *
     * @param status status
     * @return DocumentStatus
     */
    public static DocumentStatus convert(com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentStatus status) {
        if (status == null) {
            return null;
        }

        return switch (status) {
            case COMPLETED -> DocumentStatus.COMPLETED;
            case VOIDED -> DocumentStatus.VOIDED;
            case PAID -> DocumentStatus.PAID;
            case PENDING_INTEGRATION -> DocumentStatus.PENDING_INTEGRATION;
            case FAILED_INTEGRATION -> DocumentStatus.FAILED_INTEGRATION;
            case CORRECTION_NEEDED -> DocumentStatus.CORRECTION_NEEDED;
            case CANCELED -> DocumentStatus.CANCELED;
            case OPEN -> DocumentStatus.OPEN;
            case REQUESTED -> DocumentStatus.REQUESTED;
            case VOID_REQUESTED -> DocumentStatus.VOID_REQUESTED;
            case COMPLETED_INTEGRATION -> DocumentStatus.COMPLETED_INTEGRATION;
            case FAILED -> DocumentStatus.FAILED;
            case MANUALLY_RECONCILED -> DocumentStatus.MANUALLY_RECONCILED;
            case REJECTED -> DocumentStatus.REJECTED;
        };
    }

    /**
     * Convert SourceKind to com.cloudbeds.fiscaldocument.enums.SourceKind.
     *
     * @param sourceKind SourceKind
     * @return com.cloudbeds.fiscaldocument.enums.SourceKind
     */
    public static SourceKind convert(com.cloudbeds.fiscaldocument.controller.model.SourceKind sourceKind) {
        if (sourceKind == null) {
            return null;
        }

        return switch (sourceKind) {
            case GROUP_PROFILE -> SourceKind.GROUP_PROFILE;
            case RESERVATION -> SourceKind.RESERVATION;
            case HOUSE_ACCOUNT -> SourceKind.HOUSE_ACCOUNT;
            case ACCOUNTS_RECEIVABLE_LEDGER -> SourceKind.ACCOUNTS_RECEIVABLE_LEDGER;
        };
    }

    /**
     * Convert FiscalDocumentCdcModel.CdcDocumentStatus to DocumentStatus.
     *
     * @param status status
     * @return DocumentStatus
     */
    public static DocumentStatus convert(FiscalDocumentCdcModel.CdcDocumentStatus status) {
        if (status == null) {
            return null;
        }

        return switch (status) {
            case OPEN -> DocumentStatus.OPEN;
            case PAID -> DocumentStatus.PAID;
            case VOIDED -> DocumentStatus.VOIDED;
            case CANCELED -> DocumentStatus.CANCELED;
            case REQUESTED -> DocumentStatus.REQUESTED;
            case VOID_REQUESTED -> DocumentStatus.VOID_REQUESTED;
            case FAILED -> DocumentStatus.FAILED;
            case MANUALLY_RECONCILED -> DocumentStatus.MANUALLY_RECONCILED;
            case REJECTED -> DocumentStatus.REJECTED;
        };
    }

    /**
     * Convert com.cloudbeds.fiscaldocument.enums.SourceKind to SourceKind.
     *
     * @param sourceKind sourceKind
     * @return SourceKind
     */
    public static com.cloudbeds.fiscaldocument.enums.SourceKind convert(SourceKind sourceKind) {
        if (sourceKind == null) {
            return null;
        }

        return switch (sourceKind) {
            case GROUP_PROFILE -> com.cloudbeds.fiscaldocument.enums.SourceKind.GROUP_PROFILE;
            case RESERVATION -> com.cloudbeds.fiscaldocument.enums.SourceKind.RESERVATION;
            case HOUSE_ACCOUNT -> com.cloudbeds.fiscaldocument.enums.SourceKind.HOUSE_ACCOUNT;
            case ACCOUNTS_RECEIVABLE_LEDGER -> com.cloudbeds.fiscaldocument.enums.SourceKind.ACCOUNTS_RECEIVABLE_LEDGER;
        };
    }

    /**
     * Convert DocumentAction to com.cloudbeds.fiscaldocument.controller.model.DocumentAction.
     *
     * @param action action
     * @return com.cloudbeds.fiscaldocument.controller.model.DocumentAction
     */
    public static com.cloudbeds.fiscaldocument.controller.model.DocumentAction convert(DocumentAction action) {
        if (action == null) {
            return null;
        }

        return switch (action) {
            case CANCEL -> com.cloudbeds.fiscaldocument.controller.model.DocumentAction.CANCEL;
            case RECTIFY -> com.cloudbeds.fiscaldocument.controller.model.DocumentAction.RECTIFY;
            case DOWNLOAD -> com.cloudbeds.fiscaldocument.controller.model.DocumentAction.DOWNLOAD;
            case CREDIT_NOTE -> com.cloudbeds.fiscaldocument.controller.model.DocumentAction.CREDIT_NOTE;
            case VOID -> com.cloudbeds.fiscaldocument.controller.model.DocumentAction.VOID;
            case ADD_PAYMENT -> com.cloudbeds.fiscaldocument.controller.model.DocumentAction.ADD_PAYMENT;
        };
    }

    /**
     * Convert com.cloudbeds.fiscaldocument.enums.SourceKind to
     * com.cloudbeds.fiscaldocument.controller.model.SourceKind.
     *
     * @param sourceKind sourceKind
     * @return SourceKind
     */
    public static com.cloudbeds.fiscaldocument.controller.model.SourceKind convert(
        com.cloudbeds.fiscaldocument.enums.SourceKind sourceKind
    ) {
        if (sourceKind == null) {
            return null;
        }

        return switch (sourceKind) {
            case GROUP_PROFILE -> com.cloudbeds.fiscaldocument.controller.model.SourceKind.GROUP_PROFILE;
            case RESERVATION -> com.cloudbeds.fiscaldocument.controller.model.SourceKind.RESERVATION;
            case HOUSE_ACCOUNT -> com.cloudbeds.fiscaldocument.controller.model.SourceKind.HOUSE_ACCOUNT;
            case ACCOUNTS_RECEIVABLE_LEDGER ->
                com.cloudbeds.fiscaldocument.controller.model.SourceKind.ACCOUNTS_RECEIVABLE_LEDGER;
        };
    }

    /**
     * Convert RecipientType to Rest RecipientType.
     *
     * @param type FiscalDocumentRecipientType
     * @return rest RecipientType
     */
    public static com.cloudbeds.fiscaldocument.controller.model.RecipientType convert(
        Recipient.RecipientType type
    ) {
        if (type == null) {
            return null;
        }

        return switch (type) {
            case COMPANY -> com.cloudbeds.fiscaldocument.controller.model.RecipientType.COMPANY;
            case PERSON -> com.cloudbeds.fiscaldocument.controller.model.RecipientType.PERSON;
        };
    }

    /**
     * com.cloudbeds.fiscaldocument.controller.model.SourceKind to com.cloudbeds.fiscaldocument.enums.SourceKind.
     *
     * @param sourceKind sourceKind
     * @return com.cloudbeds.fiscaldocument.enums.SourceKind
     */
    public static com.cloudbeds.fiscaldocument.enums.SourceKind convertToEntity(
        com.cloudbeds.fiscaldocument.controller.model.SourceKind sourceKind
    ) {
        if (sourceKind == null) {
            return null;
        }

        return switch (sourceKind) {
            case GROUP_PROFILE -> com.cloudbeds.fiscaldocument.enums.SourceKind.GROUP_PROFILE;
            case RESERVATION -> com.cloudbeds.fiscaldocument.enums.SourceKind.RESERVATION;
            case HOUSE_ACCOUNT -> com.cloudbeds.fiscaldocument.enums.SourceKind.HOUSE_ACCOUNT;
            case ACCOUNTS_RECEIVABLE_LEDGER -> com.cloudbeds.fiscaldocument.enums.SourceKind.ACCOUNTS_RECEIVABLE_LEDGER;
        };
    }

    /**
     * Convert Rest CreationMethod to CreationMethod.
     *
     * @param method method
     * @return CreationMethod
     */
    public static CreationMethod convertToEntity(com.cloudbeds.fiscaldocument.controller.model.CreationMethod method) {
        if (method == null) {
            return null;
        }

        return switch (method) {
            case VOID -> CreationMethod.VOID;
            case ADJUSTMENT -> CreationMethod.ADJUSTMENT;
        };
    }

    /**
     * com.cloudbeds.fiscaldocument.enums.SourceKind to com.cloudbeds.fiscaldocument.controller.model.SourceKind.
     *
     * @param sourceKind sourceKind
     * @return com.cloudbeds.fiscaldocument.controller.model.SourceKind
     */
    public static SourceKind convertToAvro(com.cloudbeds.fiscaldocument.enums.SourceKind sourceKind) {
        if (sourceKind == null) {
            return null;
        }

        return switch (sourceKind) {
            case GROUP_PROFILE -> SourceKind.GROUP_PROFILE;
            case RESERVATION -> SourceKind.RESERVATION;
            case HOUSE_ACCOUNT -> SourceKind.HOUSE_ACCOUNT;
            case ACCOUNTS_RECEIVABLE_LEDGER -> SourceKind.ACCOUNTS_RECEIVABLE_LEDGER;
        };
    }

    /**
     * Convert DocumentStatus to FiscalDocumentStatus.
     *
     * @param status status
     * @return FiscalDocumentStatus
     */
    public static com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus convertToAvro(DocumentStatus status) {
        if (status == null) {
            return null;
        }

        return switch (status) {
            case COMPLETED -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.COMPLETED;
            case VOIDED -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.VOIDED;
            case PAID -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.PAID;
            case CANCELED -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.CANCELED;
            case OPEN -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.OPEN;
            case CORRECTION_NEEDED -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.CORRECTION_NEEDED;
            case FAILED_INTEGRATION -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.FAILED_INTEGRATION;
            case COMPLETED_INTEGRATION ->
                com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.COMPLETED_INTEGRATION;
            case REQUESTED -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.REQUESTED;
            case VOID_REQUESTED -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.VOID_REQUESTED;
            case FAILED -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.FAILED;
            case MANUALLY_RECONCILED -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.MANUALLY_RECONCILED;
            case REJECTED -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.REJECTED;
            case PENDING_INTEGRATION -> com.cloudbeds.FiscalDocumentService.FiscalDocumentStatus.PENDING_INTEGRATION;
        };
    }

    /**
     * Convert DocumentKind to FiscalDocumentKind.
     *
     * @param documentKind DocumentKind
     * @return FiscalDocumentKind
     */
    public static com.cloudbeds.FiscalDocumentService.FiscalDocumentKind convertToAvro(DocumentKind documentKind) {
        if (documentKind == null) {
            return null;
        }

        return switch (documentKind) {
            case INVOICE -> com.cloudbeds.FiscalDocumentService.FiscalDocumentKind.INVOICE;
            case CREDIT_NOTE -> com.cloudbeds.FiscalDocumentService.FiscalDocumentKind.CREDIT_NOTE;
            case RECEIPT -> com.cloudbeds.FiscalDocumentService.FiscalDocumentKind.RECEIPT;
            case RECTIFY_INVOICE -> com.cloudbeds.FiscalDocumentService.FiscalDocumentKind.RECTIFY_INVOICE;
        };
    }

    /**
     * Convert Recipient.RecipientType enum to avro RecipientType.
     *
     * @param recipientType Recipient.RecipientType
     * @return avro RecipientType
     */
    public static RecipientType convertToAvro(Recipient.RecipientType recipientType) {
        if (recipientType == null) {
            return null;
        }

        return switch (recipientType) {
            case COMPANY -> RecipientType.COMPANY;
            case PERSON -> RecipientType.PERSON;
        };
    }

    /**
     * Convert CreationMethod to avro Method.
     *
     * @param method CreationMethod
     * @return avro Method
     */
    public static Method convertToAvro(CreationMethod method) {
        if (method == null) {
            return null;
        }

        return switch (method) {
            case VOID -> Method.VOID;
            case ADJUSTMENT -> Method.ADJUSTMENT;
        };
    }

    /**
     * Convert CreationMethod to avro CreditNoteMethod.
     *
     * @param method CreationMethod
     * @return avro CreditNoteMethod
     */
    public static CreditNoteMethod convertToAvroCreditNote(CreationMethod method) {
        if (method == null) {
            return null;
        }

        return switch (method) {
            case VOID -> CreditNoteMethod.VOID;
            case ADJUSTMENT -> CreditNoteMethod.ADJUSTMENT;
        };
    }
}
