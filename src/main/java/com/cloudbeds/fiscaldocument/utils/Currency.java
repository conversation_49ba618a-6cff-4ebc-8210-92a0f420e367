package com.cloudbeds.fiscaldocument.utils;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum Currency {
    AED("AED", 2),
    AFN("AFN", 2),
    EEK("EEK", 2),
    ALL("ALL", 2),
    AMD("AMD", 2),
    ANG("ANG", 2),
    AOA("AOA", 2),
    ARS("ARS", 2),
    AUD("AUD", 2),
    AWG("AWG", 2),
    AZN("AZN", 2),
    BAM("BAM", 2),
    BBD("BBD", 2),
    BDT("BDT", 2),
    BGN("BGN", 2),
    BHD("BHD", 3),
    BMD("BMD", 2),
    BND("BND", 2),
    BOB("BOB", 2),
    BRL("BRL", 2),
    BSD("BSD", 2),
    BWP("BWP", 2),
    BYN("BYN", 2),
    BZD("BZD", 2),
    CAD("CAD", 2),
    CHF("CHF", 2),
    CLP("CLP", 0),
    CLF("CLF", 4),
    CNY("CNY", 2),
    COP("COP", 2),
    CRC("CRC", 2),
    CUP("CUP", 2),
    CVE("CVE", 2),
    CZK("CZK", 2),
    DJF("DJF", 0),
    DKK("DKK", 2),
    DOP("DOP", 2),
    DZD("DZD", 2),
    EGP("EGP", 2),
    ETB("ETB", 2),
    EUR("EUR", 2),
    FJD("FJD", 2),
    FKP("FKP", 2),
    GBP("GBP", 2),
    GEL("GEL", 2),
    GHS("GHS", 2),
    GIP("GIP", 2),
    GMD("GMD", 2),
    GNF("GNF", 0),
    GTQ("GTQ", 2),
    GYD("GYD", 2),
    HKD("HKD", 2),
    HNL("HNL", 2),
    HTG("HTG", 2),
    HUF("HUF", 2),
    IDR("IDR", 2),
    ILS("ILS", 2),
    INR("INR", 2),
    IQD("IQD", 3),
    ISK("ISK", 0),
    JMD("JMD", 2),
    JOD("JOD", 3),
    JPY("JPY", 0),
    KES("KES", 2),
    KGS("KGS", 2),
    KHR("KHR", 2),
    KMF("KMF", 0),
    KRW("KRW", 0),
    KWD("KWD", 3),
    KYD("KYD", 2),
    KZT("KZT", 2),
    LAK("LAK", 2),
    LBP("LBP", 2),
    LKR("LKR", 2),
    LYD("LYD", 3),
    MAD("MAD", 2),
    MDL("MDL", 2),
    MKD("MKD", 2),
    MMK("MMK", 2),
    MNT("MNT", 2),
    MOP("MOP", 2),
    MRU("MRU", 2),
    MUR("MUR", 2),
    MVR("MVR", 2),
    MWK("MWK", 2),
    MXN("MXN", 2),
    MYR("MYR", 2),
    MZN("MZN", 2),
    NAD("NAD", 2),
    NGN("NGN", 2),
    NIO("NIO", 2),
    NOK("NOK", 2),
    NPR("NPR", 2),
    NZD("NZD", 2),
    OMR("OMR", 3),
    PAB("PAB", 2),
    PEN("PEN", 2),
    PGK("PGK", 2),
    PHP("PHP", 2),
    PKR("PKR", 2),
    PLN("PLN", 2),
    PYG("PYG", 0),
    QAR("QAR", 2),
    RON("RON", 2),
    RSD("RSD", 2),
    RUB("RUB", 2),
    RWF("RWF", 0),
    SAR("SAR", 2),
    SBD("SBD", 2),
    SCR("SCR", 2),
    SEK("SEK", 2),
    SGD("SGD", 2),
    SHP("SHP", 2),
    SLE("SLE", 2),
    SOS("SOS", 2),
    SRD("SRD", 2),
    STN("STN", 2),
    SVC("SVC", 2),
    SZL("SZL", 2),
    THB("THB", 2),
    TND("TND", 3),
    TOP("TOP", 2),
    TRY("TRY", 2),
    TTD("TTD", 2),
    TWD("TWD", 2),
    TZS("TZS", 2),
    UAH("UAH", 2),
    UGX("UGX", 0),
    USD("USD", 2),
    UYU("UYU", 2),
    UZS("UZS", 2),
    VEF("VEF", 2),
    VES("VES", 2),
    VND("VND", 0),
    VUV("VUV", 0),
    WST("WST", 2),
    XAF("XAF", 0),
    XCD("XCD", 2),
    XOF("XOF", 0),
    XPF("XPF", 0),
    YER("YER", 2),
    ZAR("ZAR", 2),
    ZMW("ZMW", 2),
    //MFD extra currencies
    STD("STD", 2),
    LSL("LSL", 2),
    BIF("BIF", 0),
    ZWD("ZWD", 2),
    CDF("CDF", 2),
    ZMK("ZMK", 2),
    ADP("ADP", 2),
    ROL("ROL", 2),
    SKK("SKK", 2),
    IMP("IMP", 2),
    SDP("SDP", 2),
    JEP("JEP", 2),
    KPW("KPW", 2),
    GHC("GHC", 2),
    LVL("LVL", 2),
    LRD("LRD", 2),
    BYR("BYR", 2),
    BTC("BTC", 2),
    HRK("HRK", 2),
    MZM("MZM", 2),
    TRL("TRL", 2),
    SRG("SRG", 2),
    YUN("YUN", 2),
    BTN("BTN", 2),
    IRR("IRR", 2),
    AZM("AZM", 2),
    SDG("SDG", 2),
    ZWL("ZWL", 2),
    ERN("ERN", 2),
    SDD("SDD", 2),
    AON("AON", 2),
    CUC("CUC", 2),
    MGA("MGA", 2),
    ECS("ECS", 2),
    ADF("ADF", 2),
    TMM("TMM", 2),
    GGP("GGP", 2),
    LTL("LTL", 2),
    SLL("SLL", 2),
    MRO("MRO", 2),
    MGF("MGF", 2),
    TJS("TJS", 2),
    TMT("TMT", 2),
    SYP("SYP", 2),
    //Historical
    AFA("AFA", 2),
    ATS("ATS", 2),
    BEF("BEF", 2),
    CYP("CYP", 2),
    DEM("DEM", 2),
    ESP("ESP", 2),
    FIM("FIM", 2),
    FRF("FRF", 2),
    GRD("GRD", 2),
    IEP("IEP", 2),
    ITL("ITL", 2),
    LUF("LUF", 2),
    MTL("MTL", 2),
    NLG("NLG", 2),
    PTE("PTE", 2),
    SIT("SIT", 2),
    XCG("XCG", 2),
    SSP("SSP", 2),
    N_A("N/A", 2);

    private final String isoCode;
    private final int scale;

}
