package com.cloudbeds.fiscaldocument.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.Locale;

public class MoneyUtils {
    /**
     * Format amount to BigDecimal.
     *
     * @param amount amount
     * @param currency currency
     * @return Big decimal
     */
    public static BigDecimal applyCurrencyScale(Long amount, Currency currency) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(amount, currency.getScale());
    }

    /**
     * Formats the specified amount according to the given currency settings.
     *
     * @param amount the amount to format
     * @param currencyCode the currency code to use for formatting
     * @param currencyFormat the custom currency format settings
     * @return the formatted amount as a string
     */
    public static String formatAmount(
        BigDecimal amount,
        String currencyCode,
        CurrencyFormat currencyFormat
    ) {
        String formattedAmount;
        try {
            var currency = java.util.Currency.getInstance(currencyCode);
            var symbols = new DecimalFormatSymbols(Locale.US);

            symbols.setCurrency(currency);

            String pattern = "¤ #,##0.";
            if (currencyFormat != null) {
                symbols.setMonetaryGroupingSeparator(currencyFormat.thousandsSeparator);
                symbols.setMonetaryDecimalSeparator(currencyFormat.decimalSeparator);
            }

            if (currency.getDefaultFractionDigits() > 0) {
                pattern += "0".repeat(currency.getDefaultFractionDigits());
            }

            pattern += "##########";

            DecimalFormat decimalFormat = new DecimalFormat(pattern, symbols);
            decimalFormat.setCurrency(currency);

            formattedAmount = decimalFormat.format(amount);
        } catch (Exception e) {
            formattedAmount = currencyCode + " " + amount;
        }

        return formattedAmount;
    }

    public record CurrencyFormat(Character decimalSeparator, Character thousandsSeparator) {
    }
}
