package com.cloudbeds.fiscaldocument.grpc.mfd;

import com.cloudbeds.currency.v1.GetPropertyCurrencyFormatRequest;
import com.cloudbeds.currency.v1.ListPropertyActiveCurrencyRatesByPropertyIdRequest;
import com.cloudbeds.currency.v1.ListPropertyCurrenciesByPropertyIdRequest;
import com.cloudbeds.currency.v1.PropertyActiveCurrencyRate;
import com.cloudbeds.currency.v1.PropertyCurrency;
import com.cloudbeds.currency.v1.PropertyCurrencyFormat;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PropertyCurrencyServiceClient {
    private final PropertyCurrencyGrpcClientFactory propertyCurrencyGrpcClientFactory;

    /**
     * Retrieves a list of PropertyCurrency objects for a given property ID.
     *
     * @param propertyId the ID of the property for which to retrieve the list of currencies
     * @return a List of PropertyCurrency objects associated with the specified property
     */
    @Cacheable(cacheNames = {"propertyCurrencies"}, key = "#propertyId")
    public List<PropertyCurrency> listPropertyCurrencies(Long propertyId) {
        var request = ListPropertyCurrenciesByPropertyIdRequest.newBuilder()
            .setPropertyId(propertyId)
            .build();

        var response = propertyCurrencyGrpcClientFactory.createBlockingStub()
            .listPropertyCurrenciesByPropertyId(request);

        return response.getPropertyCurrencyList();
    }


    /**
     * Retrieves a list of active currency rates associated with a specific property.
     *
     * @param propertyId The identifier of the property for which to retrieve the active currency rates.
     * @return A response containing the list of active currency rates associated with the specified property.
     */
    public List<PropertyActiveCurrencyRate> listPropertyActiveCurrencyRatesByPropertyId(
        Long propertyId
    ) {
        return propertyCurrencyGrpcClientFactory.createBlockingStub()
            .listPropertyActiveCurrencyRatesByPropertyId(
                ListPropertyActiveCurrencyRatesByPropertyIdRequest.newBuilder()
                    .setPropertyId(propertyId)
                    .build())
            .getPropertyCurrencyRateList();
    }

    /**
     * Retrieves the currency format associated with a specific property.
     *
     * @param propertyId The identifier of the property for which to retrieve the currency format.
     * @return A response containing the currency format associated with the specified property.
     */
    @Cacheable(cacheNames = {"propertyCurrencyFormats"}, key = "#propertyId")
    public PropertyCurrencyFormat getPropertyCurrencyFormat(Long propertyId) {
        return propertyCurrencyGrpcClientFactory.createBlockingStub()
            .getPropertyCurrencyFormat(
                GetPropertyCurrencyFormatRequest.newBuilder()
                    .setPropertyId(propertyId)
                    .build()).getCurrencyFormat();
    }
}
