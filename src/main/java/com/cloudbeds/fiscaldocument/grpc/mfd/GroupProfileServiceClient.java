package com.cloudbeds.fiscaldocument.grpc.mfd;

import com.cloudbeds.group.v1.GroupProfile;
import com.cloudbeds.group.v1.GroupProfileContact;
import com.cloudbeds.group.v1.ListContactByIdRequest;
import com.cloudbeds.group.v1.ListGroupsRequest;
import com.cloudbeds.group.v1.ListGroupsRequest.FieldFilter;
import com.cloudbeds.group.v1.ListGroupsRequest.FieldFilter.Field;
import com.cloudbeds.group.v1.ListGroupsRequest.Filter;
import com.cloudbeds.rpc.v1.ConditionOperator;
import com.cloudbeds.type.v1.ListValue;
import com.cloudbeds.type.v1.Value;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class GroupProfileServiceClient {
    private final GroupProfileServiceGrpcClientFactory groupProfileServiceGrpcClientFactory;

    /**
     * Retrieves group profiles for a given property and list of group IDs.
     *
     * @param propertyId The ID of the property for which group profiles are to be retrieved.
     * @param groupIds   The list of group IDs to filter the group profiles by.
     * @return A list of group profiles matching the provided criteria.
     */
    public List<GroupProfile> listGroups(Long propertyId, Collection<Long> groupIds) {
        var request = ListGroupsRequest.newBuilder()
            .addPropertyIds(propertyId)
            .setFilter(
                Filter.newBuilder()
                    .setFieldFilter(
                        FieldFilter.newBuilder()
                            .setField(Field.FIELD_ID)
                            .setOperator(ConditionOperator.CONDITION_OPERATOR_IN)
                            .setValue(
                                Value.newBuilder()
                                    .setListValue(
                                        ListValue.newBuilder()
                                            .addAllValues(
                                                groupIds.stream()
                                                    .map(id -> Value.newBuilder().setInt64Value(id).build())
                                                    .toList())
                                    ).build()
                            ).build()
                    ).build()
            ).build();

        var response = groupProfileServiceGrpcClientFactory.createBlockingStub().listGroups(request);

        return response.getGroupsList();
    }

    /**
     * Retrieves a group profile contact for the given contact ID.
     *
     * @param id The ID of the contact.
     * @return The corresponding GroupProfileContact object, or null if not found.
     */
    public GroupProfileContact getGroupProfileContactById(Long id) {
        var request = ListContactByIdRequest.newBuilder()
            .setId(id)
            .build();

        var response = groupProfileServiceGrpcClientFactory
            .createBlockingStub()
            .listContactById(request);

        if (!response.hasContact()) {
            return null;
        }

        return response.getContact();
    }
}
