package com.cloudbeds.fiscaldocument.grpc.mfd;

import com.cloudbeds.currency.v1.PropertyCurrencyServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import java.util.concurrent.TimeUnit;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PropertyCurrencyGrpcClientFactory {
    private final ManagedChannel channel;
    private final long shutdownTimeoutMs;

    public PropertyCurrencyGrpcClientFactory(
        @Value("${grpc.services.group-profile.host}") String host,
        @Value("${grpc.services.group-profile.port}") int port,
        @Value("${grpc.services.organization.shutdownTimeoutMs}") long shutdownTimeoutMs
    ) {
        this.channel = ManagedChannelBuilder.forAddress(host, port).useTransportSecurity().build();
        this.shutdownTimeoutMs = shutdownTimeoutMs;
    }

    public PropertyCurrencyServiceGrpc.PropertyCurrencyServiceBlockingStub createBlockingStub() {
        return PropertyCurrencyServiceGrpc.newBlockingStub(channel);
    }

    @PreDestroy
    private void preDestroy() {
        try {
            channel.shutdown().awaitTermination(shutdownTimeoutMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(
                "GroupsServiceGrpcClientFactory error while shutting down GRPC channel in preDestroy method",
                e
            );
        }
    }
}
