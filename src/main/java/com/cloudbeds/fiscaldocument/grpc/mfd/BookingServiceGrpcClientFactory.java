package com.cloudbeds.fiscaldocument.grpc.mfd;

import com.cloudbeds.booking.v1.BookingServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import java.util.concurrent.TimeUnit;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BookingServiceGrpcClientFactory {
    private final ManagedChannel channel;
    private final long shutdownTimeoutMs;

    public BookingServiceGrpcClientFactory(
        @Value("${grpc.services.booking.host}") String host,
        @Value("${grpc.services.booking.port}") int port,
        @Value("${grpc.services.booking.shutdownTimeoutMs}") long shutdownTimeoutMs
    ) {
        this.channel = ManagedChannelBuilder.forAddress(host, port).useTransportSecurity().build();
        this.shutdownTimeoutMs = shutdownTimeoutMs;
    }

    public BookingServiceGrpc.BookingServiceBlockingStub createBlockingStub() {
        return BookingServiceGrpc.newBlockingStub(channel);
    }

    @PreDestroy
    private void preDestroy() {
        try {
            channel.shutdown().awaitTermination(shutdownTimeoutMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(
                "BookingServiceGrpc error while shutting down GRPC channel in preDestroy method",
                e
            );
        }
    }
}
