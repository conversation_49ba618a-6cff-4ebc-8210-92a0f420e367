package com.cloudbeds.fiscaldocument.grpc.mfd;

import com.cloudbeds.booking.v1.BookingWithRooms;
import com.cloudbeds.booking.v1.ListBookingsRequest;
import com.cloudbeds.booking.v1.ListBookingsRequest.FieldFilter;
import com.cloudbeds.booking.v1.ListBookingsRequest.FieldFilter.Field;
import com.cloudbeds.booking.v1.ListBookingsRequest.Filter;
import com.cloudbeds.rpc.v1.ConditionOperator;
import com.cloudbeds.type.v1.ListValue;
import com.cloudbeds.type.v1.Value;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BookingServiceClient {
    private final BookingServiceGrpcClientFactory bookingServiceGrpcClientFactory;

    /**
     * Retrieves group profiles for a given property and list of group IDs.
     *
     * @param propertyId The ID of the property for which group profiles are to be retrieved.
     * @param bookingIds   The list of group IDs to filter the group profiles by.
     * @return A list of group profiles matching the provided criteria.
     */
    public List<BookingWithRooms> listBookings(Long propertyId, Collection<Long> bookingIds) {
        var request = ListBookingsRequest.newBuilder()
            .addPropertyIds(propertyId)
            .setIncludeGuests(true)
            .setIncludeRooms(true)
            .setFilter(
                Filter.newBuilder()
                    .setFieldFilter(
                        FieldFilter.newBuilder()
                            .setField(Field.FIELD_ID)
                            .setOperator(ConditionOperator.CONDITION_OPERATOR_IN)
                            .setValue(
                                Value.newBuilder()
                                    .setListValue(
                                        ListValue.newBuilder()
                                            .addAllValues(
                                                bookingIds.stream()
                                                    .map(id -> Value.newBuilder().setInt64Value(id).build())
                                                    .toList())
                                    ).build()
                            ).build()
                    ).build()
            ).build();

        var response = bookingServiceGrpcClientFactory.createBlockingStub().listBookings(request);

        return response.getBookingsList();
    }
}
