package com.cloudbeds.fiscaldocument.grpc.organization;

import com.cloudbeds.organization.v1.PropertyServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import java.util.concurrent.TimeUnit;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrganizationServiceGrpcClientFactory {
    private final ManagedChannel channel;
    private final long shutdownTimeoutMs;

    public OrganizationServiceGrpcClientFactory(
        @Value("${grpc.services.organization.host}") String host,
        @Value("${grpc.services.organization.port}") int port,
        @Value("${grpc.services.organization.shutdownTimeoutMs}") long shutdownTimeoutMs
    ) {
        this.channel = ManagedChannelBuilder.forAddress(host, port).useTransportSecurity().build();
        this.shutdownTimeoutMs = shutdownTimeoutMs;
    }

    public PropertyServiceGrpc.PropertyServiceBlockingStub createPropertyServiceBlockingStub() {
        return PropertyServiceGrpc.newBlockingStub(channel);
    }

    @PreDestroy
    private void preDestroy() {
        try {
            channel.shutdown().awaitTermination(shutdownTimeoutMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(
                "OrganizationServiceGrpcClientFactory error while shutting down GRPC channel in preDestroy method",
                e
            );
        }
    }
}
