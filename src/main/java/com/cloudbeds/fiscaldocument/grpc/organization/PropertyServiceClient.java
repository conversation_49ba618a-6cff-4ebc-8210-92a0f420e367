package com.cloudbeds.fiscaldocument.grpc.organization;

import com.cloudbeds.fiscaldocument.enums.PropertyFeature;
import com.cloudbeds.organization.v1.GetPropertyFeatureRequest;
import com.cloudbeds.organization.v1.GetPropertyRequest;
import com.cloudbeds.organization.v1.ListPropertiesRequest;
import com.cloudbeds.organization.v1.Property;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PropertyServiceClient {
    private final OrganizationServiceGrpcClientFactory organizationServiceGrpcClientFactory;


    /**
     * Get properties list by set of ids.
     *
     *
     * @param propertyIds Set of property ids.
     * @return List of properties.
     */
    @Cacheable(cacheNames = {"propertyDetails"}, key = "#propertyIds.toString()")
    public List<Property> listProperties(Collection<Long> propertyIds) {
        if (propertyIds.isEmpty()) {
            log.warn("Property Ids are empty skipping request to Org Service");
            return List.of();
        }

        ListPropertiesRequest request = ListPropertiesRequest.newBuilder()
            .addAllIds(propertyIds)
            .setIncludeProfile(true)
            .build();

        var response = organizationServiceGrpcClientFactory
            .createPropertyServiceBlockingStub()
            .listProperties(request);

        return response.getPropertiesList();
    }

    /**
     * Get property.
     *
     * @param propertyId propertyId
     * @return Property
     */
    @Cacheable(cacheNames = {"propertyDetails"}, key = "#propertyId")
    public Property getProperty(Long propertyId) {
        var request = GetPropertyRequest.newBuilder()
            .setId(propertyId)
            .build();

        var response = organizationServiceGrpcClientFactory
            .createPropertyServiceBlockingStub()
            .getProperty(request);

        return response.getProperty();
    }

    /**
     * Get property feature by name.
     *
     * @param propertyId propertyId
     * @param feature feature name
     * @return PropertyFeature
     */
    public boolean isPropertyFeatureEnabled(Long propertyId, PropertyFeature feature) {
        var request = GetPropertyFeatureRequest.newBuilder()
            .setPropertyId(propertyId)
            .setName(feature.getValue())
            .build();

        var response = organizationServiceGrpcClientFactory
            .createPropertyServiceBlockingStub()
            .getPropertyFeature(request);

        return response.getPropertyFeature().getEnabled();
    }
}
