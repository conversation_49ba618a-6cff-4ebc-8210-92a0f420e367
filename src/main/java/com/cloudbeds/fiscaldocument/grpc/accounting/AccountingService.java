package com.cloudbeds.fiscaldocument.grpc.accounting;

import com.cloudbeds.accounting.v1.GetTransactionIdsByMfdTransactionsRequest;
import com.cloudbeds.accounting.v1.ListPostedTransactionsRequest;
import com.cloudbeds.accounting.v1.ListPostedTransactionsResponse;
import com.cloudbeds.accounting.v1.ListPostedTransactionsWithFolioRequest;
import com.cloudbeds.accounting.v1.ListPostedTransactionsWithFolioResponse;
import com.cloudbeds.accounting.v1.PostedTransactionsRequest;
import com.cloudbeds.accounting.v1.Transaction;
import com.cloudbeds.accounting.v1.TransactionsMap;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.rpc.v1.ConditionOperator;
import com.cloudbeds.rpc.v1.LogicalOperator;
import com.google.protobuf.ListValue;
import com.google.protobuf.Value;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountingService {
    private final AccountingServiceGrpcClientFactory accountingServiceGrpcClientFactory;

    /**
     * Get accounting transaction from mfd transaction ids.
     *
     * @param ids list of transaction ids
     * @return map of mfd transaction ids and list of accounting transaction ids
     */
    public Map<String, List<Long>> getTransactionIdsFromMfdTransactions(List<String> ids) {
        if (ids.isEmpty()) {
            return Map.of();
        }

        var transactions = accountingServiceGrpcClientFactory
            .createTransactionToMfdTransactionServiceBlockingStub()
            .getTransactionIdsByMfdTransactions(
                GetTransactionIdsByMfdTransactionsRequest.newBuilder()
                    .addAllTransactionIds(ids)
                    .build()
            );
        return transactions.getTransactionsMapList()
            .stream()
            .collect(Collectors.groupingBy(
                TransactionsMap::getMfdTransactionId,
                Collectors.mapping(
                    TransactionsMap::getTransactionId,
                    Collectors.toList()
                )
            ));
    }

    /**
     * Get posted transactions that are not invoiced.
     *
     * @param propertyId propertyId
     * @param transactionIds ids invoiced transaction ids
     * @param sourceId sourceId
     * @param sourceKind sourceKind
     * @param documentKind documentKind
     * @param pageToken page token
     * @param limit limit
     * @return list of transactions
     */
    public ListPostedTransactionsWithFolioResponse getAvailableTransactions(
        Long propertyId,
        Collection<Long> transactionIds,
        Long sourceId,
        SourceKind sourceKind,
        DocumentKind documentKind,
        String pageToken,
        Integer limit
    ) {
        var request = buildGetAvailableTransactionsRequest(
            propertyId, transactionIds, sourceId, sourceKind, documentKind, pageToken, limit);

        return accountingServiceGrpcClientFactory
            .createPostedTransactionServiceBlockingStub()
            .listPostedTransactionsWithFolio(request);
    }

    /**
     * Get posted transactions by ids.
     *
     * @param propertyId propertyId
     * @param ids transaction ids
     * @param pageToken page token
     * @param limit limit
     * @return list of transactions
     */
    public ListPostedTransactionsWithFolioResponse getTransactionsByIds(
        Long propertyId,
        Collection<Long> ids,
        String pageToken,
        Integer limit
    ) {
        if (ids == null || ids.isEmpty()) {
            return ListPostedTransactionsWithFolioResponse.newBuilder().build();
        }

        var request = buildGetByIdsRequest(propertyId, ids, pageToken, limit);
        return accountingServiceGrpcClientFactory
            .createPostedTransactionServiceBlockingStub()
            .listPostedTransactionsWithFolio(request);
    }

    /**
     * Get posted transactions.
     *
     * @param propertyId propertyId
     * @param ids ids
     * @param sourceId sourceId
     * @param sourceKind sourceKind
     * @return list of transactions
     */
    public List<Transaction> getPostedTransactions(
        Long propertyId,
        List<Long> ids,
        Long sourceId,
        SourceKind sourceKind
    ) {
        ListPostedTransactionsResponse response;
        String pageToken = null;
        var result = new ArrayList<Transaction>();
        do {
            var request = buildGetByIdRequest(propertyId, ids, sourceId, sourceKind, pageToken);
            response = accountingServiceGrpcClientFactory
                .createPostedTransactionServiceBlockingStub()
                .listPostedTransactions(request);

            pageToken = response.getNextPageToken();
            result.addAll(response.getTransactionsList());
        } while (StringUtils.hasText(response.getNextPageToken()));

        return result;
    }

    /**
     * Get posted transactions.
     *
     * @param propertyId propertyId
     * @param ids ids
     * @return list of transactions
     */
    public List<Transaction> getRoutedTransactions(Long propertyId, List<Long> ids) {
        ListPostedTransactionsResponse response;
        String pageToken = null;
        var result = new ArrayList<Transaction>();
        do {
            var request = buildGetByIdRequest(propertyId, ids, pageToken);
            response = accountingServiceGrpcClientFactory
                .createPostedTransactionServiceBlockingStub()
                .listPostedTransactions(request);

            pageToken = response.getNextPageToken();
            result.addAll(response.getTransactionsList());
        } while (StringUtils.hasText(response.getNextPageToken()));

        return result;
    }

    private ListPostedTransactionsWithFolioRequest buildGetByIdsRequest(
        Long propertyId,
        Collection<Long> ids,
        String pageToken,
        Integer limit
    ) {
        var request = PostedTransactionsRequest.newBuilder()
            .addPropertyIds(propertyId)
            .setFilter(
                PostedTransactionsRequest.Filter.newBuilder()
                    .setCompositeFilter(
                        PostedTransactionsRequest.CompositeFilter.newBuilder()
                            .addFilters(
                                PostedTransactionsRequest.Filter.newBuilder()
                                    .setFieldFilter(
                                        PostedTransactionsRequest.FieldFilter.newBuilder()
                                            .setField(PostedTransactionsRequest.FieldFilter.Field.FIELD_ID)
                                            .setOperator(ConditionOperator.CONDITION_OPERATOR_IN)
                                            .setValue(
                                                Value.newBuilder()
                                                    .setListValue(ListValue.newBuilder()
                                                        .addAllValues(ids.stream()
                                                            .map(Value.newBuilder()::setNumberValue)
                                                            .map(Value.Builder::build)
                                                            .toList()
                                                        ).build()
                                                    )
                                                    .build()
                                            )
                                            .build()
                                    ).build()
                            )
                            .setOperator(LogicalOperator.LOGICAL_OPERATOR_AND)
                            .build()
                    ).build()
            );

        if (pageToken != null) {
            request.setPageToken(pageToken);
        }
        request.setPageSize(limit);

        return ListPostedTransactionsWithFolioRequest.newBuilder()
            .setRequest(request.build())
            .build();
    }

    private ListPostedTransactionsWithFolioRequest buildGetAvailableTransactionsRequest(
        Long propertyId,
        Collection<Long> ids,
        Long sourceId,
        SourceKind sourceKind,
        DocumentKind documentKind,
        String pageToken,
        Integer limit
    ) {
        var compositeFilterBuilder = PostedTransactionsRequest.CompositeFilter.newBuilder()
            .addFilters(buildSourceIdFilter(sourceId))
            .addFilters(buildSourceKindFilter(sourceKind))
            .setOperator(LogicalOperator.LOGICAL_OPERATOR_AND);


        if (!ids.isEmpty()) {
            compositeFilterBuilder.addFilters(buildTransactionIdsFilter(ids));
        }

        if (DocumentKind.CREDIT_NOTE.equals(documentKind)) {
            compositeFilterBuilder.addFilters(buildAdjustmentTransactionsFilter());
        }
        var compositeFilter = compositeFilterBuilder.build();
        var request = PostedTransactionsRequest.newBuilder()
            .addPropertyIds(propertyId)
            .setFilter(
                PostedTransactionsRequest.Filter.newBuilder()
                    .setCompositeFilter(compositeFilter).build()
            );

        if (pageToken != null) {
            request.setPageToken(pageToken);
        }
        request.setPageSize(limit);

        return ListPostedTransactionsWithFolioRequest.newBuilder()
                .setRequest(request.build())
                .build();
    }

    private PostedTransactionsRequest.Filter buildAdjustmentTransactionsFilter() {
        return PostedTransactionsRequest.Filter.newBuilder()
            .setFieldFilter(
                PostedTransactionsRequest.FieldFilter.newBuilder()
                    .setField(PostedTransactionsRequest.FieldFilter.Field.FIELD_INTERNAL_CODE)
                    .setOperator(ConditionOperator.CONDITION_OPERATOR_IN)
                    .setValue(Value.newBuilder().setListValue(
                        ListValue.newBuilder()
                            .addValues(Value.newBuilder().setStringValue("1000A").build())
                            .addValues(Value.newBuilder().setStringValue("1100A").build())
                            .addValues(Value.newBuilder().setStringValue("5000A").build())
                            .addValues(Value.newBuilder().setStringValue("1200A").build())
                            .addValues(Value.newBuilder().setStringValue("2000A").build())
                            .addValues(Value.newBuilder().setStringValue("3000A").build())
                            .addValues(Value.newBuilder().setStringValue("4000A").build())
                            .addValues(Value.newBuilder().setStringValue("8100A").build())
                            .addValues(Value.newBuilder().setStringValue("8000A").build())
                            .addValues(Value.newBuilder().setStringValue("9000A").build())
                            .addValues(Value.newBuilder().setStringValue("9100A").build())
                            .addValues(Value.newBuilder().setStringValue("9200A").build())
                            .addValues(Value.newBuilder().setStringValue("9300A").build())
                        ).build()
                    ).build()
            ).build();
    }

    private static PostedTransactionsRequest.Filter buildTransactionIdsFilter(Collection<Long> ids) {
        return PostedTransactionsRequest.Filter.newBuilder()
            .setFieldFilter(
                PostedTransactionsRequest.FieldFilter.newBuilder()
                    .setField(PostedTransactionsRequest.FieldFilter.Field.FIELD_ID)
                    .setOperator(ConditionOperator.CONDITION_OPERATOR_NOT_IN)
                    .setValue(
                        Value.newBuilder()
                            .setListValue(ListValue.newBuilder()
                                .addAllValues(ids.stream()
                                    .map(Value.newBuilder()::setNumberValue)
                                    .map(Value.Builder::build)
                                    .toList()
                                ).build()
                            )
                            .build()
                    )
                    .build()
            ).build();
    }

    private static PostedTransactionsRequest.Filter buildSourceKindFilter(SourceKind sourceKind) {
        return PostedTransactionsRequest.Filter.newBuilder()
            .setFieldFilter(
                PostedTransactionsRequest.FieldFilter.newBuilder()
                    .setField(PostedTransactionsRequest.FieldFilter.Field.FIELD_SOURCE)
                    .setOperator(ConditionOperator.CONDITION_OPERATOR_EQUAL)
                    .setValue(
                        Value.newBuilder()
                            .setStringValue(sourceKind.name())
                            .build()
                    )
                    .build()
            )
            .build();
    }

    private static PostedTransactionsRequest.Filter buildSourceIdFilter(Long sourceId) {
        return PostedTransactionsRequest.Filter.newBuilder()
            .setFieldFilter(
                PostedTransactionsRequest.FieldFilter.newBuilder()
                    .setField(PostedTransactionsRequest.FieldFilter.Field.FIELD_SOURCE_ID)
                    .setOperator(ConditionOperator.CONDITION_OPERATOR_EQUAL)
                    .setValue(
                        Value.newBuilder()
                            .setNumberValue(sourceId)
                            .build()
                    )
                    .build()
            )
            .build();
    }

    private ListPostedTransactionsRequest buildGetByIdRequest(
        Long propertyId,
        List<Long> ids,
        String pageToken
    ) {
        var request = PostedTransactionsRequest.newBuilder()
            .addPropertyIds(propertyId)
            .setFilter(
                PostedTransactionsRequest.Filter.newBuilder()
                    .setFieldFilter(
                        PostedTransactionsRequest.FieldFilter.newBuilder()
                            .setField(PostedTransactionsRequest.FieldFilter.Field.FIELD_ID)
                            .setOperator(ConditionOperator.CONDITION_OPERATOR_IN)
                            .setValue(
                                Value.newBuilder()
                                    .setListValue(ListValue.newBuilder()
                                        .addAllValues(ids.stream()
                                            .map(Value.newBuilder()::setNumberValue)
                                            .map(Value.Builder::build)
                                            .toList()
                                        ).build()
                                    )
                                    .build()
                            )
                            .build()
                    )
                    .build()
            );

        if (pageToken != null) {
            request.setPageToken(pageToken);
        }

        return
            ListPostedTransactionsRequest.newBuilder()
                .setRequest(request.build())
                .build();
    }

    private ListPostedTransactionsRequest buildGetByIdRequest(
        Long propertyId,
        List<Long> ids,
        Long sourceId,
        SourceKind sourceKind,
        String pageToken
    ) {
        var request = PostedTransactionsRequest.newBuilder()
            .addPropertyIds(propertyId)
            .setFilter(
                PostedTransactionsRequest.Filter.newBuilder()
                    .setCompositeFilter(
                        PostedTransactionsRequest.CompositeFilter.newBuilder()
                            .addFilters(
                                PostedTransactionsRequest.Filter.newBuilder()
                                    .setFieldFilter(
                                        PostedTransactionsRequest.FieldFilter.newBuilder()
                                            .setField(
                                                PostedTransactionsRequest.FieldFilter.Field.FIELD_SOURCE_ID)
                                            .setOperator(ConditionOperator.CONDITION_OPERATOR_EQUAL)
                                            .setValue(
                                                Value.newBuilder()
                                                    .setNumberValue(sourceId)
                                                    .build()
                                            )
                                            .build()
                                    )
                                    .build()
                            )
                            .addFilters(
                                PostedTransactionsRequest.Filter.newBuilder()
                                    .setFieldFilter(
                                        PostedTransactionsRequest.FieldFilter.newBuilder()
                                            .setField(PostedTransactionsRequest.FieldFilter.Field.FIELD_SOURCE)
                                            .setOperator(ConditionOperator.CONDITION_OPERATOR_EQUAL)
                                            .setValue(
                                                Value.newBuilder()
                                                    .setStringValue(sourceKind.name())
                                                    .build()
                                            )
                                            .build()
                                    )
                                    .build()
                            )
                            .addFilters(
                                PostedTransactionsRequest.Filter.newBuilder()
                                    .setFieldFilter(
                                        PostedTransactionsRequest.FieldFilter.newBuilder()
                                            .setField(PostedTransactionsRequest.FieldFilter.Field.FIELD_ID)
                                            .setOperator(ConditionOperator.CONDITION_OPERATOR_IN)
                                            .setValue(
                                                Value.newBuilder()
                                                    .setListValue(ListValue.newBuilder()
                                                        .addAllValues(ids.stream()
                                                            .map(Value.newBuilder()::setNumberValue)
                                                            .map(Value.Builder::build)
                                                            .toList()
                                                        ).build()
                                                    )
                                                    .build()
                                            )
                                            .build()
                                    )
                                    .build()
                            )
                            .setOperator(LogicalOperator.LOGICAL_OPERATOR_AND)
                            .build()
                    )
                    .build()
            );

        if (pageToken != null) {
            request.setPageToken(pageToken);
        }

        return
            ListPostedTransactionsRequest.newBuilder()
                .setRequest(request.build())
                .build();
    }
}
