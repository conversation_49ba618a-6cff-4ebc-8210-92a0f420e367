package com.cloudbeds.fiscaldocument.grpc.accounting;

import com.cloudbeds.accounting.v1.PostedTransactionServiceGrpc;
import com.cloudbeds.accounting.v1.TransactionToMfdTransactionServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import java.util.concurrent.TimeUnit;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AccountingServiceGrpcClientFactory {
    private final ManagedChannel channel;
    private final long shutdownTimeoutMs;

    public AccountingServiceGrpcClientFactory(
        @Value("${grpc.services.accounting.host}") String host,
        @Value("${grpc.services.accounting.port}") int port,
        @Value("${grpc.services.accounting.shutdownTimeoutMs}") long shutdownTimeoutMs
    ) {
        this.channel = ManagedChannelBuilder.forAddress(host, port).useTransportSecurity().build();
        this.shutdownTimeoutMs = shutdownTimeoutMs;
    }

    public PostedTransactionServiceGrpc.PostedTransactionServiceBlockingStub
        createPostedTransactionServiceBlockingStub() {
        return PostedTransactionServiceGrpc.newBlockingStub(channel);
    }

    public TransactionToMfdTransactionServiceGrpc.TransactionToMfdTransactionServiceBlockingStub
        createTransactionToMfdTransactionServiceBlockingStub() {
        return TransactionToMfdTransactionServiceGrpc.newBlockingStub(channel);
    }

    @PreDestroy
    private void preDestroy() {
        try {
            channel.shutdown().awaitTermination(shutdownTimeoutMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(
                "OrganizationServiceGrpcClientFactory error while shutting down GRPC channel in preDestroy method",
                e
            );
        }
    }
}
