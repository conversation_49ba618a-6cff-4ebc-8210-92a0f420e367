package com.cloudbeds.fiscaldocument.grpc.marketplace;

import com.cloudbeds.marketplace.v1.GetInvoiceIntegrationEnabledRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketplaceServiceClient {
    private final MarketplaceServiceGrpcClientFactory marketplaceServiceGrpcClientFactory;

    /**
     * Checks if invoice integration is enabled for the given property.
     *
     * @param propertyId property id
     * @return {@code true} if invoice integration is enabled; {@code false} otherwise
     */
    @Cacheable(cacheNames = {"marketplaceIntegrations"}, key = "#propertyId")
    public boolean getInvoiceIntegrationEnabled(Long propertyId) {
        var request = GetInvoiceIntegrationEnabledRequest.newBuilder()
            .setPropertyId(propertyId)
            .build();

        var response = marketplaceServiceGrpcClientFactory.createBlockingStub().getInvoiceIntegrationEnabled(request);

        return response.getEnabled();
    }
}
