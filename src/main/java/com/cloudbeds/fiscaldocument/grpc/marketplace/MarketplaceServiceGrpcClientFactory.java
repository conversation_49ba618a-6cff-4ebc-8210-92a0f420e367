package com.cloudbeds.fiscaldocument.grpc.marketplace;

import com.cloudbeds.marketplace.v1.MarketplaceServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import java.util.concurrent.TimeUnit;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MarketplaceServiceGrpcClientFactory {
    private final ManagedChannel channel;
    private final long shutdownTimeoutMs;

    public MarketplaceServiceGrpcClientFactory(
        @Value("${grpc.services.marketplace.host}") String host,
        @Value("${grpc.services.marketplace.port}") int port,
        @Value("${grpc.services.marketplace.shutdownTimeoutMs}") long shutdownTimeoutMs
    ) {
        this.channel = ManagedChannelBuilder.forAddress(host, port).useTransportSecurity().build();
        this.shutdownTimeoutMs = shutdownTimeoutMs;
    }

    public MarketplaceServiceGrpc.MarketplaceServiceBlockingStub createBlockingStub() {
        return MarketplaceServiceGrpc.newBlockingStub(channel);
    }

    @PreDestroy
    private void preDestroy() {
        try {
            channel.shutdown().awaitTermination(shutdownTimeoutMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(
                "MarketplaceServiceGrpc error while shutting down GRPC channel in preDestroy method",
                e
            );
        }
    }
}
