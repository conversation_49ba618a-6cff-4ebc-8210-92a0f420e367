package com.cloudbeds.fiscaldocument.grpc.guestrequirement;

import com.cloudbeds.guestrequirements.v1.GuestRequirementsServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import java.util.concurrent.TimeUnit;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GuestRequirementsServiceGrpcClientFactory {
    private final ManagedChannel channel;
    private final long shutdownTimeoutMs;

    public GuestRequirementsServiceGrpcClientFactory(
        @Value("${grpc.services.guest-requirements.host}") String host,
        @Value("${grpc.services.guest-requirements.port}") int port,
        @Value("${grpc.services.guest-requirements.shutdownTimeoutMs}") long shutdownTimeoutMs
    ) {
        this.channel = ManagedChannelBuilder.forAddress(host, port).useTransportSecurity().build();
        this.shutdownTimeoutMs = shutdownTimeoutMs;
    }

    public GuestRequirementsServiceGrpc.GuestRequirementsServiceBlockingStub createGuestRequirementServiceBlockingStub(
    ) {
        return GuestRequirementsServiceGrpc.newBlockingStub(channel);
    }

    @PreDestroy
    private void preDestroy() {
        try {
            channel.shutdown().awaitTermination(shutdownTimeoutMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(
                "GuestRequirementsServiceGrpcClientFactory error while shutting down GRPC channel in preDestroy method",
                e
            );
        }
    }
}
