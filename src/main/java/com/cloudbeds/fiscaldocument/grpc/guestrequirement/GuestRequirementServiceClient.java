package com.cloudbeds.fiscaldocument.grpc.guestrequirement;

import com.cloudbeds.guestrequirements.v1.GuestRequirements;
import com.cloudbeds.guestrequirements.v1.ReadGuestRequirementsRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class GuestRequirementServiceClient {
    private final GuestRequirementsServiceGrpcClientFactory guestRequirementsServiceGrpcClientFactory;

    /**
     * List Guests requirements by property id.
     *
     * @param propertyId propertyId
     * @param guestId guestId
     * @return Property
     */
    public GuestRequirements listGuestsRequirement(Long propertyId, Long guestId) {
        var request = ReadGuestRequirementsRequest.newBuilder()
            .setPropertyId(propertyId)
            .setGuestId(guestId)
            .build();

        var response = guestRequirementsServiceGrpcClientFactory
            .createGuestRequirementServiceBlockingStub()
            .readGuestRequirements(request);

        return response.getGuestRequirements();
    }
}
