package com.cloudbeds.fiscaldocument.grpc.guest;

import com.cloudbeds.guest.v1.GuestServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import java.util.concurrent.TimeUnit;
import javax.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GuestServiceGrpcClientFactory {
    private final ManagedChannel channel;
    private final long shutdownTimeoutMs;

    public GuestServiceGrpcClientFactory(
        @Value("${grpc.services.guest.host}") String host,
        @Value("${grpc.services.guest.port}") int port,
        @Value("${grpc.services.guest.shutdownTimeoutMs}") long shutdownTimeoutMs
    ) {
        this.channel = ManagedChannelBuilder.forAddress(host, port).useTransportSecurity().build();
        this.shutdownTimeoutMs = shutdownTimeoutMs;
    }

    public GuestServiceGrpc.GuestServiceBlockingStub createGuestServiceBlockingStub(
    ) {
        return GuestServiceGrpc.newBlockingStub(channel);
    }

    @PreDestroy
    private void preDestroy() {
        try {
            channel.shutdown().awaitTermination(shutdownTimeoutMs, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error(
                "GuestServiceGrpcClientFactory error while shutting down GRPC channel in preDestroy method",
                e
            );
        }
    }
}
