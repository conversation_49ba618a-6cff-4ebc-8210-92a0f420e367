package com.cloudbeds.fiscaldocument.grpc.guest;

import com.cloudbeds.guest.v1.GetPersonByExternalIdRequest;
import com.cloudbeds.guest.v1.Person;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class GuestServiceClient {
    private final GuestServiceGrpcClientFactory guestServiceGrpcClientFactory;

    /**
     * Get guest by id.
     *
     * @param organizationId organizationId
     * @param guestId guestId
     * @return Guest
     */
    @Cacheable(cacheNames = {"guestDetails"}, key = "{#organizationId, #guestId}")
    public Person getGuestById(Long organizationId, Long guestId) {
        var request = GetPersonByExternalIdRequest.newBuilder()
            .setOrganizationId(organizationId)
            .setExternalId(guestId)
            .build();

        var response = guestServiceGrpcClientFactory
            .createGuestServiceBlockingStub()
            .getPersonByExternalId(request);

        return response.getPerson();
    }
}
