package com.cloudbeds.fiscaldocument.eventhandlers.fiscaldocument;

import com.cloudbeds.FiscalDocumentService.CreditNoteMethod;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import com.cloudbeds.FiscalDocumentService.RectifyCreateMethod;
import com.cloudbeds.accounting.v1.InternalCodeGroup;
import com.cloudbeds.accounting.v1.Source;
import com.cloudbeds.accounting.v1.Transaction;
import com.cloudbeds.booking.v1.BookingRoom;
import com.cloudbeds.booking.v1.BookingWithRooms;
import com.cloudbeds.fiscaldocument.entity.DocumentConfig;
import com.cloudbeds.fiscaldocument.entity.DocumentSequence;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentLinkedDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentTransaction;
import com.cloudbeds.fiscaldocument.enums.CreationMethod;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import com.cloudbeds.fiscaldocument.enums.Origin;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.grpc.accounting.AccountingService;
import com.cloudbeds.fiscaldocument.grpc.mfd.GroupProfileServiceClient;
import com.cloudbeds.fiscaldocument.grpc.organization.PropertyServiceClient;
import com.cloudbeds.fiscaldocument.models.InvoiceTemplateKeys;
import com.cloudbeds.fiscaldocument.models.TransactionDto;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentTransactionRepository;
import com.cloudbeds.fiscaldocument.services.BookingService;
import com.cloudbeds.fiscaldocument.services.DocumentConfigService;
import com.cloudbeds.fiscaldocument.services.DocumentSequenceService;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentFileService;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentService;
import com.cloudbeds.fiscaldocument.services.data.ConfigDataExtractor;
import com.cloudbeds.fiscaldocument.services.data.PropertyProfileDataExtractor;
import com.cloudbeds.fiscaldocument.services.data.ReservationDataExtractor;
import com.cloudbeds.fiscaldocument.services.data.TransactionDataExtractor;
import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import com.cloudbeds.fiscaldocument.utils.Currency;
import com.cloudbeds.fiscaldocument.utils.EnumConverterUtil;
import com.cloudbeds.fiscaldocument.utils.PdfBuilder;
import com.cloudbeds.fiscaldocument.utils.QrCodeGenerator;
import com.cloudbeds.fiscaldocument.utils.ResourceFileReader;
import com.cloudbeds.fiscaldocument.utils.TemplateRenderer;
import com.cloudbeds.fiscaldocument.utils.constants.DocumentTemplateLists;
import com.cloudbeds.group.v1.GroupProfile;
import com.cloudbeds.organization.v1.Property;
import java.math.BigInteger;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import static com.cloudbeds.fiscaldocument.converters.TransactionToDtoConverter.convertToDto;
import static com.cloudbeds.fiscaldocument.services.FiscalDocumentService.VALID_LINKED_DOCUMENT_STATUSES;

@Slf4j
@Component
@RequiredArgsConstructor
public class FiscalDocumentEventHandler {
    private static final int QR_CODE_SIZE = 76;
    private static final List<DocumentStatus> PROCESSED_STATUSES = List.of(
        DocumentStatus.COMPLETED,
        DocumentStatus.VOIDED,
        DocumentStatus.FAILED
    );

    private final PropertyServiceClient propertyServiceClient;
    private final DocumentSequenceService documentSequenceService;
    private final AccountingService accountingService;
    private final DocumentConfigService documentConfigService;
    private final FiscalDocumentService fiscalDocumentService;
    private final BookingService bookingService;
    private final TransactionDataExtractor transactionDataExtractor;
    private final ReservationDataExtractor reservationDataExtractor;
    private final PropertyProfileDataExtractor propertyProfileDataExtractor;
    private final ConfigDataExtractor configDataExtractor;
    private final FiscalDocumentFileService fiscalDocumentFileService;
    private final GroupProfileServiceClient groupProfileServiceClient;
    private final FiscalDocumentTransactionRepository fiscalDocumentTransactionRepository;
    private final TransactionTemplate transactionTemplate;

    /**
     * Process a list of fiscal document events.
     *
     * @param fiscalDocumentEvents the list of fiscal document events to process
     */
    public void processEvents(List<FiscalDocumentEventValue> fiscalDocumentEvents) {
        fiscalDocumentEvents.forEach(fiscalDocumentEvent -> {
            transactionTemplate.executeWithoutResult(status -> {
                switch (fiscalDocumentEvent.getType()) {
                    case INVOICE_CREATE_EVENT -> createInvoice(fiscalDocumentEvent);
                    case CREDIT_NOTE_CREATE_EVENT -> createCreditNote(fiscalDocumentEvent);
                    case RECTIFY_INVOICE_CREATE_EVENT -> createRectifyInvoice(fiscalDocumentEvent);
                    case INTEGRATION_CREATE_EVENT -> createIntegrationDocument(fiscalDocumentEvent);
                    default -> log.warn("Unknown event type: {}", fiscalDocumentEvent.getType());
                }
            });
        });
    }

    private void createRectifyInvoice(FiscalDocumentEventValue fiscalDocumentEvent) {
        var createEvent = fiscalDocumentEvent.getRectifyInvoiceCreateEvent();
        var rectifyInvoiceOptional = fiscalDocumentService.findById(createEvent.getId());
        if (rectifyInvoiceOptional.isEmpty()) {
            log.error("Document not found for id: {}", createEvent.getId());
            throw new RuntimeException("Document not found");
        }
        var rectifyInvoice = rectifyInvoiceOptional.get();
        if (PROCESSED_STATUSES.contains(rectifyInvoice.getStatus())) {
            log.warn(
                "Document with id {} already has a completed status: {}",
                createEvent.getId(),
                rectifyInvoice.getStatus()
            );
            return;
        }

        if (rectifyInvoice.getStatus() != DocumentStatus.REQUESTED) {
            log.warn(
                "Document with id {} not in requested status: {}",
                createEvent.getId(),
                rectifyInvoice.getStatus()
            );
            return;
        }

        var invoiceOptional = fiscalDocumentService.findById(createEvent.getInvoiceId());
        if (invoiceOptional.isEmpty()) {
            log.error("Invoice not found for id: {}", createEvent.getInvoiceId());
            throw new RuntimeException("Document not found");
        }
        var invoice = invoiceOptional.get();

        if (DocumentStatus.VOIDED.equals(invoice.getStatus())) {
            rectifyInvoice.setStatus(DocumentStatus.FAILED);
            rectifyInvoice.setFailReason("Cannot create rectify invoice for voided invoice");
            fiscalDocumentService.save(rectifyInvoice);
            return;
        }

        if (RectifyCreateMethod.ADJUSTMENT.equals(createEvent.getMethod())) {
            if (!areTransactionsValid(createEvent.getTransactionIds())) {
                rectifyInvoice.setStatus(DocumentStatus.FAILED);
                rectifyInvoice.setFailReason("Transactions are already in use");
                fiscalDocumentService.save(rectifyInvoice);
                return;
            }
        }

        if (Origin.INTEGRATION.equals(rectifyInvoice.getOrigin())) {
            rectifyInvoice.setStatus(DocumentStatus.PENDING_INTEGRATION);
            fiscalDocumentService.save(rectifyInvoice);
        } else {
            log.error("Rectify invoice with origin not integration");
            rectifyInvoice.setStatus(DocumentStatus.FAILED);
            rectifyInvoice.setFailReason("Rectify invoice with origin not integration");
            fiscalDocumentService.save(rectifyInvoice);
        }

    }

    private void createIntegrationDocument(FiscalDocumentEventValue fiscalDocumentEvent) {
        var createEvent = fiscalDocumentEvent.getIntegrationCreateEvent();
        var documentOptional = fiscalDocumentService.findById(createEvent.getId());
        if (documentOptional.isEmpty()) {
            log.error("Document not found for id: {}", createEvent.getId());
            throw new RuntimeException("Document not found");
        }

        var document = documentOptional.get();
        if (document.getFilePath() != null) {
            log.warn("Document with id {} already has a file path: {}", createEvent.getId(), document.getFilePath());
            return; // Document already processed
        }

        switch (document.getKind()) {
            case INVOICE -> createInvoiceIntegration(fiscalDocumentEvent, document);
            case RECTIFY_INVOICE -> createRectifyInvoiceIntegration(fiscalDocumentEvent, document);
            default -> {
                log.warn("Unsupported document kind: {}", document.getKind());
                throw new RuntimeException("Unsupported document kind");
            }
        }
    }

    private void createRectifyInvoiceIntegration(
        FiscalDocumentEventValue eventValue,
        FiscalDocument rectifyInvoice
    ) {
        var createEvent = eventValue.getIntegrationCreateEvent();
        var documentConfig =
            documentConfigService.getDocumentConfig(eventValue.getPropertyId(), DocumentKind.RECTIFY_INVOICE);

        var parent = rectifyInvoice.getLinkedDocuments()
            .stream()
            .map(FiscalDocumentLinkedDocument::getLinkedDocument)
            .filter(linkedDocument -> linkedDocument.getKind() == DocumentKind.INVOICE)
            .findFirst();

        if (parent.isEmpty()) {
            log.error("Parent invoice not found for rectify invoice id: {}", createEvent.getId());
            throw new RuntimeException("Parent invoice not found");
        }

        var sourceKind = EnumConverterUtil.convert(eventValue.getSourceKind());
        var transactions = getTransactions(
            parent.get(),
            rectifyInvoice,
            CreationMethod.VOID.equals(rectifyInvoice.getMethod()),
            rectifyInvoice.getTransactionList().stream().map(FiscalDocumentTransaction::getTransactionId).toList()
        );

        var routedTransactions = getRoutedTransactions(transactions, eventValue.getPropertyId());

        var invoiceNumber = createEvent.getNumber();
        var property = propertyServiceClient.getProperty(eventValue.getPropertyId());
        var transactionsDto = convertToDto(transactions);
        var templateKeys = buildTemplateKeys(
            transactionsDto,
            convertToDto(routedTransactions),
            invoiceNumber,
            property,
            eventValue.getSourceId(),
            sourceKind,
            documentConfig,
            rectifyInvoice
        );

        rectifyInvoice.setNumber(createEvent.getNumber());
        templateKeys.setRectifyInvoiceNumber(createEvent.getNumber());
        templateKeys.setOfficialId(createEvent.getOfficialId());
        templateKeys.setGovernmentIntegrationUrl(createEvent.getUrl());

        if (StringUtils.hasText(createEvent.getQrUrl())) {
            templateKeys.setQrCodeFilename(createEvent.getQrUrl());
        } else {
            try {
                templateKeys.setQrCodeFilename("data:image/png;base64," + createEvent.getQrString());
            } catch (Exception e) {
                log.error("Error generating QR code for document id: {}", rectifyInvoice.getId(), e);
                throw new RuntimeException("Error generating QR code", e);
            }
        }

        storeAndUpdateDocument(
            rectifyInvoice,
            documentConfig,
            sourceKind,
            invoiceNumber,
            Currency.valueOf(property.getCurrency()),
            transactionsDto,
            templateKeys,
            property.getPropertyProfile().getHotelAddress().getCountryCode(),
            Set.of());
    }

    private void createInvoiceIntegration(FiscalDocumentEventValue eventValue, FiscalDocument document) {
        var createEvent = eventValue.getIntegrationCreateEvent();
        var documentConfig =
            documentConfigService.getDocumentConfig(eventValue.getPropertyId(), DocumentKind.INVOICE);

        var sourceKind = EnumConverterUtil.convert(eventValue.getSourceKind());
        var transactions = accountingService.getPostedTransactions(
            eventValue.getPropertyId(),
            document.getTransactionList().stream().map(FiscalDocumentTransaction::getTransactionId).toList(),
            eventValue.getSourceId(),
            EnumConverterUtil.convert(eventValue.getSourceKind())
        );

        var routedTransactions = getRoutedTransactions(transactions, eventValue.getPropertyId());

        var invoiceNumber = createEvent.getNumber();
        var property = propertyServiceClient.getProperty(eventValue.getPropertyId());
        var transactionsDto = convertToDto(transactions);
        var templateKeys = buildTemplateKeys(
            transactionsDto,
            convertToDto(routedTransactions),
            invoiceNumber,
            property,
            eventValue.getSourceId(),
            sourceKind,
            documentConfig,
            document
        );

        if (StringUtils.hasText(createEvent.getQrUrl())) {
            templateKeys.setQrCodeFilename(createEvent.getQrUrl());
        } else {
            try {
                templateKeys.setQrCodeFilename(
                    QrCodeGenerator.getCodeAsBase64Image(
                        createEvent.getQrString(),
                        QR_CODE_SIZE
                    )
                );
            } catch (Exception e) {
                log.error("Error generating QR code for document id: {}", document.getId(), e);
                throw new RuntimeException("Error generating QR code", e);
            }
        }
        templateKeys.setOfficialId(createEvent.getOfficialId());
        templateKeys.setGovernmentIntegrationUrl(createEvent.getUrl());

        storeAndUpdateDocument(
            document,
            documentConfig,
            sourceKind,
            invoiceNumber,
            Currency.valueOf(property.getCurrency()),
            transactionsDto,
            templateKeys,
            property.getPropertyProfile().getHotelAddress().getCountryCode(),
            Set.of());
    }

    private void createCreditNote(FiscalDocumentEventValue fiscalDocumentEvent) {
        var createEvent = fiscalDocumentEvent.getCreditNoteCreateEvent();
        var creditNoteOptional = fiscalDocumentService.findById(createEvent.getId());
        if (creditNoteOptional.isEmpty()) {
            log.error("Document not found for id: {}", createEvent.getId());
            throw new RuntimeException("Document not found");
        }
        var creditNote = creditNoteOptional.get();
        if (PROCESSED_STATUSES.contains(creditNote.getStatus())) {
            log.warn(
                "Document with id {} already has a completed status: {}",
                createEvent.getId(),
                creditNote.getStatus()
            );
            return;
        }

        if (creditNote.getStatus() != DocumentStatus.REQUESTED) {
            log.warn(
                "Document with id {} not in requested status: {}",
                createEvent.getId(),
                creditNote.getStatus()
            );
            return;
        }

        var invoiceOptional = fiscalDocumentService.findById(createEvent.getInvoiceId());
        if (invoiceOptional.isEmpty()) {
            log.error("Invoice not found for id: {}", createEvent.getInvoiceId());
            throw new RuntimeException("Document not found");
        }
        var invoice = invoiceOptional.get();

        if (DocumentStatus.VOIDED.equals(invoice.getStatus())) {
            creditNote.setStatus(DocumentStatus.FAILED);
            creditNote.setFailReason("Cannot create credit note for voided invoice");
            fiscalDocumentService.save(creditNote);
            return;
        }

        if (CreditNoteMethod.ADJUSTMENT.equals(createEvent.getMethod())) {
            if (!areTransactionsValid(createEvent.getTransactionIds())) {
                creditNote.setStatus(DocumentStatus.FAILED);
                creditNote.setFailReason("Transactions are already in use");
                fiscalDocumentService.save(creditNote);
                return;
            }
        }

        if (Origin.INTEGRATION.equals(creditNote.getOrigin())) {
            creditNote.setStatus(DocumentStatus.PENDING_INTEGRATION);
            fiscalDocumentService.save(creditNote);
            return;
        }

        var transactions = getTransactions(
            invoice,
            creditNote,
            CreditNoteMethod.VOID.equals(createEvent.getMethod()),
            createEvent.getTransactionIds()
        );

        if (CreditNoteMethod.ADJUSTMENT.equals(createEvent.getMethod())) {
            if (transactions.stream().anyMatch(transaction -> !transaction.getInternalCode().getCode().endsWith("A"))) {
                creditNote.setStatus(DocumentStatus.FAILED);
                creditNote.setFailReason(
                    "Cannot create adjustment credit note for transactions that are not adjustments"
                );
                fiscalDocumentService.save(creditNote);
                return;
            }
        }

        var routedTransactions = getRoutedTransactions(transactions, creditNote.getPropertyId());

        var documentConfig =
            documentConfigService.getDocumentConfig(fiscalDocumentEvent.getPropertyId(), DocumentKind.CREDIT_NOTE);
        var sequence = getDocumentSequence(createEvent.getSequenceId(), documentConfig, invoice.getSourceKind());

        var creditNoteNumber = documentConfig.getNumber(invoice.getSourceKind(), sequence, null);

        var property = propertyServiceClient.getProperty(fiscalDocumentEvent.getPropertyId());
        var transactionsDto = convertToDto(transactions);
        if (CreditNoteMethod.VOID.equals(createEvent.getMethod())) {
            transactionsDto
                .forEach(transaction -> {
                    transaction.setAmount(transaction.getAmount().negate());
                    transaction.setAmountMinorUnits(transaction.getAmountMinorUnits().negate());
                });
        }

        var templateKeys = buildTemplateKeys(
            transactionsDto,
            convertToDto(routedTransactions),
            invoice.getNumber(),
            property,
            invoice.getSourceId(),
            invoice.getSourceKind(),
            documentConfig,
            invoice
        );

        creditNote.setNumber(creditNoteNumber);
        creditNote.setStatus(DocumentStatus.OPEN);
        templateKeys.setCreditNoteReason(createEvent.getReason());
        templateKeys.setCreditNoteNumber(creditNote.getNumber());
        var formatter =  getDateTimeFormatter(property);
        templateKeys.setCreditNoteDate(creditNote.getInvoiceDate().format(formatter));

        var updatedLinkedDocuments = new HashSet<FiscalDocument>();
        if (CreditNoteMethod.VOID.equals(createEvent.getMethod())) {
            invoice.getLinkedChildDocuments()
                .stream()
                .filter(document -> !document.getFiscalDocument().getId().equals(creditNote.getId()))
                .forEach(invoiceLinkedDocument -> {
                    var linkedDocumentFiscalDocument = invoiceLinkedDocument.getFiscalDocument();

                    if (VALID_LINKED_DOCUMENT_STATUSES.contains(linkedDocumentFiscalDocument.getStatus())) {
                        linkedDocumentFiscalDocument.setStatus(DocumentStatus.VOIDED);
                        updatedLinkedDocuments.add(linkedDocumentFiscalDocument);
                    } else {
                        throw new FiscalDocumentException(
                                ErrorCode.UNEXPECTED_ERROR,
                                "Linked child document status is not in: " + VALID_LINKED_DOCUMENT_STATUSES);
                    }
                });
        }

        storeAndUpdateDocument(
            creditNote,
            documentConfig,
            invoice.getSourceKind(),
            creditNoteNumber,
            Currency.valueOf(property.getCurrency()),
            transactionsDto,
            templateKeys,
            property.getPropertyProfile().getHotelAddress().getCountryCode(),
            updatedLinkedDocuments
        );
    }

    private void createInvoice(FiscalDocumentEventValue eventValue) {
        var createEvent = eventValue.getInvoiceCreateEvent();

        var documentOptional = fiscalDocumentService.findById(createEvent.getId());
        if (documentOptional.isEmpty()) {
            log.error("Document not found for id: {}", createEvent.getId());
            throw new RuntimeException("Document not found");
        }
        var document = documentOptional.get();

        if (!areTransactionsValid(eventValue.getInvoiceCreateEvent().getTransactionIds())) {
            document.setStatus(DocumentStatus.FAILED);
            document.setFailReason("Transactions are already in use");
            fiscalDocumentService.save(document);
            return;
        }

        if (document.getStatus() != DocumentStatus.REQUESTED) {
            return;
        }

        if (Origin.INTEGRATION.equals(document.getOrigin())) {
            document.setStatus(DocumentStatus.PENDING_INTEGRATION);
            fiscalDocumentService.save(document);
            return;
        }

        var documentConfig =
            documentConfigService.getDocumentConfig(eventValue.getPropertyId(), DocumentKind.INVOICE);

        if (documentConfig.getDueDays() > 0) {
            document.setDueDate(document.getInvoiceDate().plusDays(documentConfig.getDueDays()));
        }

        var sourceKind = EnumConverterUtil.convert(eventValue.getSourceKind());
        var transactions = accountingService.getPostedTransactions(
            eventValue.getPropertyId(),
            createEvent.getTransactionIds(),
            eventValue.getSourceId(),
            EnumConverterUtil.convert(eventValue.getSourceKind())
        );

        var routedTransactions = getRoutedTransactions(transactions, eventValue.getPropertyId());

        var sequence = getDocumentSequence(
            createEvent.getSequenceId(),
            documentConfig,
            sourceKind
        );

        var invoiceNumber = documentConfig.getNumber(sourceKind, sequence, null);
        var property = propertyServiceClient.getProperty(eventValue.getPropertyId());
        var transactionsDto = convertToDto(transactions);
        var templateKeys = buildTemplateKeys(
            transactionsDto,
            convertToDto(routedTransactions),
            invoiceNumber,
            property,
            eventValue.getSourceId(),
            sourceKind,
            documentConfig,
            document
        );

        storeAndUpdateDocument(
            document,
            documentConfig,
            sourceKind,
            invoiceNumber,
            Currency.valueOf(property.getCurrency()),
            transactionsDto,
            templateKeys,
            property.getPropertyProfile().getHotelAddress().getCountryCode(),
            Set.of());
    }

    private void storeAndUpdateDocument(
        FiscalDocument document,
        DocumentConfig documentConfig,
        SourceKind sourceKind,
        String number,
        Currency currency,
        List<TransactionDto> transactions,
        InvoiceTemplateKeys templateKeys,
        String countryCode,
        Set<FiscalDocument> updatedLinkedDocuments
    ) {
        document.setNumber(number);
        if (document.getStatus().equals(DocumentStatus.FAILED_INTEGRATION)) {
            document.setStatus(DocumentStatus.CORRECTION_NEEDED);
        } else {
            document.setStatus(DocumentStatus.OPEN);
        }

        calculateTransactionAmounts(document, transactions, currency);

        var html = documentConfig.getHtml(sourceKind);
        if (html == null) {
            var path = DocumentTemplateLists.getDefaultTemplate(
                sourceKind,
                document.getKind(),
                documentConfig.isCompact(),
                countryCode
            );

            html = ResourceFileReader.readFileFromResources(path);
        }

        var template = TemplateRenderer.renderTemplate(
            html,
            templateKeys.toMap()
        );
        var file = PdfBuilder.buildPdfFromHtml(template);
        var filePath = fiscalDocumentFileService.uploadFile(file, document, "pdf");
        document.setFilePath(filePath);

        if (updatedLinkedDocuments == null || updatedLinkedDocuments.isEmpty()) {
            fiscalDocumentService.save(document);
        } else {
            fiscalDocumentService.saveAll(SetUtils.union(Set.of(document), updatedLinkedDocuments));
        }
    }

    private boolean areTransactionsValid(List<Long> transactionIds) {
        var invalidTransactions =
            fiscalDocumentTransactionRepository.findAllByTransactionIdInAndFiscalDocumentStatusIn(
                transactionIds,
                DocumentStatus.getActiveStatuses()
            );

        return invalidTransactions.isEmpty();
    }


    private List<Transaction> getRoutedTransactions(List<Transaction> transactions, Long request) {
        if (transactions == null || transactions.isEmpty()) {
            return List.of();
        }

        var routedFromIds = transactions.stream()
            .filter(Transaction::hasRoutedFrom)
            .map(Transaction::getRoutedFrom)
            .toList();

        return routedFromIds.isEmpty()
            ? List.of()
            : accountingService.getRoutedTransactions(request, routedFromIds);
    }

    private DocumentSequence getDocumentSequence(Long requestedId, DocumentConfig config, SourceKind sourceKind) {
        DocumentSequence sequence;
        if (requestedId != null) {
            sequence = documentSequenceService.getSequenceWithLock(requestedId, config.getPropertyId());
        } else {
            var defaultSequence = config.getSequence(sourceKind);
            sequence =
                documentSequenceService.getSequenceWithLock(defaultSequence.getId(), config.getPropertyId());
        }
        return sequence;
    }

    private InvoiceTemplateKeys buildTemplateKeys(
        List<TransactionDto> transactions,
        List<TransactionDto> routedTransactions,
        String invoiceNumber,
        Property property,
        Long sourceId,
        SourceKind sourceKind,
        DocumentConfig documentConfig,
        FiscalDocument document
    ) {
        var bookingIds = routedTransactions.stream()
            .filter(transaction -> transaction.getSource().equals(Source.SOURCE_RESERVATION))
            .map(TransactionDto::getSourceId)
            .collect(Collectors.toSet());

        var bookingMap = new HashMap<Long, BookingWithRooms>();
        var bookings = bookingService.getBookings(property.getId(), bookingIds);
        bookings.forEach(booking -> bookingMap.put(booking.getBooking().getId(), booking));

        var templateKeys = new InvoiceTemplateKeys(property.getLang());

        var dateFormatter = getDateTimeFormatter(property);

        if (sourceKind.equals(SourceKind.RESERVATION)) {
            addReservationData(
                property,
                sourceId,
                documentConfig,
                bookingMap,
                templateKeys,
                dateFormatter
            );
        }

        if (sourceKind.equals(SourceKind.GROUP_PROFILE)) {
            addGroupProfileData(property.getId(), sourceId, templateKeys);
        }

        var roomsMap = bookingMap.values()
            .stream()
            .map(BookingWithRooms::getRoomsList)
            .flatMap(List::stream)
            .collect(Collectors.toMap(BookingRoom::getId, Function.identity()));

        transactionDataExtractor.addTemplateKeysFromTransactions(
            templateKeys,
            transactions,
            routedTransactions,
            property,
            documentConfig.isShowDetailedTaxFee(),
            documentConfig.isIncludeRoomNumber(),
            documentConfig.isCompact(),
            bookingMap,
            roomsMap,
            dateFormatter
        );

        configDataExtractor.addTemplateKeysFromConfig(templateKeys, documentConfig, property.getLang());
        propertyProfileDataExtractor.addTemplateKeysFromPropertyProfile(templateKeys, property);

        addTemplateSpecificData(
                invoiceNumber, documentConfig, property, templateKeys, dateFormatter, document);

        return templateKeys;
    }

    private void addTemplateSpecificData(String invoiceNumber, DocumentConfig documentConfig, Property property,
                                         InvoiceTemplateKeys templateKeys, DateTimeFormatter dateFormatter,
                                         FiscalDocument fiscalDocument) {
        if (property.getPropertyProfile().getHotelAddress().getCountryCode().equals("BR")) {
            templateKeys.setCpf(documentConfig.getCpf());
        }

        if (documentConfig.isShowLegalCompanyName()) {
            templateKeys.setLegalCompanyName(property.getPropertyProfile().getBusinessName());
        }

        var dateNow = fiscalDocument.getInvoiceDate();
        if (dateNow == null) {
            var propertyZone = TimeZone.getTimeZone(property.getTimezone()).toZoneId();
            dateNow = ZonedDateTime.now().withZoneSameInstant(propertyZone).toLocalDate();
        }
        templateKeys.setInvoiceDate(dateNow.format(dateFormatter));
        templateKeys.setInvoiceDateNotFormatted(dateNow);

        templateKeys.setInvoiceNumber(invoiceNumber);

        if (fiscalDocument.getDueDate() != null) {
            templateKeys.setDueDate(fiscalDocument.getDueDate().format(dateFormatter));
        }
    }

    private DateTimeFormatter getDateTimeFormatter(Property property) {
        var dateFormatter = DateTimeFormatter.ISO_DATE;
        if (StringUtils.hasText(property.getDateFormat())) {
            dateFormatter = DateTimeFormatter.ofPattern(property.getDateFormat()
                .replace("d", "dd")
                .replace("m", "MM")
                .replace("Y", "yyyy")
            );
        }
        return dateFormatter;
    }

    private void addGroupProfileData(Long propertyId, Long groupId, InvoiceTemplateKeys templateKeys) {
        var groupProfilesMap = groupProfileServiceClient
            .listGroups(propertyId, List.of(groupId))
            .stream()
            .findFirst();

        templateKeys.setGroupName(groupProfilesMap.map(GroupProfile::getName).orElse("-"));
        templateKeys.setGroupCode(groupProfilesMap.map(GroupProfile::getCode).orElse("-"));

        //TODO: fix this
        templateKeys.setOccupantNameLabel("Label");
        templateKeys.setOccupantName("name");
        templateKeys.setPdfEmail("email");
        templateKeys.setReservationFirstName("firstName");
        templateKeys.setReservationLastName("LastName");
        templateKeys.setCheckIn("checkin");
        templateKeys.setCheckOut("checkout");
        templateKeys.setNights("5");
        templateKeys.setZip("zip");
        templateKeys.setState("state");
        templateKeys.setCompanyName("companyName");
        templateKeys.setAddress("address");
        templateKeys.setAddress2("address2");
        templateKeys.setCountryName("country");
        templateKeys.setWorkPhone("work phone");
        templateKeys.setCellPhone("cell phone");
        templateKeys.setPhone("phone");
        templateKeys.setReservationDate("date");
    }

    private void addReservationData(
        Property property,
        Long sourceId,
        DocumentConfig documentConfig,
        HashMap<Long, BookingWithRooms> bookingMap,
        InvoiceTemplateKeys templateKeys,
        DateTimeFormatter dateFormatter
    ) {
        var booking = bookingService.getBooking(property.getId(), sourceId);
        bookingMap.put(sourceId, booking);

        reservationDataExtractor.addTemplateKeysFromReservation(
            property,
            templateKeys,
            documentConfig,
            booking,
            dateFormatter
        );
    }

    private void calculateTransactionAmounts(
        FiscalDocument fiscalDocument,
        List<TransactionDto> transactions,
        Currency currency
    ) {
        var amount = BigInteger.ZERO;
        var paidValue = BigInteger.ZERO;

        for (var transaction : transactions) {
            if (!transaction.getInternalCode().getGroup().equals(InternalCodeGroup.INTERNAL_CODE_GROUP_PAYMENT)) {
                amount = amount.add(transaction.getAmountMinorUnits());
            } else {
                paidValue = paidValue.add(transaction.getAmountMinorUnits());
            }
        }

        fiscalDocument.setCurrency(currency.getIsoCode());
        fiscalDocument.setAmount(amount.longValue());
        fiscalDocument.setBalance(amount.add(paidValue).longValue());
    }

    private List<Transaction> getTransactions(
        FiscalDocument parentDocument,
        FiscalDocument document,
        boolean isVoid,
        List<Long> documentTransactions
    ) {
        var transactionIds = new ArrayList<Long>();
        if (isVoid) {
            parentDocument.setStatus(DocumentStatus.VOIDED);
            fiscalDocumentService.save(parentDocument);

            parentDocument.getTransactionList()
                .stream()
                .map(FiscalDocumentTransaction::getTransactionId)
                .forEach(transactionIds::add);

            parentDocument.getLinkedDocuments()
                .stream()
                .map(FiscalDocumentLinkedDocument::getLinkedDocument)
                .filter(linkedDocument -> linkedDocument.getKind() == DocumentKind.CREDIT_NOTE)
                .filter(childDocument -> CreationMethod.ADJUSTMENT.equals(childDocument.getMethod()))
                .flatMap(linkedDocument -> linkedDocument.getTransactionList()
                    .stream()
                    .map(FiscalDocumentTransaction::getTransactionId)
                ).forEach(transactionIds::add);
        } else {
            transactionIds.addAll(documentTransactions);
        }

        return accountingService.getPostedTransactions(
            document.getPropertyId(),
            transactionIds,
            document.getSourceId(),
            document.getSourceKind()
        );
    }

}
