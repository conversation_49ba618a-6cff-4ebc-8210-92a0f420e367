package com.cloudbeds.fiscaldocument.eventhandlers.fiscaldocument;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import com.cloudbeds.distributedid.pool.IdPool;
import com.cloudbeds.fiscaldocument.converters.FiscalDocumentCdcConverter;
import com.cloudbeds.fiscaldocument.entity.DocumentConfig;
import com.cloudbeds.fiscaldocument.entity.DocumentSequence;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentTransaction;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument_;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import com.cloudbeds.fiscaldocument.enums.Origin;
import com.cloudbeds.fiscaldocument.grpc.accounting.AccountingService;
import com.cloudbeds.fiscaldocument.grpc.organization.PropertyServiceClient;
import com.cloudbeds.fiscaldocument.models.FiscalDocumentCdcModel;
import com.cloudbeds.fiscaldocument.models.FiscalDocumentGroupKey;
import com.cloudbeds.fiscaldocument.models.FiscalDocumentResult;
import com.cloudbeds.fiscaldocument.services.DocumentConfigService;
import com.cloudbeds.fiscaldocument.services.DocumentSequenceService;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentService;
import com.cloudbeds.fiscaldocument.utils.EnumConverterUtil;
import com.cloudbeds.organization.v1.Property;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

@Slf4j
@Component
@RequiredArgsConstructor
public class CdcEventHandler {
    private final PropertyServiceClient propertyServiceClient;
    private final DocumentSequenceService documentSequenceService;
    private final FiscalDocumentService fiscalDocumentService;
    private final TransactionTemplate transactionTemplate;
    private final AccountingService accountingService;
    private final DocumentConfigService documentConfigService;
    private final IdPool idPool;
    private final FiscalDocumentCdcConverter fiscalDocumentCdcConverter;

    /**
     * Process CDC messages and update the fiscal documents in the database.
     *
     * @param events List of CDC events to process
     */
    public void processCdcMessages(List<FiscalDocumentEventValue> events) {
        if (events.isEmpty()) {
            return;
        }

        var cdcRecords = events.stream()
            .map(fiscalDocumentCdcConverter::convert)
            .flatMap(List::stream)
            .toList();

        var propertyIds = cdcRecords.stream()
            .map(FiscalDocumentCdcModel::getPropertyId)
            .distinct()
            .toList();

        var propertyMap = propertyServiceClient.listProperties(propertyIds)
            .stream()
            .collect(Collectors.toMap(
                Property::getId,
                property -> property
            ));

        var transactionIds = cdcRecords.stream()
            .map(FiscalDocumentCdcModel::getTransactionIds)
            .flatMap(List::stream)
            .distinct()
            .map(id -> Base64.getEncoder().encodeToString(id.getBytes()))
            .toList();

        var mfdTransactionToAccountingIds = accountingService.getTransactionIdsFromMfdTransactions(transactionIds);

        transactionTemplate.executeWithoutResult(status -> {
            var existingRecords = fiscalDocumentService.findAllBySpecification(prepareSpecifications(cdcRecords));
            var propertySequences = documentSequenceService.findAllByPropertyIds(propertyIds)
                .stream()
                .collect(Collectors.toMap(
                    DocumentSequence::getId,
                    sequence -> sequence
                ));

            var documentConfigs = documentConfigService.getDocumentConfigsByPropertyIds(propertyIds)
                .stream()
                .collect(Collectors.toMap(
                    config -> Pair.of(config.getPropertyId(), config.getDocumentKind()),
                    config -> config
                ));

            var existingRecordsMap = existingRecords.stream()
                .collect(Collectors.toMap(
                    this::getGroupKey,
                    fiscalDocument -> fiscalDocument,
                    (existing, replacement) -> replacement
                ));
            var result = new FiscalDocumentResult();
            for (var record : cdcRecords) {
                var existingRecord = existingRecordsMap.get(record.getGroupKey());
                if (existingRecord != null) {
                    result.addResult(updateExistingRecord(existingRecord, record, propertySequences, documentConfigs));
                } else {
                    try {
                        result.addResult(createNewRecord(
                            record,
                            mfdTransactionToAccountingIds,
                            propertyMap,
                            existingRecordsMap,
                            propertySequences,
                            documentConfigs
                        ));
                    } catch (InterruptedException e) {
                        log.error("Error creating new record: {}", e.getMessage(), e);
                        throw new RuntimeException(e);
                    }
                }
            }

            documentSequenceService.saveAll(result.getSequences());
            fiscalDocumentService.saveAll(result.getDocuments());
        });
    }

    private FiscalDocumentResult createNewRecord(
        FiscalDocumentCdcModel record,
        Map<String, List<Long>> mfdTransactionToAccountingIds,
        Map<Long, Property> propertyMap,
        Map<FiscalDocumentGroupKey, FiscalDocument> existingRecordsMap,
        Map<Long, DocumentSequence> propertySequences, Map<Pair<Long, DocumentKind>, DocumentConfig> documentConfigs
    ) throws InterruptedException {
        var propertyId = record.getPropertyId();
        if (!propertyMap.containsKey(propertyId)) {
            log.error("Missing property for id: {}", propertyId);
            throw new RuntimeException();
        }

        var timezone = ZoneId.of("UTC");
        try {
            timezone = ZoneId.of(propertyMap.get(propertyId).getTimezone());
        } catch (Exception e) {
            log.error("Error parsing timezone {} for property id: {}", propertyMap.get(propertyId), propertyId);
        }

        var fiscalDocument = new FiscalDocument();
        fiscalDocument.setId(idPool.nextIdWithRetry(5, 1000));
        fiscalDocument.setPropertyId(propertyId);
        fiscalDocument.setExternalId(record.getId().toString());
        fiscalDocument.setOrigin(Origin.MFD);
        fiscalDocument.setSourceId(record.getSourceId());
        fiscalDocument.setSourceKind(record.getSourceKind());
        fiscalDocument.setKind(record.getDocumentKind());
        fiscalDocument.setInvoiceDate(LocalDate.from(record.getDate().atZone(timezone)));
        fiscalDocument.setCurrency(record.getCurrency());
        fiscalDocument.setAmount(record.getAmount());
        fiscalDocument.setBalance(record.getBalance());
        fiscalDocument.setTransactionList(record.getTransactionIds().stream()
            .flatMap(mfdId -> {
                var base64Id = Base64.getEncoder().encodeToString(mfdId.getBytes());
                var accountingTransactionsIds = mfdTransactionToAccountingIds.getOrDefault(base64Id, List.of());
                if (accountingTransactionsIds.isEmpty()) {
                    log.debug(
                        "Missing accounting transaction for mfd transaction id, invoiceId: {}, {}",
                        mfdId,
                        record.getId()
                    );
                }
                return accountingTransactionsIds.stream();
            })
            .distinct()
            .map(id -> {
                var transaction = new FiscalDocumentTransaction();
                transaction.setTransactionId(id);
                transaction.setFiscalDocumentId(fiscalDocument.getId());
                return transaction;
            })
            .toList()
        );
        fiscalDocument.setRecipients(
            record.getRecipients().stream()
                .peek(recipient -> recipient.setFiscalDocumentId(fiscalDocument.getId()))
                .toList()
        );
        fiscalDocument.setUserId(0L);

        existingRecordsMap.put(getGroupKey(fiscalDocument), fiscalDocument);
        return updateExistingRecord(fiscalDocument, record, propertySequences, documentConfigs);
    }

    private FiscalDocumentResult updateExistingRecord(FiscalDocument fiscalDocument, FiscalDocumentCdcModel record,
                                                      Map<Long, DocumentSequence> propertySequences,
                                                      Map<Pair<Long, DocumentKind>, DocumentConfig> documentConfigs) {
        var documentConfig = documentConfigs.get(Pair.of(record.getPropertyId(), record.getDocumentKind()));

        var documentSequence = propertySequences.get(documentConfig.getSequence(record.getSourceKind()).getId());

        var result = new FiscalDocumentResult();
        if (record.getNumber() != null && documentSequence.getNumber() < record.getNumber()) {
            documentSequence.setNumber(record.getNumber());
            result.getSequences().add(documentSequence);
        }
        var documentNumber = documentConfig.getNumber(
            fiscalDocument.getSourceKind(), documentSequence, record.getExternalSettingsId());
        fiscalDocument.setNumber(documentNumber.fullNumber());
        fiscalDocument.setSequenceNumber(documentNumber.sequenceNumber());
        fiscalDocument.setUrl(record.getPfdUrl());

        if (record.getDocumentKind().equals(DocumentKind.INVOICE)) {
            fiscalDocument.setStatusDirectly(EnumConverterUtil.convert(record.getStatus()));
        } else {
            fiscalDocument.setStatusDirectly(DocumentStatus.COMPLETED);
        }

        result.getDocuments().add(fiscalDocument);

        return result;
    }

    private FiscalDocumentGroupKey getGroupKey(FiscalDocument fiscalDocument) {
        return new FiscalDocumentGroupKey(
            fiscalDocument.getPropertyId(),
            fiscalDocument.getExternalId(),
            fiscalDocument.getKind()
        );
    }

    private Specification<FiscalDocument> prepareSpecifications(List<FiscalDocumentCdcModel> models) {
        return (root, query, criteriaBuilder) -> {
            var predicates = criteriaBuilder.disjunction();
            for (var model : models) {
                var predicate = criteriaBuilder.and(
                    criteriaBuilder.equal(root.get(FiscalDocument_.PROPERTY_ID), model.getPropertyId()),
                    criteriaBuilder.equal(root.get(FiscalDocument_.EXTERNAL_ID), model.getId()),
                    criteriaBuilder.equal(root.get(FiscalDocument_.ORIGIN), Origin.MFD),
                    criteriaBuilder.equal(root.get(FiscalDocument_.KIND), model.getDocumentKind())
                );
                predicates = criteriaBuilder.or(predicates, predicate);
            }
            return predicates;
        };
    }
}
