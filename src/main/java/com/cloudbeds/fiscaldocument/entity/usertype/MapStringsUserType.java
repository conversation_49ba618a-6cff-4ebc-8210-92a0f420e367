package com.cloudbeds.fiscaldocument.entity.usertype;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.Serializable;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.HashMap;
import java.util.Map;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.type.SqlTypes;
import org.hibernate.usertype.UserType;

public class MapStringsUserType implements UserType<Map<String, String>> {
    public static final ObjectMapper MAPPER = new ObjectMapper().registerModule(new JavaTimeModule());

    @Override
    public boolean equals(Map<String, String> x, Map<String, String> y) {
        if (x == null || y == null) {
            return x == y;
        }
        return x.equals(y);
    }

    @Override
    public int hashCode(Map<String, String> x) {
        return x.hashCode();
    }

    @Override
    public int getSqlType() {
        return SqlTypes.JSON;
    }

    @Override
    public Class returnedClass() {
        return Map.class;
    }

    @Override
    public Map<String, String> nullSafeGet(
        ResultSet rs, int position, SharedSessionContractImplementor session,
        Object owner
    ) throws SQLException {
        final String cellContent = rs.getString(position);
        if (cellContent == null) {
            return null;
        }
        try {
            return MAPPER.readValue(cellContent.getBytes(StandardCharsets.UTF_8), new TypeReference<>() {});
        } catch (final Exception ex) {
            throw new RuntimeException(
                "Failed to convert String to MyJson: " + ex.getMessage(),
                ex
            );
        }
    }

    @Override
    public void nullSafeSet(
        PreparedStatement st, Map<String, String> value, int index,
        SharedSessionContractImplementor session
    ) throws SQLException {
        if (value == null) {
            st.setNull(index, Types.OTHER);
            return;
        }
        try {
            final StringWriter w = new StringWriter();
            MAPPER.writeValue(w, value);
            w.flush();
            st.setObject(index, w.toString(), Types.OTHER);
        } catch (final Exception ex) {
            throw new RuntimeException(
                "Failed to convert Object to String: " + ex.getMessage(),
                ex
            );
        }
    }

    @Override
    public Map<String, String> deepCopy(Map<String, String> value) {
        if (value == null) {
            return null;
        }

        return new HashMap<>(value);
    }

    @Override
    public boolean isMutable() {
        return false;
    }

    @Override
    public Serializable disassemble(Map<String, String> value) {
        return (Serializable) value;
    }

    @Override
    public Map<String, String> assemble(Serializable cached, Object owner) {
        return cached == null ? null : (Map<String, String>) cached;
    }
}
