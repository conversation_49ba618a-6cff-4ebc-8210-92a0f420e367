package com.cloudbeds.fiscaldocument.entity.usertype;

import com.cloudbeds.fiscaldocument.entity.GovernmentIntegration;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.Serializable;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Map;
import java.util.Objects;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.type.SqlTypes;
import org.hibernate.usertype.UserType;

public class GovernmentIntegrationUserType implements UserType<GovernmentIntegration> {
    public static final ObjectMapper MAPPER = new ObjectMapper().registerModule(new JavaTimeModule());

    @Override
    public int getSqlType() {
        return SqlTypes.JSON;
    }

    @Override
    public Class returnedClass() {
        return Map.class;
    }

    @Override
    public boolean equals(GovernmentIntegration x, GovernmentIntegration y) {
        return Objects.equals(x, y);
    }

    @Override
    public int hashCode(GovernmentIntegration x) {
        return x.hashCode();
    }

    @Override
    public GovernmentIntegration nullSafeGet(
        ResultSet rs, int position, SharedSessionContractImplementor session,
        Object owner
    ) throws SQLException {
        final String cellContent = rs.getString(position);
        if (cellContent == null) {
            return null;
        }
        try {
            return MAPPER.readValue(cellContent.getBytes(StandardCharsets.UTF_8), new TypeReference<>() {});
        } catch (final Exception ex) {
            throw new RuntimeException(
                "Failed to convert String to MyJson: " + ex.getMessage(),
                ex
            );
        }
    }

    @Override
    public void nullSafeSet(
        PreparedStatement st, GovernmentIntegration value, int index,
        SharedSessionContractImplementor session
    ) throws SQLException {
        if (value == null) {
            st.setNull(index, Types.OTHER);
            return;
        }
        try {
            final StringWriter w = new StringWriter();
            MAPPER.writeValue(w, value);
            w.flush();
            st.setObject(index, w.toString(), Types.OTHER);
        } catch (final Exception ex) {
            throw new RuntimeException(
                "Failed to convert Object to String: " + ex.getMessage(),
                ex
            );
        }
    }

    @Override
    public GovernmentIntegration deepCopy(GovernmentIntegration value) {
        return value;
    }

    @Override
    public boolean isMutable() {
        return false;
    }

    @Override
    public Serializable disassemble(GovernmentIntegration value) {
        return null;
    }

    @Override
    public GovernmentIntegration assemble(Serializable cached, Object owner) {
        return null;
    }
}
