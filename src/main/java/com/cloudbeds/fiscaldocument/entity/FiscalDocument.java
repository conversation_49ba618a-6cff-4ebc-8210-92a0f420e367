package com.cloudbeds.fiscaldocument.entity;

import com.cloudbeds.fiscaldocument.entity.usertype.GovernmentIntegrationUserType;
import com.cloudbeds.fiscaldocument.enums.CreationMethod;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import com.cloudbeds.fiscaldocument.enums.Origin;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.Version;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;

import static com.cloudbeds.fiscaldocument.support.exception.ErrorCode.INVALID_ARGUMENT_ERROR;

@Entity
@Getter
@Setter
@Table(name = "fiscal_document")
public class FiscalDocument {
    @DistributedId
    @Id
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "number", nullable = false)
    private String number;

    @Column(name = "sequence_number")
    private Long sequenceNumber;

    @Column(name = "property_id", nullable = false)
    private Long propertyId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "source_id", nullable = false)
    private Long sourceId;

    @Column(name = "source_kind", nullable = false)
    @Enumerated(EnumType.STRING)
    private SourceKind sourceKind;

    @Enumerated(EnumType.STRING)
    private DocumentKind kind;

    @Enumerated(EnumType.STRING)
    private Origin origin;

    @Column(name = "invoice_date", nullable = false)
    private LocalDate invoiceDate;

    @Enumerated(EnumType.STRING)
    private DocumentStatus status;

    @Enumerated(EnumType.STRING)
    @Column(name = "method")
    private CreationMethod method;

    @Column(name = "external_id")
    private String externalId;

    @Column(name = "file_path")
    private String filePath;

    @Column(name = "url")
    private String url;

    @Column(name = "fail_reason")
    private String failReason;

    @Column(name = "currency")
    private String currency;

    @Column(name = "amount")
    private Long amount;

    @Column(name = "balance")
    private Long balance;

    @Type(GovernmentIntegrationUserType.class)
    @Column(name = "government_integration", columnDefinition = "jsonb")
    private GovernmentIntegration governmentIntegration;

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "fiscal_document_id", referencedColumnName = "id")
    private List<FiscalDocumentTransaction> transactionList = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "linked_document_id", referencedColumnName = "id")
    private List<FiscalDocumentLinkedDocument> linkedChildDocuments = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "fiscal_document_id", referencedColumnName = "id")
    private List<FiscalDocumentLinkedDocument> linkedDocuments = new ArrayList<>();

    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinColumn(name = "fiscal_document_id", referencedColumnName = "id", insertable = false, updatable = false)
    private List<FiscalDocumentRecipient> recipients = new ArrayList<>();

    @Column(name = "due_date")
    private LocalDate dueDate;

    @Column(name = "created_at", updatable = false, nullable = false)
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at", columnDefinition = "timestamp")
    @UpdateTimestamp
    private LocalDateTime updatedAt = LocalDateTime.now();

    @Version
    @Column(name = "version")
    private int version;

    @Transient
    private Long sequenceId;

    @Transient
    private String reason;

    /**
     * Add a document to the list of linked documents.
     *
     * @param linkedDocument the linkedDocument to add
     */
    public void addLinkedDocument(FiscalDocumentLinkedDocument linkedDocument) {
        if (linkedDocuments == null) {
            linkedDocuments = new ArrayList<>();
        }
        linkedDocuments.add(linkedDocument);
    }

    /**
     * Set the status of the document.
     *
     * @param status status
     */
    public void setStatus(DocumentStatus status) {
        if (this.status != null && !this.status.equals(status)) {
            if (!DocumentStatus.getAllowedStatusChanges(this.status).contains(status)) {
                throw new FiscalDocumentException(
                    INVALID_ARGUMENT_ERROR,
                    "Invalid status change from " + this.status + " to " + status
                );
            }
        }

        this.status = status;
    }

    /**
     * Sets the status of the document directly without any validation checks.
     * To be used only for CDC event handler logic.
     *
     * @param status the new status to assign to the document
     */
    public void setStatusDirectly(DocumentStatus status) {
        this.status = status;
    }
}