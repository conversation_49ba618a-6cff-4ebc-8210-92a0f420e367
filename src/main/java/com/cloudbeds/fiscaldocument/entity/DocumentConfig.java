package com.cloudbeds.fiscaldocument.entity;

import com.cloudbeds.fiscaldocument.entity.usertype.MapStringsUserType;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.util.StringUtils;

@Entity
@Getter
@Setter
@Table(name = "document_config")
@IdClass(CompoundDocumentConfigKey.class)
public class DocumentConfig {
    @Id
    @Column(name = "property_id", nullable = false)
    private Long propertyId;

    @Column(name = "document_kind", nullable = false)
    @Enumerated(EnumType.STRING)
    private DocumentKind documentKind;

    @Column(name = "show_detailed_tax_fee", nullable = false)
    private boolean showDetailedTaxFee;

    @Column(name = "charge_breakdown", nullable = false)
    private boolean chargeBreakdown;

    @Column(name = "use_guest_lang", nullable = false)
    private boolean useGuestLang;

    private int dueDays;

    @Column(name = "lang")
    private String lang;

    @Column(name = "prefix")
    private String prefix;

    @Column(name = "suffix")
    private String suffix;

    @Column(name = "legal_company_name")
    private String legalCompanyName;

    @Column(name = "img_url")
    private String imgUrl;

    @Column(name = "title")
    @Type(MapStringsUserType.class)
    private Map<String, String> title = new HashMap<>();

    @Column(name = "show_legal_company_name", nullable = false)
    private boolean showLegalCompanyName = false;

    @Column(name = "include_room_number", nullable = false)
    private boolean includeRoomNumber = false;

    @Column(name = "use_document_number", nullable = false)
    private boolean useDocumentNumber = false;

    @Column(name = "is_compact", nullable = false)
    private boolean isCompact = false;

    @Column(name = "tax_id1")
    private String taxId1;

    @Column(name = "tax_id2")
    private String taxId2;

    @Column(name = "cpf")
    private String cpf;

    @Column(name = "custom_text")
    @Type(MapStringsUserType.class)
    private Map<String, String> customText = new HashMap<>();

    @OneToMany(fetch = FetchType.EAGER, mappedBy = "documentConfig", cascade = CascadeType.ALL)
    private Set<DocumentContent> documentContents = new HashSet<>();

    @Column(name = "created_at", updatable = false, nullable = false)
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at", columnDefinition = "timestamp")
    @UpdateTimestamp
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * Get document sequence by source kind.
     *
     * @param sourceKind Source kind
     * @return Document sequence
     */
    public DocumentSequence getSequence(SourceKind sourceKind) {
        return documentContents.stream()
            .filter(DocumentContent::isActive)
            .filter(c -> c.getSourceKind() == null || c.getSourceKind().equals(sourceKind))
            .findFirst()
                .map(DocumentContent::getDocumentSequence)
            .orElse(null);
    }

    /**
     * Get document number by source kind.
     *
     * @param sourceKind Source kind
     * @param sequence Document sequence
     * @param externalSettingsId settings id
     * @return DocumentNumber
     */
    public DocumentNumber getNumber(SourceKind sourceKind, DocumentSequence sequence, Long externalSettingsId) {
        return getNumber(sourceKind, sequence.getNextNumber(), externalSettingsId);
    }

    /**
     * Get document number by source kind.
     *
     * @param sourceKind Source kind
     * @param sequenceNumber Document sequence number
     * @param externalSettingsId settings id
     * @return DocumentNumber
     */
    public DocumentNumber getNumber(SourceKind sourceKind, Long sequenceNumber, Long externalSettingsId) {
        DocumentContent historicalContent = null;
        if (externalSettingsId != null) {
            historicalContent = documentContents.stream()
                .filter(c -> Objects.equals(c.getExternalId(), externalSettingsId)
                    && (c.getSourceKind() == null || c.getSourceKind().equals(sourceKind)))
                .findFirst().orElse(null);
        }

        var content = historicalContent != null
            ? historicalContent
            : documentContents.stream()
                .filter(DocumentContent::isActive)
                .filter(c -> c.getSourceKind() == null || c.getSourceKind().equals(sourceKind))
                .findFirst()
                .orElse(null);

        var invoicePrefix = prefix;
        var invoiceSuffix = suffix;

        if (content != null && !StringUtils.hasText(content.getPrefix())) {
            invoicePrefix = content.getPrefix();
        }

        if (content != null && !StringUtils.hasText(content.getSuffix())) {
            invoiceSuffix = content.getSuffix();
        }

        var fullNumber = sequenceNumber.toString();
        if (StringUtils.hasText(invoicePrefix)) {
            fullNumber = invoicePrefix + "-" + fullNumber;
        }

        if (StringUtils.hasText(invoiceSuffix)) {
            fullNumber = fullNumber + "-" + invoiceSuffix;
        }

        return new DocumentNumber(fullNumber, sequenceNumber);
    }

    /**
     * Get HTML by source kind.
     *
     * @param sourceKind Source kind
     * @return HTML
     */
    public String getHtml(SourceKind sourceKind) {
        return documentContents.stream()
            .filter(content -> content.getSourceKind() == null || content.getSourceKind().equals(sourceKind))
            .filter(DocumentContent::isActive)
            .map(DocumentContent::getHtml)
            .filter(Objects::nonNull)
            .findFirst().orElse(null);
    }

    /**
     * Add content to document config.
     *
     * @param documentContent documentContent
     */
    public void addContent(DocumentContent documentContent) {
        documentContents.stream()
            .filter(content -> documentContent.getSourceKind().equals(content.getSourceKind()))
            .filter(content -> documentContent.getDocumentKind().equals(content.getDocumentKind()))
            .filter(DocumentContent::isActive)
            .forEach(content -> content.setActive(false));

        documentContents.add(documentContent);
    }

    /**
     * Get custom text for specific language.
     *
     * @param lang lang
     * @return custom text
     */
    public String getCustomTextForLang(String lang) {
        if (customText == null || customText.isEmpty()) {
            return null;
        }

        return customText.get(lang);
    }

    /**
     * Get title for specific language.
     *
     * @param lang lang
     * @return title
     */
    public String getTitleForLang(String lang) {
        if (title == null || title.isEmpty()) {
            return null;
        }

        return title.get(lang);
    }

    public record DocumentNumber(
        String fullNumber,
        Long sequenceNumber
    ) {}
}