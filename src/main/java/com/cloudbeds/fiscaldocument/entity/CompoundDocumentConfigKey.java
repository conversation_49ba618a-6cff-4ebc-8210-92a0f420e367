package com.cloudbeds.fiscaldocument.entity;

import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class CompoundDocumentConfigKey implements Serializable {
    private Long propertyId;
    private DocumentKind documentKind;
}
