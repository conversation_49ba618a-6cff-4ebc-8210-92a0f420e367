package com.cloudbeds.fiscaldocument.entity;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class CompoundFiscalDocumentRecipientKey implements Serializable {
    private Long fiscalDocumentId;
    private Long recipientId;
}