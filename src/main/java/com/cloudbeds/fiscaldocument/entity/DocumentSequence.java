package com.cloudbeds.fiscaldocument.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "document_sequence")
@EqualsAndHashCode
public class DocumentSequence {
    @DistributedId
    @Id
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "property_id", nullable = false)
    private Long propertyId;

    @Column(name = "auto_reset", nullable = false)
    private boolean autoReset;

    @Column(name = "reset_datetime", columnDefinition = "timestamp")
    private LocalDateTime resetDatetime;

    @Column(name = "number")
    private Long number = 0L;

    @Column(name = "increment")
    private int increment = 1;

    @Column(name = "created_at", updatable = false, nullable = false)
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at", columnDefinition = "timestamp")
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * Get invoice next number by source kind.
     */
    public Long getNextNumber() {
        return number += increment;
    }
}