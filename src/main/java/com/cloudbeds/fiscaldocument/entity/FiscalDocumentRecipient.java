package com.cloudbeds.fiscaldocument.entity;

import com.cloudbeds.fiscaldocument.entity.usertype.RecipientUserType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "fiscal_document_recipient")
@Getter
@Setter
@IdClass(CompoundFiscalDocumentRecipientKey.class)
public class FiscalDocumentRecipient {
    @Id
    @Column(name = "fiscal_document_id", nullable = false)
    private Long fiscalDocumentId;

    @Id
    @Column(name = "recipient_id", nullable = false)
    private Long recipientId;

    @Type(RecipientUserType.class) // Or your custom JsonbUserType
    @Column(name = "recipient", columnDefinition = "jsonb")
    private Recipient recipient;
}

