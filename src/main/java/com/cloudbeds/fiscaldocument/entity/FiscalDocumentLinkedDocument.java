package com.cloudbeds.fiscaldocument.entity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "fiscal_document_linked_documents")
@IdClass(CompoundFiscalDocumentLinkedDocumentKey.class)
public class FiscalDocumentLinkedDocument {
    @Id
    @Column(name = "fiscal_document_id", nullable = false)
    private Long fiscalDocumentId;

    @Id
    @Column(name = "linked_document_id", nullable = false)
    private Long linkedDocumentId;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "fiscal_document_id", referencedColumnName = "id", updatable = false, insertable = false)
    private FiscalDocument fiscalDocument;

    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    @JoinColumn(name = "linked_document_id", referencedColumnName = "id", updatable = false, insertable = false)
    private FiscalDocument linkedDocument;
}