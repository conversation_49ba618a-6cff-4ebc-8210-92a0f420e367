package com.cloudbeds.fiscaldocument.entity;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Recipient {

    private String id;
    private String firstName;
    private String lastName;
    private String email;
    private RecipientType type;

    private Address address;
    private TaxInfo tax;
    private ContactDetails contactDetails;
    private Document document;

    // Holds dynamic country-specific data (e.g., "ES", "AR", "US")
    private Map<String, Object> countryData = new HashMap<>();

    @JsonAnySetter
    public void setCountryData(String key, Object value) {
        countryData.put(key, value);
    }

    @JsonAnyGetter
    public Map<String, Object> getCountryData() {
        return countryData;
    }

    @Getter
    @Setter
    public static class Address {
        private String address1;
        private String address2;
        private String city;
        private String state;
        private String zipCode;
        private String country;
    }

    @Getter
    @Setter
    public static class TaxInfo {
        private String id;
        private String companyName;
    }

    @Getter
    @Setter
    public static class ContactDetails {
        private String phone;
        private String gender;
        private String cellPhone;
        private Instant birthday;
    }

    @Getter
    @Setter
    public static class Document {
        private String type;
        private String number;
        private String issuingCountry;
        private Instant issueDate;
        private Instant expirationDate;
    }

    public enum RecipientType {
        COMPANY, PERSON
    }
}