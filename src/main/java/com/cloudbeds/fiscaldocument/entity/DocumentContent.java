package com.cloudbeds.fiscaldocument.entity;

import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinColumns;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Getter
@Setter
@Table(name = "document_content")
public class DocumentContent {
    @DistributedId
    @Id
    @Column(name = "id", unique = true, nullable = false)
    private Long id;

    @Column(name = "property_id", nullable = false)
    private Long propertyId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({
        @JoinColumn(name = "document_kind", referencedColumnName = "document_kind",
            insertable = false, updatable = false),
        @JoinColumn(name = "property_id", referencedColumnName = "property_id", insertable = false, updatable = false)
    })
    private DocumentConfig documentConfig;

    @Column(name = "external_id")
    Long externalId;

    @Column(name = "document_kind", nullable = false)
    @Enumerated(EnumType.STRING)
    private DocumentKind documentKind;

    @Column(name = "prefix")
    private String prefix;

    @Column(name = "suffix")
    private String suffix;

    @ManyToOne
    @JoinColumn(name = "default_sequence_id", referencedColumnName = "id", nullable = false)
    private DocumentSequence documentSequence;

    @Column(name = "source_kind")
    @Enumerated(EnumType.STRING)
    private SourceKind sourceKind;

    @Column(name = "html", nullable = false)
    private String html;

    private boolean active = true;

    @Column(name = "created_at", columnDefinition = "timestamp")
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at", columnDefinition = "timestamp")
    @UpdateTimestamp
    private LocalDateTime updatedAt = LocalDateTime.now();
}