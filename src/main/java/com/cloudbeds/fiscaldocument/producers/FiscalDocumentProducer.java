package com.cloudbeds.fiscaldocument.producers;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import java.util.concurrent.Future;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

@EnableKafka
@Slf4j
@RequiredArgsConstructor
@Component
public class FiscalDocumentProducer {
    private final KafkaTemplate<FiscalDocumentKey, FiscalDocumentValue> producerTemplate;

    @Value(value = "${topics.fiscal_documents}")
    private String topic;

    @Value(value = "${kafka.producers.enabled}")
    private Boolean isProducerEnabled;

    /**
     * Send fiscal document event.
     *
     * @param value FiscalDocumentValue
     * @return Future
     */
    public Future<SendResult<FiscalDocumentKey, FiscalDocumentValue>> send(FiscalDocumentValue value) {
        if (value == null || !isProducerEnabled) {
            return null;
        }

        var key = FiscalDocumentKey.newBuilder()
            .setId(value.getId())
            .build();

        try {
            return producerTemplate.send(topic, key, value);
        } catch (Exception e) {
            log.error("Error while sending FiscalDocument message", e);
            throw new RuntimeException(
                "Error while sending FiscalDocument message."
            );
        }
    }
}