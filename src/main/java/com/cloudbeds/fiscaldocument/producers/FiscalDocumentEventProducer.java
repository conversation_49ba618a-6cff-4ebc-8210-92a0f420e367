package com.cloudbeds.fiscaldocument.producers;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import java.util.concurrent.Future;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

@EnableKafka
@Slf4j
@RequiredArgsConstructor
@Component
public class FiscalDocumentEventProducer {
    private final KafkaTemplate<FiscalDocumentEventKey, FiscalDocumentEventValue> producerTemplate;

    @Value(value = "${topics.fiscal_document_events}")
    private String topic;

    @Value(value = "${kafka.producers.enabled}")
    private Boolean isProducerEnabled;

    /**
     * Send fiscal document event.
     *
     * @param value FiscalDocumentEventValue
     * @return Future
     */
    public Future<SendResult<FiscalDocumentEventKey, FiscalDocumentEventValue>> send(FiscalDocumentEventValue value) {
        if (value == null || !isProducerEnabled) {
            return null;
        }

        var key = FiscalDocumentEventKey.newBuilder()
            .setPropertyId(value.getPropertyId())
            .build();

        try {
            return producerTemplate.send(topic, key, value);
        } catch (Exception e) {
            throw new RuntimeException("Error while sending FiscalDocumentEvent message.", e);
        }
    }
}