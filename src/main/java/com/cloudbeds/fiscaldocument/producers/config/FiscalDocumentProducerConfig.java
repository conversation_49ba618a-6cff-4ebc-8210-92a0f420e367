package com.cloudbeds.fiscaldocument.producers.config;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentValue;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;

@Configuration
public class FiscalDocumentProducerConfig {
    /**
     * Create template for fiscal document cdc.
     *
     * @param properties Kafka properties
     * @return Kafka template
     */
    @Bean
    public KafkaTemplate<FiscalDocumentKey, FiscalDocumentValue>
        fiscalDocumentKafkaTemplate(KafkaProperties properties) {
        return new KafkaTemplate<>(
                new DefaultKafkaProducerFactory<>(properties.buildProducerProperties(null))
        );
    }
}
