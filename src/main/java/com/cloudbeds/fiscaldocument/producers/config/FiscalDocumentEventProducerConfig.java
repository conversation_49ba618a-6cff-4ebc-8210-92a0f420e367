package com.cloudbeds.fiscaldocument.producers.config;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventKey;
import com.cloudbeds.FiscalDocumentService.FiscalDocumentEventValue;
import io.confluent.kafka.serializers.KafkaAvroSerializer;
import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;

@Configuration
public class FiscalDocumentEventProducerConfig {

    /**
     * Create template for fiscal document events.
     *
     * @param properties Kafka properties
     * @return Kafka template
     */
    @Profile("!local")
    @Bean("fiscalDocumentEventKafkaTemplate")
    public KafkaTemplate<FiscalDocumentEventKey, FiscalDocumentEventValue>
        fiscalDocumentEventKafkaTemplate(KafkaProperties properties) {
        return new KafkaTemplate<>(
            new DefaultKafkaProducerFactory<>(properties.buildProducerProperties(null))
        );
    }

    /**
     * Create template for fiscal document events.
     *
     * @param properties Kafka properties
     * @return Kafka template
     */
    @Profile("local")
    @Bean("fiscalDocumentEventKafkaTemplate")
    public KafkaTemplate<FiscalDocumentEventKey, FiscalDocumentEventValue>
        fiscalDocumentEventLocalKafkaTemplate(KafkaProperties properties) {
        Map<String, Object> configProps = new HashMap<>();
        // Kafka broker settings
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "http://127.0.0.1:9092");
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer.class);

        // Schema Registry settings
        configProps.put("schema.registry.url", "http://localhost:8081/");
        configProps.put("auto.register.schemas", "true");
        configProps.put("use.latest.version", "true");

        return new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(configProps));
    }
}
