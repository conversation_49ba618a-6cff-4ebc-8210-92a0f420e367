package com.cloudbeds.fiscaldocument.services.data;

import com.cloudbeds.fiscaldocument.models.InvoiceTemplateKeys;
import com.cloudbeds.organization.v1.Property;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PropertyProfileDataExtractor {
    /**
     * Add template keys from property profile.
     *
     * @param templateKeys templateKeys
     * @param property property
     */
    public void addTemplateKeysFromPropertyProfile(
        InvoiceTemplateKeys templateKeys,
        Property property
    ) {
        templateKeys.setHotelName(property.getPropertyProfile().getHotelName());
        templateKeys.setHotelAddress(property.getPropertyProfile().getHotelAddress().getAddress1());
        templateKeys.setHotelCity(property.getPropertyProfile().getHotelAddress().getCity());
        templateKeys.setHotelState(property.getPropertyProfile().getHotelAddress().getState());
        templateKeys.setHotelZip(property.getPropertyProfile().getHotelAddress().getZip());
        templateKeys.setHotelPhone(property.getPropertyProfile().getHotelPhone());
        templateKeys.setFax(property.getPropertyProfile().getHotelFax());
        templateKeys.setDefaultCurrency(property.getCurrency());
        templateKeys.setHotelEmail(property.getPropertyProfile().getHotelAddress().getEmail());
        var locale = Locale.of(property.getLang(), property.getPropertyProfile().getHotelAddress().getCountryCode());
        templateKeys.setHotelCountry(locale.getDisplayCountry());
    }
}
