package com.cloudbeds.fiscaldocument.services.data;

import com.cloudbeds.fiscaldocument.entity.DocumentConfig;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.models.InvoiceTemplateKeys;
import com.cloudbeds.fiscaldocument.support.localization.LocalizationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ConfigDataExtractor {

    /**
     * Add template keys from config.
     *
     * @param templateKeys templateKeys
     * @param documentConfig documentConfig
     * @param lang lang
     */
    public void addTemplateKeysFromConfig(
        InvoiceTemplateKeys templateKeys,
        DocumentConfig documentConfig,
        String lang
    ) {
        templateKeys.setInvoiceImg(documentConfig.getImgUrl());
        var title = documentConfig.getTitleForLang(lang);
        if (title != null) {
            templateKeys.setInvoiceTitle(title);
        } else {
            if (documentConfig.getDocumentKind().equals(DocumentKind.INVOICE)) {
                templateKeys.setInvoiceTitle(LocalizationUtil.getTranslation("RES_invoice", lang));
            } else {
                templateKeys.setInvoiceTitle(LocalizationUtil.getTranslation("reservation/creditNote", lang));
            }
        }
        templateKeys.setTaxId1(documentConfig.getTaxId1());
        templateKeys.setTaxId2(documentConfig.getTaxId2());

        templateKeys.setCustomText(documentConfig.getCustomTextForLang(lang));
        templateKeys.setHasRoomNumber(documentConfig.isIncludeRoomNumber());
    }
}
