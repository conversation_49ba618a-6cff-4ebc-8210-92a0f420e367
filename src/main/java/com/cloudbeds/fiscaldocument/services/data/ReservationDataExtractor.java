package com.cloudbeds.fiscaldocument.services.data;

import com.cloudbeds.booking.v1.Booking;
import com.cloudbeds.booking.v1.BookingGuest;
import com.cloudbeds.booking.v1.BookingWithRooms;
import com.cloudbeds.fiscaldocument.entity.DocumentConfig;
import com.cloudbeds.fiscaldocument.models.InvoiceTemplateKeys;
import com.cloudbeds.fiscaldocument.support.localization.LocalizationUtil;
import com.cloudbeds.organization.v1.Property;
import java.time.Duration;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import static com.cloudbeds.fiscaldocument.utils.DateUtils.formatProtobufDate;
import static com.cloudbeds.fiscaldocument.utils.DateUtils.formatProtobufTimestamp;
import static com.cloudbeds.fiscaldocument.utils.DateUtils.toLocalDate;

@Component
@RequiredArgsConstructor
public class ReservationDataExtractor {
    /**
     * Add template keys from reservation.
     *
     * @param templateKeys templateKeys
     * @param config config
     * @param dateFormatter dateFormatter
     */
    public void addTemplateKeysFromReservation(
        Property property,
        InvoiceTemplateKeys templateKeys,
        DocumentConfig config,
        BookingWithRooms bookingWithRoom,
        DateTimeFormatter dateFormatter
    ) {
        var mainGuest = bookingWithRoom.getGuestsList().stream()
            .filter(BookingGuest::getIsMainGuest)
            .findFirst()
            .orElseThrow();
        Booking booking = bookingWithRoom.getBooking();
        templateKeys.setReservationFirstName(mainGuest.getFirstName());
        templateKeys.setReservationLastName(mainGuest.getLastName());
        templateKeys.setReservationNumber(booking.getIdentifier());
        templateKeys.setReservationDate(
            formatProtobufTimestamp(booking.getCreatedAt(), dateFormatter, ZoneId.of(property.getTimezone())));
        templateKeys.setCheckIn(formatProtobufDate(booking.getCheckinDate(), dateFormatter));
        templateKeys.setCheckOut(formatProtobufDate(booking.getCheckoutDate(), dateFormatter));
        templateKeys.setNights(
            String.valueOf(
                Duration.between(
                    toLocalDate(booking.getCheckinDate()).atStartOfDay(),
                    toLocalDate(booking.getCheckoutDate()).atStartOfDay()).toDays())
        );
        templateKeys.setZip(mainGuest.getZip());
        templateKeys.setState(mainGuest.getState());
        templateKeys.setCompanyName(mainGuest.getCompanyName());
        templateKeys.setAddress(mainGuest.getAddress1());
        templateKeys.setAddress2(mainGuest.getAddress2());
        templateKeys.setCountryName(mainGuest.getCountry());
        templateKeys.setPdfEmail(mainGuest.getEmail());
        templateKeys.setWorkPhone(mainGuest.getPhone());
        templateKeys.setCellPhone(mainGuest.getCellPhone());
        templateKeys.setPhone(mainGuest.getCellPhone());

        String reservationGuestTaxId = mainGuest.getGuestTaxIdNumber();
        String reservationCompanyTaxId = mainGuest.getCompanyTaxIdNumber();
        String reservationCompanyName = mainGuest.getCompanyName();
        String reservationName = booking.getCustomerName();

        var lang = templateKeys.getLang();
        if (config.isUseDocumentNumber() && StringUtils.hasText(mainGuest.getDocumentNumber())) {
            templateKeys.setOccupantTaxIdNumber(mainGuest.getDocumentNumber());
            templateKeys.setOccupantTaxIdNumberLabel(
                LocalizationUtil.getTranslation("ResCreate_document_number", lang)
            );
        } else if (StringUtils.hasText(reservationCompanyTaxId)) {
            templateKeys.setOccupantTaxIdNumber(reservationCompanyTaxId);
            templateKeys.setOccupantTaxIdNumberLabel(
                LocalizationUtil.getTranslation("invoice_pdf_company_tax_id", lang)
            );
        } else if (StringUtils.hasText(reservationGuestTaxId)) {
            templateKeys.setOccupantTaxIdNumber(reservationGuestTaxId);
            templateKeys.setOccupantTaxIdNumberLabel(
                LocalizationUtil.getTranslation("invoice_pdf_guest_tax_id", lang)
            );
        }

        if (StringUtils.hasText(reservationCompanyName)) {
            templateKeys.setOccupantNameLabel(LocalizationUtil.getTranslation("invoice_pdf_company_name", lang));
            templateKeys.setOccupantName(reservationCompanyName);
        } else if (StringUtils.hasText(reservationName)) {
            templateKeys.setOccupantNameLabel(LocalizationUtil.getTranslation("invoice_pdf_guest_name", lang));
            templateKeys.setOccupantName(reservationName);
        }
    }
}
