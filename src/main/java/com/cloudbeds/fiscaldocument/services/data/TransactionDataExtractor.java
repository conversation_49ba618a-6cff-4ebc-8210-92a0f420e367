package com.cloudbeds.fiscaldocument.services.data;

import com.cloudbeds.accounting.v1.InternalCodeGroup;
import com.cloudbeds.accounting.v1.Source;
import com.cloudbeds.booking.v1.BookingRoom;
import com.cloudbeds.booking.v1.BookingWithRooms;
import com.cloudbeds.currency.v1.PropertyActiveCurrencyRate;
import com.cloudbeds.currency.v1.PropertyCurrency;
import com.cloudbeds.fiscaldocument.grpc.mfd.GroupProfileServiceClient;
import com.cloudbeds.fiscaldocument.grpc.mfd.PropertyCurrencyServiceClient;
import com.cloudbeds.fiscaldocument.models.ForeignCurrency;
import com.cloudbeds.fiscaldocument.models.InvoiceTemplateKeys;
import com.cloudbeds.fiscaldocument.models.TaxFeeDetails;
import com.cloudbeds.fiscaldocument.models.TransactionDto;
import com.cloudbeds.fiscaldocument.models.TransactionModel;
import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import com.cloudbeds.fiscaldocument.utils.MoneyUtils.CurrencyFormat;
import com.cloudbeds.group.v1.GroupProfile;
import com.cloudbeds.organization.v1.Property;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import static com.cloudbeds.fiscaldocument.utils.MoneyUtils.formatAmount;

@Slf4j
@RequiredArgsConstructor
@Component
public class TransactionDataExtractor {
    private final PropertyCurrencyServiceClient propertyCurrencyServiceClient;
    private final GroupProfileServiceClient groupProfileServiceClient;

    /**
     * Add template keys from transactions.
     *
     * @param templateKeys             templateKeys
     * @param transactions             transactions
     * @param routedTransactions       routedTransactions
     * @param property                 property
     * @param showDetailedTaxFee       showDetailedTaxFee
     * @param includeRoomNumber        includeRoomNumber
     * @param isCompact                isCompact
     * @param bookingMap               bookingMap
     * @param roomsMap                 roomsMap
     * @param dateFormatter            dateFormatter
     */
    public void addTemplateKeysFromTransactions(
        InvoiceTemplateKeys templateKeys,
        List<TransactionDto> transactions,
        List<TransactionDto> routedTransactions,
        Property property,
        boolean showDetailedTaxFee,
        boolean includeRoomNumber,
        boolean isCompact,
        HashMap<Long, BookingWithRooms> bookingMap,
        Map<Long, BookingRoom> roomsMap,
        DateTimeFormatter dateFormatter
    ) {
        var routedTransctionsMap = routedTransactions.stream()
            .collect(Collectors.toMap(TransactionDto::getId, Function.identity()));
        var transactionTotal = BigDecimal.ZERO;
        var paidValue = BigDecimal.ZERO;
        var taxes = BigDecimal.ZERO;
        var fees = BigDecimal.ZERO;
        var specificTaxesMap = new HashMap<String, BigDecimal>();
        var specificFeesMap = new HashMap<String, BigDecimal>();
        var transactionModels = new ArrayList<TransactionModel>();

        var groupIds = transactions.stream()
            .filter(tx -> tx.getSource().equals(Source.SOURCE_GROUP_PROFILE))
            .map(TransactionDto::getSourceId)
            .collect(Collectors.toSet());

        var groupProfilesMap =
            groupProfileServiceClient.listGroups(property.getId(), groupIds).stream()
                .collect(Collectors.toMap(GroupProfile::getId, Function.identity()));

        var propertyCurrencyFormat = getPropertyCurrencyFormat(property);

        for (var transaction : transactions) {
            if (!transaction.getInternalCode().getGroup().equals(InternalCodeGroup.INTERNAL_CODE_GROUP_PAYMENT)) {
                if (!transaction.getInternalCode().getGroup().equals(InternalCodeGroup.INTERNAL_CODE_GROUP_FEE)
                    && !transaction.getInternalCode().getGroup().equals(InternalCodeGroup.INTERNAL_CODE_GROUP_TAX)) {
                    transactionTotal = transactionTotal.add(transaction.getAmount());
                }
            } else {
                paidValue = paidValue.add(transaction.getAmount());
            }

            if (transaction.getInternalCode().getGroup().equals(InternalCodeGroup.INTERNAL_CODE_GROUP_TAX)) {
                taxes = taxes.add(transaction.getAmount());
                if (showDetailedTaxFee) {
                    specificTaxesMap.putIfAbsent(transaction.getDescription(), BigDecimal.ZERO);
                    specificTaxesMap.put(
                        transaction.getDescription(),
                        specificTaxesMap.get(transaction.getDescription()).add(transaction.getAmount())
                    );
                }
            }

            if (transaction.getInternalCode().getGroup().equals(InternalCodeGroup.INTERNAL_CODE_GROUP_FEE)) {
                fees = fees.add(transaction.getAmount());
                if (showDetailedTaxFee) {
                    specificFeesMap.putIfAbsent(transaction.getDescription(), BigDecimal.ZERO);
                    specificFeesMap.put(
                        transaction.getDescription(),
                        specificFeesMap.get(transaction.getDescription())
                            .add(transaction.getAmount())
                    );
                }
            }
            String roomName = "";
            if (roomsMap.containsKey(transaction.getSubSourceId())) {
                var room = roomsMap.get(transaction.getSubSourceId());
                roomName = room.getRoomTypeName();
            }

            String guestName = "";
            String sourceName = "";
            if (transaction.hasRoutedFrom() && transaction.getSource().equals(Source.SOURCE_GROUP_PROFILE)) {
                var relatedReservation =
                    bookingMap.get(routedTransctionsMap.get(transaction.getRoutedFrom()).getSourceId()).getBooking();
                guestName = relatedReservation.getCustomerName();
                sourceName = relatedReservation.getIdentifier();
            } else if (transaction.getSource().equals(Source.SOURCE_RESERVATION)) {
                var relatedReservation = bookingMap.get(transaction.getSourceId()).getBooking();
                guestName = relatedReservation.getCustomerName();
                sourceName = relatedReservation.getIdentifier();
            } else if (transaction.getSource().equals(Source.SOURCE_GROUP_PROFILE)) {
                var groupProfile = groupProfilesMap.get(transaction.getSourceId());
                guestName = groupProfile.getName();
                sourceName = groupProfile.getCode();
            }

            if (!isCompact) {
                transactionModels.add(getTransactionModel(
                    transaction,
                    includeRoomNumber,
                    guestName,
                    roomName,
                    sourceName,
                    dateFormatter,
                    propertyCurrencyFormat
                ));
            }
        }

        if (isCompact) {
            transactionModels.addAll(
                getCompactTransactions(
                    transactions,
                    routedTransctionsMap,
                    groupProfilesMap,
                    bookingMap,
                    includeRoomNumber,
                    roomsMap,
                    dateFormatter,
                    propertyCurrencyFormat
                )
            );
        }

        templateKeys.setTransactions(transactionModels);
        var propertyCurrencyCode = property.getCurrency();
        var specificTaxes = new ArrayList<TaxFeeDetails>();
        var specificFees = new ArrayList<TaxFeeDetails>();

        specificTaxesMap.forEach((key, value) -> specificTaxes.add(
            new TaxFeeDetails(
                key,
                formatAmount(value, propertyCurrencyCode, propertyCurrencyFormat)
            )
        ));
        specificFeesMap.forEach((key, value) -> specificFees.add(
            new TaxFeeDetails(
                key,
                formatAmount(value, propertyCurrencyCode, propertyCurrencyFormat)
            )
        ));

        var total = transactionTotal.add(taxes).add(fees);
        var balanceDue = total.subtract(paidValue);
        templateKeys.setSpecificFees(specificFees);
        templateKeys.setSpecificTaxes(specificTaxes);
        templateKeys.setTransactionsTotalCredit(
            formatAmount(total, propertyCurrencyCode, propertyCurrencyFormat)
        );
        templateKeys.setTransactionsTotalDebit(
            formatAmount(paidValue, propertyCurrencyCode, propertyCurrencyFormat)
        );
        templateKeys.setTransactionTotal(
            formatAmount(transactionTotal, propertyCurrencyCode, propertyCurrencyFormat)
        );
        templateKeys.setBalanceDue(
            formatAmount(balanceDue, propertyCurrencyCode, propertyCurrencyFormat)
        );
        templateKeys.setResCreateTaxes(
            formatAmount(taxes, propertyCurrencyCode, propertyCurrencyFormat)
        );
        templateKeys.setResCreateFees(
            formatAmount(fees, propertyCurrencyCode, propertyCurrencyFormat)
        );
        templateKeys.setGrandTotal(
            formatAmount(total, propertyCurrencyCode, propertyCurrencyFormat)
        );
        templateKeys.setTransactionsTotalAmount(
            formatAmount(balanceDue, propertyCurrencyCode, propertyCurrencyFormat)
        );
        //TODO: Calculate when we have enough information
        templateKeys.setTransactionsTotalVat(
            formatAmount(BigDecimal.ZERO, propertyCurrencyCode, propertyCurrencyFormat)
        );


        var response = propertyCurrencyServiceClient.listPropertyCurrencies(property.getId());
        var foreignCurrencies = response.stream()
            .filter(PropertyCurrency::getIsActive)
            .filter(PropertyCurrency::getDisplayInvoiceRate)
            .map(propertyCurrency -> {
                var foreignCurrency = new ForeignCurrency();
                foreignCurrency.setForeignCurrencyCode(propertyCurrency.getCurrencyCode());
                return foreignCurrency;
            })
            .toList();

        if (!foreignCurrencies.isEmpty()) {
            var currencyRates =
                propertyCurrencyServiceClient.listPropertyActiveCurrencyRatesByPropertyId(property.getId());

            var propertyCurrenciesMap = response.stream()
                .collect(Collectors.toMap(PropertyCurrency::getCurrencyCode, Function.identity()));

            var currencyRatesMap = currencyRates.stream()
                .collect(Collectors.toMap(PropertyActiveCurrencyRate::getCurrencyCode, Function.identity()));

            foreignCurrencies.forEach(foreignCurrency -> {
                var currencyRate = currencyRatesMap.get(foreignCurrency.getForeignCurrencyCode());
                if (currencyRate == null) {
                    return;
                }

                foreignCurrency.setForeignCurrencyRate(
                    formatAmount(
                        BigDecimal.valueOf(currencyRate.getRate()),
                        propertyCurrencyCode,
                        propertyCurrencyFormat
                    ));

                var foreignCurrencySettings = propertyCurrenciesMap.get(foreignCurrency.getForeignCurrencyCode());
                var rate = BigDecimal.valueOf(currencyRate.getRate());
                var ratePercent = rate.compareTo(BigDecimal.ZERO) > 0
                    ? BigDecimal.ONE.setScale(9, RoundingMode.HALF_UP).divide(rate, RoundingMode.HALF_UP)
                    : BigDecimal.ZERO;

                if (foreignCurrencySettings.getDisplayInvoiceTotal()) {
                    foreignCurrency.setForeignCurrencyInvoiceTotal(
                        formatAmount(
                            balanceDue.multiply(ratePercent).setScale(2, RoundingMode.HALF_UP),
                            foreignCurrencySettings.getCurrencyCode(),
                            propertyCurrencyFormat
                        )
                    );
                }

                if (foreignCurrencySettings.getDisplayInvoiceRate()) {
                    foreignCurrency.setForeignBalanceDueRate(
                        "1 = " + formatAmount(ratePercent, propertyCurrencyCode, propertyCurrencyFormat));
                }
            });
            templateKeys.setForeignCurrencies(foreignCurrencies);
        }
    }

    private CurrencyFormat getPropertyCurrencyFormat(Property property) {
        try {
            var propertyCurrencyFormat = propertyCurrencyServiceClient.getPropertyCurrencyFormat(property.getId());
            if (propertyCurrencyFormat != null) {
                if (StringUtils.isNotBlank(propertyCurrencyFormat.getDecimalSeparator())
                    && StringUtils.isNotBlank(propertyCurrencyFormat.getThousandSeparator())
                ) {
                    var decimalSeparator = propertyCurrencyFormat.getDecimalSeparator().charAt(0);
                    var thousandsSeparator = propertyCurrencyFormat.getThousandSeparator().charAt(0);

                    return new CurrencyFormat(decimalSeparator, thousandsSeparator);
                }
            }
        } catch (Exception e) {
            log.warn("Could not get property currency format for property {}", property.getId(), e);
        }

        return null;
    }



    private List<TransactionModel> getCompactTransactions(
        List<TransactionDto> transactions,
        Map<Long, TransactionDto> routedTransctionsMap,
        Map<Long, GroupProfile> groupProfilesMap,
        HashMap<Long, BookingWithRooms> bookingMap,
        boolean includeRoomNumber,
        Map<Long, BookingRoom> roomsMap,
        DateTimeFormatter dateFormatter,
        CurrencyFormat propertyCurrencyFormat
    ) {
        var voidedRoots = transactions.stream()
            .filter(transaction -> transaction.getInternalCode().getCode().endsWith("V"))
            .map(TransactionDto::getRootId)
            .collect(Collectors.toSet());

        var groupedTransactionsNonPayment = transactions.stream()
            .filter(transaction -> !voidedRoots.contains(transaction.getRootId()))
            .filter(transaction ->
                !transaction.getInternalCode().getGroup().equals(InternalCodeGroup.INTERNAL_CODE_GROUP_PAYMENT))
            .collect(
                Collectors.groupingBy(
                    tx -> getSourceString(tx, routedTransctionsMap) + " - " + tx.getDescription()
                ));

        var result = new ArrayList<>(transactions.stream()
            .filter(transaction -> !voidedRoots.contains(transaction.getRootId()))
            .filter(transaction ->
                transaction.getInternalCode().getGroup().equals(InternalCodeGroup.INTERNAL_CODE_GROUP_PAYMENT))
            .map(transaction -> {
                var roomName = "";
                if (roomsMap.containsKey(transaction.getSubSourceId())) {
                    var room = roomsMap.get(transaction.getSubSourceId());
                    roomName = room.getRoomTypeName();
                }

                var guestNameAndIdentifier = getGuestNameAndIdentifier(
                    transaction,
                    routedTransctionsMap,
                    bookingMap,
                    groupProfilesMap
                );

                String guestName = guestNameAndIdentifier.getLeft();
                String identifier = guestNameAndIdentifier.getRight();
                return getTransactionModel(
                    transaction,
                    includeRoomNumber,
                    guestName,
                    roomName,
                    identifier,
                    dateFormatter,
                    propertyCurrencyFormat
                );
            }).toList());

        groupedTransactionsNonPayment.forEach((key, transactionList) -> {
            TransactionDto firstTransaction = transactionList.getFirst();

            var minDate = transactionList.stream()
                .map(TransactionDto::getTransactionDatetimePropertyTime)
                .min(Comparator.comparingLong(Instant::getEpochSecond))
                .orElse(firstTransaction.getTransactionDatetimePropertyTime());

            var maxDate = transactionList.stream()
                .map(TransactionDto::getTransactionDatetimePropertyTime)
                .max(Comparator.comparingLong(Instant::getEpochSecond))
                .orElse(firstTransaction.getTransactionDatetimePropertyTime());

            var transactionModel = new TransactionModel();
            if (minDate != maxDate) {
                transactionModel.setTransactionDateTime(
                    LocalDateTime.ofInstant(minDate, ZoneId.systemDefault()).format(dateFormatter)
                        + " - "
                        + LocalDateTime.ofInstant(maxDate, ZoneId.systemDefault()).format(dateFormatter)
                );
            } else {
                transactionModel.setTransactionDateTime(
                    LocalDateTime.ofInstant(minDate, ZoneId.systemDefault()).format(dateFormatter)
                );
            }

            var guestNameAndIdentifier = getGuestNameAndIdentifier(
                firstTransaction,
                routedTransctionsMap,
                bookingMap,
                groupProfilesMap
            );

            transactionModel.setTransactionName(guestNameAndIdentifier.getLeft());
            if (includeRoomNumber) {
                var roomName = "";
                if (roomsMap.containsKey(firstTransaction.getSubSourceId())) {
                    var room = roomsMap.get(firstTransaction.getSubSourceId());
                    roomName = room.getRoomTypeName();
                }
                transactionModel.setTransactionRoomName(roomName);
            }
            transactionModel.setTransactionReservationIdentifier(guestNameAndIdentifier.getRight());

            var sum = transactionList.stream()
                .map(TransactionDto::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            transactionModel.setTransactionQty(
                String.valueOf(
                    transactionList.stream()
                        .map(TransactionDto::getQuantity)
                        .reduce(0, Integer::sum)
                )
            );

            transactionModel.setTransactionCredit(
                formatAmount(sum, firstTransaction.getCurrency(), propertyCurrencyFormat),
                sum.compareTo(BigDecimal.ZERO) < 0
            );

            transactionModel.setTransactionAmount(
                formatAmount(sum, firstTransaction.getCurrency(), propertyCurrencyFormat)
            );

            var isRateTransactionGroup = firstTransaction.getInternalCode().getGroup()
                .equals(InternalCodeGroup.INTERNAL_CODE_GROUP_ROOM_REVENUE_RATE);

            if (isRateTransactionGroup) {
                transactionModel.setTransactionNights(String.valueOf(transactionList.size()));

                transactionModel.setTransactionDescription(firstTransaction.getDescription());
            } else {
                var quantity = transactionList.stream()
                    .map(TransactionDto::getQuantity)
                    .reduce(0, Integer::sum);
                if (quantity > 1) {
                    transactionModel.setTransactionDescription(quantity + " x " + firstTransaction.getDescription());
                } else {
                    transactionModel.setTransactionDescription(firstTransaction.getDescription());
                }
            }
            result.add(transactionModel);
        });

        return result;
    }

    private Pair<String, String> getGuestNameAndIdentifier(
        TransactionDto transaction,
        Map<Long, TransactionDto> routedTransctionsMap,
        HashMap<Long, BookingWithRooms> bookingMap,
        Map<Long, GroupProfile> groupProfilesMap
    ) {
        if (transaction.hasRoutedFrom()) {
            var originalTransaction = routedTransctionsMap.get(transaction.getRoutedFrom());
            if (transaction.getSource().equals(Source.SOURCE_GROUP_PROFILE)) {
                return getGuestNameAndIdentifierFromGroupProfile(groupProfilesMap, originalTransaction);
            } else if (transaction.getSource().equals(Source.SOURCE_RESERVATION)) {
                return getGuestNameAndIdentifierFromBooking(bookingMap, originalTransaction);
            }
        }

        if (transaction.getSource().equals(Source.SOURCE_GROUP_PROFILE)) {
            return getGuestNameAndIdentifierFromGroupProfile(groupProfilesMap, transaction);
        } else if (transaction.getSource().equals(Source.SOURCE_RESERVATION)) {
            return getGuestNameAndIdentifierFromBooking(bookingMap, transaction);
        }

        return Pair.of("", "");
    }

    private static Pair<String, String> getGuestNameAndIdentifierFromGroupProfile(
        Map<Long, GroupProfile> groupProfilesMap,
        TransactionDto transaction
    ) {
        if (!groupProfilesMap.containsKey(transaction.getSourceId())) {
            throw new FiscalDocumentException(
                ErrorCode.UNEXPECTED_ERROR,
                "Related group profile not found for transaction " + transaction.getId()
            );
        }

        return Pair.of(
            groupProfilesMap.get(transaction.getSourceId()).getName(),
            groupProfilesMap.get(transaction.getSourceId()).getCode()
        );
    }

    private static Pair<String, String> getGuestNameAndIdentifierFromBooking(
        HashMap<Long, BookingWithRooms> bookingMap,
        TransactionDto originalTransaction
    ) {
        if (!bookingMap.containsKey(originalTransaction.getSourceId())) {
            throw new FiscalDocumentException(
                ErrorCode.UNEXPECTED_ERROR,
                "Related reservation not found for transaction " + originalTransaction.getId()
            );
        }

        var relatedReservation = bookingMap.get(originalTransaction.getSourceId()).getBooking();
        var guestName = relatedReservation.getCustomerName();
        var identifier = relatedReservation.getIdentifier();
        return Pair.of(guestName, identifier);
    }

    private static String getSourceString(TransactionDto tx, Map<Long, TransactionDto> routedTransctionsMap) {
        if (tx.hasRoutedFrom()) {
            var originalTransaction = routedTransctionsMap.get(tx.getRoutedFrom());
            return originalTransaction.getSource().name() + "_" + originalTransaction.getSourceId();
        }

        return tx.getSource().name() + "_" + tx.getSourceId();
    }

    private TransactionModel getTransactionModel(
        TransactionDto transaction,
        boolean includeRoomNumber,
        String reservationName,
        String reservationRoomName,
        String reservationIdentifier,
        DateTimeFormatter dateFormatter,
        CurrencyFormat propetyCurrencyFormat
    ) {
        var transactionModel = new TransactionModel();
        transactionModel.setTransactionDateTime(
            LocalDateTime.ofInstant(
                transaction.getTransactionDatetimePropertyTime(),
                ZoneId.systemDefault()
            ).format(dateFormatter)
        );
        transactionModel.setTransactionName(transaction.getDescription());
        if (!transaction.getInternalCode().getGroup().equals(InternalCodeGroup.INTERNAL_CODE_GROUP_PAYMENT)) {
            var amount = transaction.getAmount();
            transactionModel.setTransactionCredit(
                formatAmount(amount, transaction.getCurrency(), propetyCurrencyFormat),
                amount.compareTo(BigDecimal.ZERO) < 0
            );
        } else {
            var amount = transaction.getAmount().negate();
            transactionModel.setTransactionDebit(
                formatAmount(amount, transaction.getCurrency(), propetyCurrencyFormat),
                amount.compareTo(BigDecimal.ZERO) < 0
            );
        }

        transactionModel.setTransactionAmount(
            formatAmount(transaction.getAmount(), transaction.getCurrency(), propetyCurrencyFormat)
        );

        if (transaction.getQuantity() > 0) {
            transactionModel.setTransactionQty(String.valueOf(transaction.getQuantity()));
        }
        transactionModel.setTransactionDescription(transaction.getDescription());
        transactionModel.setTransactionName(reservationName);
        if (includeRoomNumber) {
            transactionModel.setTransactionRoomName(reservationRoomName);
        }
        transactionModel.setTransactionReservationIdentifier(reservationIdentifier);
        return transactionModel;
    }
}
