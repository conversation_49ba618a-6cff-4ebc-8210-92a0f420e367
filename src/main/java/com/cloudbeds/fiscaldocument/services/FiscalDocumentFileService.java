package com.cloudbeds.fiscaldocument.services;

import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.enums.Origin;
import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import java.io.File;
import java.time.Duration;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.CaseUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class FiscalDocumentFileService {
    private final S3Service s3Service;

    @Value("${application.aws.s3.bucket-name}")
    private String bucketName;

    @Value("${application.aws.s3.mfd-bucket-name}")
    private String mfdBucketName;

    private static final String MFD_FOLDER = "01tt";
    private final Duration defaultDuration = Duration.ofMinutes(60);

    /**
     * Retrieves the content of a FiscalDocument from an S3 bucket.
     *
     * @param fiscalDocument the FiscalDocument to retrieve content for
     * @return the content of the FiscalDocument as a byte array
     */
    public byte[] getContent(FiscalDocument fiscalDocument) {
        String targetBucket = getBucket(fiscalDocument);
        String filePath = getFilePath(fiscalDocument);

        return s3Service.getContent(targetBucket, filePath);
    }

    /**
     * Uploads a file to S3 bucket with specified file path based on the provided FiscalDocument.
     *
     * @param generatedFile the file to upload
     * @param fiscalDocument the FiscalDocument object associated with the file
     * @return the file path where the file was uploaded
     */
    public String uploadFile(File generatedFile, FiscalDocument fiscalDocument, String fileExtension) {
        var filePath = buildFilePath(fiscalDocument, fileExtension);
        s3Service.uploadContent(generatedFile, bucketName, filePath);
        return filePath;
    }

    /**
     * Function to validate if document can be downloaded.
     *
     * @param document the fiscal document
     * @return true if document can be downloaded
     */
    public boolean isDownloadable(FiscalDocument document) {
        if (document.getOrigin() == Origin.MFD) {
            return StringUtils.hasText(document.getUrl());
        } else {
            return StringUtils.hasText(document.getFilePath());
        }
    }

    /**
     * Starts the download of a Fiscal Document.
     *
     * @param document the fiscal document
     * @return the file
     */
    public ResponseEntity<Resource> downloadFile(FiscalDocument document) {
        byte[] fileContent = getContent(document);
        String extension = getExtensionFromPath(document);
        String filename = "fiscal_document_" + document.getId() + "." + extension;

        return ResponseEntity.ok()
            .contentType(getMediaTypeFromExtension(extension))
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
            .header(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "Content-Disposition")
            .body(new ByteArrayResource(fileContent));
    }

    /**
     * Get the extension from the fiscal document.
     *
     * @param document the fiscal document
     * @return extension of the file
     */
    public String getExtensionFromPath(FiscalDocument document) {
        var path = getResolvedPath(document);
        int dotIndex = path.lastIndexOf('.');
        return (dotIndex != -1) ? path.substring(dotIndex + 1) : "";
    }

    private String getResolvedPath(FiscalDocument document) {
        if (document.getOrigin() == Origin.MFD) {
            return document.getUrl();
        } else {
            return document.getFilePath();
        }
    }

    private MediaType getMediaTypeFromExtension(String extension) {
        return switch (extension.toLowerCase()) {
            case "pdf" -> MediaType.APPLICATION_PDF;
            case "json" -> MediaType.APPLICATION_JSON;
            case "csv" -> MediaType.valueOf("text/csv");
            case "xml" -> MediaType.APPLICATION_XML;
            default -> MediaType.APPLICATION_OCTET_STREAM;
        };
    }

    private String buildFilePath(FiscalDocument fiscalDocument, String extension) {
        return fiscalDocument.getPropertyId()
            + "/" + fiscalDocument.getInvoiceDate().getYear()
            + "/" + String.format("%02d", fiscalDocument.getInvoiceDate().getMonthValue())
            + "/" + String.format("%02d", fiscalDocument.getInvoiceDate().getDayOfMonth())
            + "/" + CaseUtils.toCamelCase(fiscalDocument.getKind().name().toLowerCase(), true, '_')
            + "_" + fiscalDocument.getId()
            + "_" + fiscalDocument.getNumber()
            + "." + extension;
    }

    /**
     * Resolves the appropriate S3 bucket for a given fiscal document.
     */
    private String getBucket(FiscalDocument doc) {
        if (doc.getOrigin() == Origin.MFD) {
            return Optional.ofNullable(mfdBucketName)
                .orElseThrow(() -> new FiscalDocumentException(
                    ErrorCode.INVALID_REQUEST,
                    "Please refer to Cloudbeds Invoice API to download this file"
                ));
        }
        return bucketName;
    }

    /**
     * Resolves the S3 file path for a given fiscal document.
     */
    private String getFilePath(FiscalDocument document) {
        String filePath = document.getFilePath();

        if (document.getOrigin() == Origin.MFD) {
            filePath = extractPathFromMfdUrl(document.getUrl());
        }

        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }

        return filePath;
    }

    /**
     * Extracts the path within the MFD folder from a full S3 URL.
     *
     * @param url full S3 URL
     * @return trimmed file path starting from MFD_FOLDER
     */
    private String extractPathFromMfdUrl(String url) {
        int index = url.indexOf(MFD_FOLDER);
        if (index >= 0) {
            return url.substring(index);
        }

        throw new FiscalDocumentException(
            ErrorCode.INVALID_REQUEST,
            "Invalid file to download"
        );
    }
}
