package com.cloudbeds.fiscaldocument.services;

import jakarta.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;

@Slf4j
@Service
@RequiredArgsConstructor
public class S3Service {

    @Value("${application.aws.region}")
    private String region;

    @Value("${application.aws.endpoint}")
    private String endpoint;

    private S3Client s3Client;
    private S3Presigner s3Presigner;

    @SuppressWarnings("checkstyle:MissingJavadocMethod")
    @PostConstruct
    public void init() {
        S3ClientBuilder s3ClientBuilder = S3Client.builder()
            .region(Region.of(region))
            .serviceConfiguration(S3Configuration.builder()
                .pathStyleAccessEnabled(true)
                .build()
            );

        S3Presigner.Builder presignerBuilder = S3Presigner.builder()
            .region(Region.of(region))
            .serviceConfiguration(S3Configuration.builder()
                .pathStyleAccessEnabled(true)
                .build()
            );

        if (endpoint != null && !endpoint.isEmpty()) {
            try {
                URI endpointUri = new URI(endpoint);
                s3ClientBuilder.endpointOverride(endpointUri);
                presignerBuilder.endpointOverride(endpointUri);
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid AWS_ENDPOINT URI", e);
            }
        }

        this.s3Client = s3ClientBuilder.build();
        this.s3Presigner = presignerBuilder.build();
    }

    /**
     * Generates a pre-signed URL for downloading an object from S3.
     *
     * @param bucket the name of the S3 bucket
     * @param key the object key within the bucket
     * @param duration the time duration for which the URL should remain valid
     * @return a {@link URL} that can be used to download the object directly from S3
     */
    public String generatePresignedUrl(String bucket, String key, Duration duration) {
        if (key.startsWith("/")) {
            key = key.substring(1);
        }

        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
            .bucket(bucket)
            .key(key)
            .build();

        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
            .signatureDuration(duration)
            .getObjectRequest(getObjectRequest)
            .build();

        return s3Presigner.presignGetObject(presignRequest).url().toString();
    }

    /**
     * Retrieves the content of an object from S3 bucket.
     *
     * @param bucket the name of the S3 bucket
     * @param key the object key within the bucket
     * @return the content of the object as a byte array
     */
    public byte[] getContent(String bucket, String key) {
        ResponseInputStream<GetObjectResponse> response = s3Client.getObject(req -> req.bucket(bucket).key(key));

        try {
            return StreamUtils.copyToByteArray(response);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * Uploads the content of a given file to the specified S3 bucket with the provided key.
     *
     * @param file the file to upload
     * @param bucket the name of the destination S3 bucket
     * @param key the key under which the file will be stored in the bucket
     * @return the response of the put operation in S3
     */
    public PutObjectResponse uploadContent(File file, String bucket, String key) {
        return s3Client.putObject(req -> req.bucket(bucket).key(key), RequestBody.fromFile(file));
    }
}
