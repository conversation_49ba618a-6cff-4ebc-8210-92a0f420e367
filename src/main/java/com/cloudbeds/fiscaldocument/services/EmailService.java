package com.cloudbeds.fiscaldocument.services;

import com.sendgrid.Method;
import com.sendgrid.Request;
import com.sendgrid.Response;
import com.sendgrid.SendGrid;
import com.sendgrid.helpers.mail.Mail;
import com.sendgrid.helpers.mail.objects.Attachments;
import com.sendgrid.helpers.mail.objects.Content;
import com.sendgrid.helpers.mail.objects.Email;
import com.sendgrid.helpers.mail.objects.Personalization;
import java.io.IOException;
import java.util.Base64;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmailService {
    @Value("${sendgrid.api_key}")
    private String sendGridApiKey;

    @Value("${sendgrid.sender}")
    private String sender;

    /**
     * Sends an email with the fiscal document attached to a list of recipients.
     *
     * @param recipients List of recipient email addresses
     * @param fileName   Name of the attached file (without extension)
     * @param extension  File extension (e.g., pdf, zip)
     * @param content    Raw file content in bytes
     * @return HTTP status code returned by the SendGrid API
     * @throws IOException if the SendGrid API call fails
     */
    public int send(List<String> recipients, String fileName, String extension, byte[] content) throws IOException {
        Mail mail = buildMail(recipients, fileName, extension, content);
        return sendMail(mail);
    }

    private Mail buildMail(List<String> recipients, String fileName, String extension, byte[] content) {
        Mail mail = new Mail();
        mail.setFrom(new Email(sender));
        mail.setSubject("Here is your Fiscal Document");

        Content body = new Content("text/plain", "Fiscal Document");
        mail.addContent(body);

        recipients.forEach(email -> {
            Personalization personalization = new Personalization();
            personalization.addTo(new Email(email));
            mail.addPersonalization(personalization);
        });

        Attachments attachment = new Attachments();
        attachment.setFilename(fileName + "." + extension);
        attachment.setType("application/" + extension);
        attachment.setDisposition("attachment");
        attachment.setContent(Base64.getEncoder().encodeToString(content));

        mail.addAttachments(attachment);
        return mail;
    }

    private int sendMail(Mail mail) throws IOException {
        SendGrid sg = new SendGrid(sendGridApiKey);
        Request request = new Request();

        request.setMethod(Method.POST);
        request.setEndpoint("mail/send");
        request.setBody(mail.build());

        Response response = sg.api(request);
        return response.getStatusCode();
    }
}