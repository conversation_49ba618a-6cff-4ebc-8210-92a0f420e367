package com.cloudbeds.fiscaldocument.services;

import com.cloudbeds.fiscaldocument.controller.model.ConfigsUpdateRequest;
import com.cloudbeds.fiscaldocument.entity.DocumentConfig;
import com.cloudbeds.fiscaldocument.entity.DocumentContent;
import com.cloudbeds.fiscaldocument.entity.DocumentSequence;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.grpc.organization.PropertyServiceClient;
import com.cloudbeds.fiscaldocument.repositories.DocumentConfigRepository;
import com.cloudbeds.fiscaldocument.utils.constants.CountryCodes;
import com.cloudbeds.fiscaldocument.utils.constants.DocumentTemplateLists;
import com.cloudbeds.organization.v1.Property;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class DocumentConfigService {

    private final DocumentConfigRepository documentConfigRepository;
    private final DocumentSequenceService documentSequenceService;
    private final PropertyServiceClient propertyServiceClient;

    private static final String INVOICE_RESERVATION_TEMPLATE = "templates/reservation_invoice.html";
    private static final String INVOICE_RESERVATION_COMPACT_TEMPLATE = "templates/reservation_invoice_compacted.html";
    private static final String INVOICE_GROUP_TEMPLATE = "templates/group_profile_invoice.html";
    private static final String INVOICE_GROUP_COMPACT_TEMPLATE = "templates/group_profile_invoice_compacted.html";

    private static final String CREDIT_NOTE_RESERVATION_TEMPLATE = "templates/reservation_credit_note.html";
    private static final String CREDIT_NOTE_RESERVATION_COMPACT_TEMPLATE =
        "templates/reservation_credit_note_compacted.html";
    private static final String CREDIT_NOTE_GROUP_TEMPLATE = "templates/group_profile_credit_note.html";
    private static final String CREDIT_NOTE_GROUP_COMPACT_TEMPLATE =
        "templates/group_profile_credit_note_compacted.html";


    /**
     * Retrieves all document configurations for the given property IDs.
     * If some configurations for document kinds are missing, they will be created automatically.
     *
     * @param propertyIds property IDs
     * @return a list of DocumentConfig objects
     */
    @Transactional
    public List<DocumentConfig> getOrCreateAllConfigs(List<Long> propertyIds) {
        var configs = getDocumentConfigsByPropertyIds(propertyIds);
        var configsByProperty = configs.stream()
            .collect(Collectors.groupingBy(
                DocumentConfig::getPropertyId
            ));

        if (propertyIds.isEmpty()) {
            return configs;
        }

        var properties = propertyServiceClient.listProperties(propertyIds);
        properties.forEach(property -> {
            var existingConfigDocumentKinds = configsByProperty.getOrDefault(property.getId(), List.of()).stream()
                .map(DocumentConfig::getDocumentKind)
                .collect(Collectors.toSet());

            var missingDocumentConfigKinds = getSupportedDocumentKinds(property);
            existingConfigDocumentKinds.forEach(missingDocumentConfigKinds::remove);

            if (!missingDocumentConfigKinds.isEmpty()) {
                missingDocumentConfigKinds.forEach((kind, templates) -> {
                    createConfig(property.getId(), kind, true, null);
                });
            }
        });

        return getDocumentConfigsByPropertyIds(propertyIds);
    }

    private Map<DocumentKind, DocumentTemplateLists.DocumentTemplates> getSupportedDocumentKinds(Property property) {
        var countryCode = property.getPropertyProfile().getHotelAddress().getCountryCode();

        if (CountryCodes.SPAIN.getCode().equalsIgnoreCase(countryCode)) {
            return new HashMap<>(DocumentTemplateLists.SPAIN_DOCUMENT_TEMPLATES);
        }

        return new HashMap<>(DocumentTemplateLists.DEFAULT_DOCUMENT_TEMPLATES);
    }

    /**
     * Create a new document configuration for the given property ID and document kind.
     *
     * @param propertyId   property ID
     * @param documentKind document kind
     * @param isCompact    is compact
     * @return DocumentConfig
     */
    @Transactional
    public DocumentConfig createConfig(
        Long propertyId,
        DocumentKind documentKind,
        boolean isCompact,
        DocumentSequence sequence
    ) {
        var newPropertyDocumentConfig = new DocumentConfig();
        newPropertyDocumentConfig.setPropertyId(propertyId);
        newPropertyDocumentConfig.setDocumentKind(documentKind);
        newPropertyDocumentConfig.setCompact(isCompact);

        if (sequence == null) {
            sequence = documentSequenceService.createDefaultSequence(propertyId);
        }
        var contents = newPropertyDocumentConfig.getDocumentContents();
        contents.add(
            createDefaultContent(
                propertyId,
                documentKind,
                null,
                null,
                null,
                sequence,
                SourceKind.RESERVATION
            )
        );

        contents.add(
            createDefaultContent(
                propertyId,
                documentKind,
                null,
                null,
                null,
                sequence,
                SourceKind.GROUP_PROFILE
            )
        );

        return documentConfigRepository.save(newPropertyDocumentConfig);
    }

    @Transactional
    public void saveAll(List<DocumentConfig> configs) {
        documentConfigRepository.saveAll(configs);
    }

    /**
     * Create default content for a property.
     *
     * @param propertyId propertyId
     * @param documentKind documentKind
     * @param prefix prefix
     * @param suffix suffix
     * @param sequence   documentSequence
     * @param sourceKind sourceKind
     *
     * @return DocumentContent
     */
    public DocumentContent createDefaultContent(
        Long propertyId,
        DocumentKind documentKind,
        String prefix,
        String suffix,
        Long externalId, // invoice setup id in MFD
        DocumentSequence sequence,
        SourceKind sourceKind
    ) {

        var content = new DocumentContent();
        content.setPrefix(prefix);
        content.setSuffix(suffix);
        content.setExternalId(externalId);
        content.setPropertyId(propertyId);
        content.setDocumentKind(documentKind);
        content.setSourceKind(sourceKind);
        content.setDocumentSequence(sequence);

        return content;
    }

    /**
     * Create default content for invoice per source.
     *
     * @param propertyId propertyId
     * @param prefix prefix
     * @param suffix suffix
     * @param sequence   documentSequence
     * @param sourceKind sourceKind
     * @return DocumentContent
     */
    public DocumentContent createDefaultContentsInvoice(
        Long propertyId,
        String prefix,
        String suffix,
        Long externalId, // invoice setup id in MFD
        DocumentSequence sequence,
        SourceKind sourceKind
    ) {
        var content = new DocumentContent();
        content.setPrefix(prefix);
        content.setSuffix(suffix);
        content.setExternalId(externalId);
        content.setPropertyId(propertyId);
        content.setDocumentKind(DocumentKind.INVOICE);
        content.setSourceKind(sourceKind);
        content.setDocumentSequence(sequence);

        return content;
    }

    /**
     * Create default content for credit note per source.
     *
     * @param propertyId propertyId
     * @param sequence documentSequence
     * @param sourceKind sourceKind
     * @return DocumentContent
     */
    public DocumentContent createDefaultContentsCreditNote(
        Long propertyId,
        String prefix,
        String suffix,
        Long externalId, // invoice setup id in MFD
        DocumentSequence sequence,
        SourceKind sourceKind
    ) {
        var documentContent = new DocumentContent();
        documentContent.setPrefix(prefix);
        documentContent.setSuffix(suffix);
        documentContent.setExternalId(externalId);
        documentContent.setPropertyId(propertyId);
        documentContent.setDocumentKind(DocumentKind.CREDIT_NOTE);
        documentContent.setSourceKind(sourceKind);
        documentContent.setDocumentSequence(sequence);
        return documentContent;
    }

    /**
     * Create default content for credit note per source.
     *
     * @param propertyId propertyId
     * @param sequence documentSequence
     * @param sourceKind sourceKind
     * @return DocumentContent
     */
    public DocumentContent createDefaultContentsRectifyInvoice(
        Long propertyId,
        String prefix,
        String suffix,
        Long externalId, // invoice setup id in MFD
        DocumentSequence sequence,
        SourceKind sourceKind
    ) {
        var documentContent = new DocumentContent();
        documentContent.setPrefix(prefix);
        documentContent.setSuffix(suffix);
        documentContent.setExternalId(externalId);
        documentContent.setPropertyId(propertyId);
        documentContent.setDocumentKind(DocumentKind.RECTIFY_INVOICE);
        documentContent.setSourceKind(sourceKind);
        documentContent.setDocumentSequence(sequence);
        return documentContent;
    }

    public Optional<DocumentConfig> getDocumentConfigOptional(Long propertyId, DocumentKind documentKind) {
        return documentConfigRepository.findByPropertyIdAndDocumentKind(propertyId, documentKind);
    }

    @Transactional
    public DocumentConfig getDocumentConfig(Long propertyId, DocumentKind documentKind) {
        return documentConfigRepository.findByPropertyIdAndDocumentKind(propertyId, documentKind)
            .orElseThrow();
    }

    @Transactional
    public List<DocumentConfig> getDocumentConfigsByPropertyIds(List<Long> propertyIds) {
        return documentConfigRepository.findAllByPropertyIdIn(propertyIds);
    }

    /**
     * Update the document config.
     *
     * @param propertyId propertyId
     * @param configsUpdateRequest request
     * @param convert documentKind
     * @return DocumentConfig
     */
    @Transactional
    public DocumentConfig updateDocumentConfig(
        Long propertyId,
        ConfigsUpdateRequest configsUpdateRequest,
        DocumentKind convert
    ) {
        var config = getDocumentConfig(propertyId, convert);
        config.setShowDetailedTaxFee(configsUpdateRequest.isShowDetailedTaxFee());
        config.setChargeBreakdown(configsUpdateRequest.isChargeBreakdown());
        config.setUseGuestLang(configsUpdateRequest.isUseGuestLang());
        config.setDueDays(configsUpdateRequest.getDueDays());
        config.setLang(configsUpdateRequest.getLang());
        config.setPrefix(configsUpdateRequest.getPrefix());
        config.setSuffix(configsUpdateRequest.getSuffix());
        config.setLegalCompanyName(configsUpdateRequest.getLegalCompanyName());
        config.setTitle(configsUpdateRequest.getTitle());
        config.setShowLegalCompanyName(configsUpdateRequest.isShowLegalCompanyName());
        config.setIncludeRoomNumber(configsUpdateRequest.isIncludeRoomNumber());
        config.setUseDocumentNumber(configsUpdateRequest.isUseDocumentNumber());

        if (config.isCompact() != configsUpdateRequest.isIsCompact()) {
            config.setCompact(configsUpdateRequest.isIsCompact());
            config.addContent(
                createDefaultContentsInvoice(
                    config.getPropertyId(),
                    configsUpdateRequest.getPrefix(),
                    configsUpdateRequest.getSuffix(),
                    null,
                    config.getSequence(SourceKind.RESERVATION),
                    SourceKind.RESERVATION
                )
            );

            config.addContent(
                createDefaultContentsInvoice(
                    config.getPropertyId(),
                    configsUpdateRequest.getPrefix(),
                    configsUpdateRequest.getSuffix(),
                    null,
                    config.getSequence(SourceKind.GROUP_PROFILE),
                    SourceKind.GROUP_PROFILE
                )
            );
        }

        config.setTaxId1(configsUpdateRequest.getTaxId1());
        config.setTaxId2(configsUpdateRequest.getTaxId2());
        config.setCpf(configsUpdateRequest.getCpf());
        config.setCustomText(configsUpdateRequest.getCustomText());
        return documentConfigRepository.save(config);
    }

    private String getTemplatePath(boolean isCompact, SourceKind sourceKind,
                                   DocumentTemplateLists.DocumentTemplates templates) {
        String templatePath;
        if (isCompact) {
            if (sourceKind.equals(SourceKind.RESERVATION)) {
                templatePath = templates.reservationCompactTemplate();
            } else {
                templatePath = templates.groupCompactTemplate();
            }
        } else {
            if (sourceKind.equals(SourceKind.RESERVATION)) {
                templatePath = templates.reservationTemplate();
            } else {
                templatePath = templates.groupTemplate();
            }
        }
        return templatePath;
    }
}
