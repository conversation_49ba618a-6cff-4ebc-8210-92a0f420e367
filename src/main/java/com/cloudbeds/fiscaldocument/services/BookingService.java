package com.cloudbeds.fiscaldocument.services;

import com.cloudbeds.booking.v1.BookingWithRooms;
import com.cloudbeds.fiscaldocument.grpc.mfd.BookingServiceClient;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BookingService {
    private final BookingServiceClient bookingServiceClient;

    /**
     * Get the details of a booking with rooms for a specific property and reservation identifier.
     *
     * @param propertyId The ID of the property where the booking is located.
     * @param reservationId The id of the reservation.
     * @return A BookingWithRooms object containing details of the booking with rooms.
     * @throws NoSuchElementException if no booking is found.
     */
    public BookingWithRooms getBooking(Long propertyId, Long reservationId) {
        return bookingServiceClient.listBookings(propertyId, List.of(reservationId))
            .stream().findFirst().orElseThrow();
    }

    /**
     * Get a list of bookings with rooms for a specific property and reservation IDs.
     *
     * @param propertyId The ID of the property where the bookings are located.
     * @param reservationIds Set of reservation IDs to filter the bookings by.
     * @return List of BookingWithRooms objects containing details of the bookings with rooms.
     */
    public List<BookingWithRooms> getBookings(Long propertyId, Set<Long> reservationIds) {
        return bookingServiceClient.listBookings(propertyId, reservationIds);
    }
}
