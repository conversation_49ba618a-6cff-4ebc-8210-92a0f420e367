package com.cloudbeds.fiscaldocument.services;

import com.cloudbeds.distributedid.pool.IdPool;
import com.cloudbeds.dynamicquerying.model.PageRequest;
import com.cloudbeds.fiscaldocument.controller.model.Action;
import com.cloudbeds.fiscaldocument.controller.model.CreateCreditNoteRequest;
import com.cloudbeds.fiscaldocument.controller.model.CreateInvoiceRequest;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentDetailedResponse;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPaginated;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentPatchRequest;
import com.cloudbeds.fiscaldocument.controller.model.FiscalDocumentTransactionsPaginated;
import com.cloudbeds.fiscaldocument.controller.model.RectifyInvoiceNoteRequest;
import com.cloudbeds.fiscaldocument.converters.GovernmentIntegrationToEntityConverter;
import com.cloudbeds.fiscaldocument.converters.ListPostedTransactionsResponseToRestConverter;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentLinkedDocument;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.entity.FiscalDocumentTransaction;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument_;
import com.cloudbeds.fiscaldocument.enums.CreationMethod;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import com.cloudbeds.fiscaldocument.enums.Origin;
import com.cloudbeds.fiscaldocument.enums.PropertyFeature;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import com.cloudbeds.fiscaldocument.grpc.accounting.AccountingService;
import com.cloudbeds.fiscaldocument.grpc.marketplace.MarketplaceServiceClient;
import com.cloudbeds.fiscaldocument.grpc.organization.PropertyServiceClient;
import com.cloudbeds.fiscaldocument.mappers.FiscalDocumentMapper;
import com.cloudbeds.fiscaldocument.outbox.FiscalDocumentEventSyncService;
import com.cloudbeds.fiscaldocument.outbox.FiscalDocumentSyncService;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentCustomRepositoryImpl;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentRepository;
import com.cloudbeds.fiscaldocument.repositories.FiscalDocumentTransactionRepository;
import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import com.cloudbeds.fiscaldocument.utils.DateTimeService;
import com.cloudbeds.fiscaldocument.utils.EnumConverterUtil;
import com.cloudbeds.fiscaldocument.utils.constants.CountryCodes;
import com.cloudbeds.fiscaldocument.utils.constants.CreditNoteActionLists;
import com.cloudbeds.fiscaldocument.utils.constants.InvoiceActionLists;
import com.cloudbeds.fiscaldocument.utils.constants.RectifyInvoiceActionLists;
import com.cloudbeds.organization.v1.Property;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import static com.cloudbeds.fiscaldocument.enums.DocumentStatus.FAILED;
import static com.cloudbeds.fiscaldocument.enums.DocumentStatus.OPEN;
import static com.cloudbeds.fiscaldocument.enums.DocumentStatus.REQUESTED;
import static com.cloudbeds.fiscaldocument.enums.DocumentStatus.VOID_REQUESTED;
import static com.cloudbeds.fiscaldocument.utils.EnumConverterUtil.convert;

@Service
@RequiredArgsConstructor
@Slf4j
public class FiscalDocumentService {

    private final FiscalDocumentRepository fiscalDocumentRepository;
    private final FiscalDocumentTransactionRepository fiscalDocumentTransactionRepository;
    private final FiscalDocumentCustomRepositoryImpl fiscalDocumentCustomRepository;
    private final AccountingService accountingService;
    private final PropertyServiceClient propertyServiceClient;
    private final IdPool idPool;
    private final ListPostedTransactionsResponseToRestConverter listPostedTransactionsResponseToRestConverter;
    private final GovernmentIntegrationToEntityConverter governmentIntegrationToEntityConverter;
    private final FiscalDocumentEventSyncService fiscalDocumentEventSyncService;
    private final FiscalDocumentMapper fiscalDocumentMapper;
    private final FiscalDocumentSyncService fiscalDocumentSyncService;
    private final DateTimeService dateTimeService;
    private final FiscalDocumentRecipientService recipientService;
    private final MarketplaceServiceClient marketplaceServiceClient;

    /**
     * Valid statuses for the linked documents of an invoice that can be voided.
     */
    public static final List<DocumentStatus> VALID_LINKED_DOCUMENT_STATUSES = List.of(OPEN, REQUESTED, VOID_REQUESTED);

    /**
     * Gets fiscal document paginated.
     *
     * @param specification the specification
     * @param pageRequest the page request
     * @return the fiscal document paginated
     * @throws IllegalArgumentException if the specification is invalid
     */
    @Transactional(readOnly = true)
    public FiscalDocumentPaginated getFiscalDocumentDetailedPaginated(
        Specification<FiscalDocument> specification,
        PageRequest<FiscalDocument> pageRequest
    ) throws IllegalArgumentException {
        var documents = fiscalDocumentCustomRepository.findAll(specification, pageRequest);

        var propertyIds = documents.getContent().stream()
            .map(FiscalDocument::getPropertyId)
            .collect(Collectors.toSet());

        if (propertyIds.isEmpty()) {
            return new FiscalDocumentPaginated()
                .fiscalDocuments(List.of())
                .nextPageToken(documents.getNextPageToken());
        }

        var properties = propertyServiceClient.listProperties(propertyIds)
            .stream()
            .collect(Collectors.toMap(
                Property::getId,
                property -> property.getPropertyProfile().getHotelAddress().getCountryCode())
            );

        var fiscalDocuments = fiscalDocumentMapper.toDetailedResponse(documents.getContent());

        // Enrich with latest linked document information
        enrichWithLatestLinkedDocuments(fiscalDocuments, documents.getContent());

        setActions(fiscalDocuments, properties);

        return new FiscalDocumentPaginated()
            .fiscalDocuments(fiscalDocuments)
            .nextPageToken(documents.getNextPageToken());
    }

    @Transactional(readOnly = true)
    public List<FiscalDocument> findAllBySpecification(Specification<FiscalDocument> specification) {
        return fiscalDocumentRepository.findAll(specification);
    }

    @Transactional(readOnly = true)
    public Optional<FiscalDocument> findOneBySpecification(Specification<FiscalDocument> specification) {
        return fiscalDocumentRepository.findOne(specification);
    }

    /**
     * Receives a message and stores it in the database.
     *
     * @param request    the message to store
     * @param propertyId propertyId
     * @return the stored message
     */
    @Transactional
    public FiscalDocument createInvoice(CreateInvoiceRequest request, Long propertyId) {
        var sourceKind = EnumConverterUtil.convertToEntity(request.getSourceKind());
        checkPropertyFeatures(sourceKind, propertyId);
        checkIfTransactionsAreAvailable(request.getTransactionIds());
        recipientService.checkRecipientType(sourceKind, request.getRecipient());

        var property = propertyServiceClient.getProperty(propertyId);
        try {
            var id = idPool.nextIdWithRetry(5, 1000);
            var document = new FiscalDocument();

            var propertyZone = TimeZone.getTimeZone(property.getTimezone()).toZoneId();
            var dateNow = dateTimeService.getLocalDate(propertyZone);

            document.setId(id);
            document.setKind(DocumentKind.INVOICE);
            document.setInvoiceDate(dateNow);
            document.setSourceId(request.getSourceId());
            document.setSourceKind(EnumConverterUtil.convertToEntity(request.getSourceKind()));
            document.setPropertyId(propertyId);
            document.setUserId(request.getUserId());
            document.setStatus(REQUESTED);
            document.setOrigin(getDocumentOrigin(property));
            document.setTransactionList(
                request.getTransactionIds().stream().map(transactionId -> {
                    var transaction = new FiscalDocumentTransaction();
                    transaction.setTransactionId(transactionId);
                    transaction.setFiscalDocumentId(id);
                    return transaction;
                }).toList()
            );
            document.setRecipients(recipientService.getRecipients(
                    document.getId(),
                    propertyId,
                    List.of(request.getRecipient()),
                    document.getSourceKind(),
                    document.getSourceId()
                )
            );

            document.setSequenceId(request.getSequenceId());

            return saveAndSendEvent(document);
        } catch (FiscalDocumentException e) {
            log.error("Error while storing FiscalDocument", e);
            throw e;
        } catch (Exception e) {
            log.error("Error while storing FiscalDocument", e);
            throw new FiscalDocumentException(
                ErrorCode.UNEXPECTED_ERROR,
                "Error while storing FiscalDocument."
            );
        }
    }

    private Origin getDocumentOrigin(Property property) {
        var countryCode = property.getPropertyProfile().getHotelAddress().getCountryCode();
        if (CountryCodes.SPAIN.getCode().equalsIgnoreCase(countryCode)
            && marketplaceServiceClient.getInvoiceIntegrationEnabled(property.getId())) {
            return Origin.INTEGRATION;
        }
        return Origin.FISCAL_DOCUMENT;
    }

    /**
     * Create credit note.
     *
     * @param request request
     * @param propertyId propertyId
     * @return invoice
     */
    @Transactional
    public FiscalDocument createCreditNote(CreateCreditNoteRequest request, Long propertyId) {
        var property = propertyServiceClient.getProperty(propertyId);

        try {
            var invoice = fiscalDocumentRepository.findById(request.getInvoiceId()).orElseThrow();

            invoice.getLinkedChildDocuments().forEach(invoiceLinkedDocument -> {
                if (!VALID_LINKED_DOCUMENT_STATUSES.contains(invoiceLinkedDocument.getFiscalDocument().getStatus())) {
                    throw new FiscalDocumentException(
                        ErrorCode.UNEXPECTED_ERROR,
                        "Linked child document status is not in: " + VALID_LINKED_DOCUMENT_STATUSES);
                }
            });

            var document = new FiscalDocument();
            var id = idPool.nextIdWithRetry(5, 1000);
            var linkedDocument = new FiscalDocumentLinkedDocument();
            linkedDocument.setLinkedDocumentId(invoice.getId());
            linkedDocument.setFiscalDocumentId(id);
            document.setId(id);
            document.setKind(DocumentKind.CREDIT_NOTE);
            document.setOrigin(getDocumentOrigin(property));
            document.setSourceId(invoice.getSourceId());
            document.setSourceKind(invoice.getSourceKind());
            document.setPropertyId(propertyId);

            var propertyZone = TimeZone.getTimeZone(property.getTimezone()).toZoneId();
            var dateNow = dateTimeService.getLocalDate(propertyZone);
            document.setInvoiceDate(dateNow);

            var recipients = invoice.getRecipients().stream()
                .map(recipient -> {
                    var fiscalDocumentRecipient = new FiscalDocumentRecipient();
                    fiscalDocumentRecipient.setRecipientId(recipient.getRecipientId());
                    fiscalDocumentRecipient.setFiscalDocumentId(id);
                    fiscalDocumentRecipient.setRecipient(recipient.getRecipient());

                    return fiscalDocumentRecipient;
                })
                .toList();

            document.setUserId(request.getUserId());
            document.setStatus(REQUESTED);
            document.addLinkedDocument(linkedDocument);
            document.setSequenceId(request.getSequenceId());
            document.setReason(request.getReason());
            document.setRecipients(recipients);
            document.setMethod(EnumConverterUtil.convertToEntity(request.getMethod()));
            if (request.getTransactionIds() != null && !request.getTransactionIds().isEmpty()) {
                checkIfTransactionsAreAvailable(request.getTransactionIds());
                document.setTransactionList(
                    request.getTransactionIds().stream().map(transactionId -> {
                        var transaction = new FiscalDocumentTransaction();
                        transaction.setTransactionId(transactionId);
                        transaction.setFiscalDocumentId(id);
                        return transaction;
                    }).toList()
                );
            }

            return saveAndSendEvent(document);
        } catch (FiscalDocumentException e) {
            log.error("Error while storing FiscalDocument", e);
            throw e;
        } catch (Exception e) {
            log.error("Error while storing FiscalDocument", e);
            throw new FiscalDocumentException(
                ErrorCode.UNEXPECTED_ERROR,
                "Error while storing FiscalDocument."
            );
        }
    }

    /**
     * Creates a rectify invoice.
     *
     * @param request request
     * @param propertyId propertyId
     * @return FiscalDocument
     */
    @Transactional
    public FiscalDocument createRectifyInvoice(
        RectifyInvoiceNoteRequest request,
        Long propertyId
    ) {
        var property = propertyServiceClient.getProperty(propertyId);
        var countryCode = property.getPropertyProfile().getHotelAddress().getCountryCode();
        if (!CountryCodes.SPAIN.getCode().equalsIgnoreCase(countryCode)) {
            throw new FiscalDocumentException(
                ErrorCode.INVALID_REQUEST,
                "Rectify invoice is not supported for this property"
            );
        }

        try {
            var invoice = fiscalDocumentRepository.findById(request.getInvoiceId()).orElseThrow();

            // Validate Spanish rectification chain rules
            validateSpanishRectificationChain(invoice);

            var document = new FiscalDocument();
            var id = idPool.nextIdWithRetry(5, 1000);
            var linkedDocument = new FiscalDocumentLinkedDocument();
            linkedDocument.setLinkedDocumentId(invoice.getId());
            linkedDocument.setFiscalDocumentId(id);
            document.setId(id);
            document.setKind(DocumentKind.RECTIFY_INVOICE);
            document.setOrigin(getDocumentOrigin(property));
            document.setSourceId(invoice.getSourceId());
            document.setSourceKind(invoice.getSourceKind());
            document.setPropertyId(propertyId);

            var propertyZone = TimeZone.getTimeZone(property.getTimezone()).toZoneId();
            var dateNow = dateTimeService.getLocalDate(propertyZone);
            document.setInvoiceDate(dateNow);

            document.setUserId(request.getUserId());
            document.setStatus(REQUESTED);
            document.addLinkedDocument(linkedDocument);
            document.setReason(request.getReason());

            var recipients = invoice.getRecipients().stream()
                .map(recipient -> {
                    var fiscalDocumentRecipient = new FiscalDocumentRecipient();
                    fiscalDocumentRecipient.setRecipientId(recipient.getRecipientId());
                    fiscalDocumentRecipient.setFiscalDocumentId(id);
                    fiscalDocumentRecipient.setRecipient(recipient.getRecipient());

                    return fiscalDocumentRecipient;
                })
                .toList();

            document.setRecipients(recipients);
            document.setMethod(EnumConverterUtil.convertToEntity(request.getMethod()));
            if (request.getTransactionIds() != null && !request.getTransactionIds().isEmpty()) {
                checkIfTransactionsAreAvailable(request.getTransactionIds());
                document.setTransactionList(
                    request.getTransactionIds().stream().map(transactionId -> {
                        var transaction = new FiscalDocumentTransaction();
                        transaction.setTransactionId(transactionId);
                        transaction.setFiscalDocumentId(id);
                        return transaction;
                    }).toList()
                );
            }

            return saveAndSendEvent(document);
        } catch (FiscalDocumentException e) {
            log.error("Error while storing FiscalDocument", e);
            throw e;
        } catch (Exception e) {
            log.error("Error while storing FiscalDocument", e);
            throw new FiscalDocumentException(
                ErrorCode.UNEXPECTED_ERROR,
                "Error while storing FiscalDocument."
            );
        }
    }

    @Transactional
    public void saveAll(Collection<FiscalDocument> documents) {
        documents.forEach(this::save);
    }

    @Transactional
    public Optional<FiscalDocument> findById(long id) {
        return fiscalDocumentRepository.findById(id);
    }

    /**
     * Gets all not invoiced transactions for source.
     *
     * @return the stored message
     */
    @Transactional(readOnly = true)
    public FiscalDocumentTransactionsPaginated getSourceAvailableTransactions(
        Long propertyId,
        Long sourceId,
        SourceKind sourceKind,
        DocumentKind documentKind,
        String pageToken,
        Integer limit
    ) {
        var documentSpec =
            getDocumentsForSource(propertyId, sourceId, sourceKind, DocumentStatus.getBlockingTransactionsStatuses());
        var invoicedTransactionIds = fiscalDocumentRepository.findAll(documentSpec).stream()
            .map(FiscalDocument::getTransactionList)
            .flatMap(List::stream)
            .map(FiscalDocumentTransaction::getTransactionId)
            .collect(Collectors.toSet());

        var transactionResponse = accountingService.getAvailableTransactions(
            propertyId, invoicedTransactionIds, sourceId, sourceKind, documentKind, pageToken, limit
        );

        return listPostedTransactionsResponseToRestConverter.convert(propertyId, transactionResponse);
    }

    /**
     * Gets all invoiced transactions for fiscalDocumentId.
     *
     * @return paginated response object
     */
    @Transactional(readOnly = true)
    public FiscalDocumentTransactionsPaginated getFiscalDocumentTransactions(
        Long propertyId,
        Long fiscalDocumentId,
        Boolean includeLinkedDocumentTransactions,
        String pageToken,
        Integer limit
    ) {
        var fiscalDocument = fiscalDocumentRepository.findById(fiscalDocumentId).orElseThrow();
        var transactionIds = new ArrayList<Long>();
        if (includeLinkedDocumentTransactions || CreationMethod.VOID.equals(fiscalDocument.getMethod())) {
            getTransactionIdsRecursive(fiscalDocument).forEach(transactionIds::add);
        } else {
            fiscalDocument.getTransactionList().stream()
                .map(FiscalDocumentTransaction::getTransactionId)
                .forEach(transactionIds::add);
        }
        var transactionResponse = accountingService.getTransactionsByIds(
            propertyId, transactionIds, pageToken, limit
        );

        return listPostedTransactionsResponseToRestConverter.convert(propertyId, transactionResponse);
    }

    private Stream<Long> getTransactionIdsRecursive(FiscalDocument fiscalDocument) {
        var documentTransactionIds = fiscalDocumentTransactionRepository
            .findAllByFiscalDocumentId(fiscalDocument.getId()).stream()
            .map(FiscalDocumentTransaction::getTransactionId);

        var linkedDocuments = new ArrayList<FiscalDocument>();

        if (CreationMethod.VOID.equals(fiscalDocument.getMethod())) {
            linkedDocuments.addAll(fiscalDocument.getLinkedDocuments().stream()
                .map(FiscalDocumentLinkedDocument::getLinkedDocument)
                .toList());
        } else {
            linkedDocuments.addAll(fiscalDocument.getLinkedChildDocuments().stream()
                .map(FiscalDocumentLinkedDocument::getFiscalDocument)
                .filter(doc -> !CreationMethod.VOID.equals(doc.getMethod()))
                .toList());
        }

        var linkedChildDocumentTransactionIds = linkedDocuments.stream()
            .flatMap(this::getTransactionIdsRecursive)
            .distinct();

        return Stream.concat(documentTransactionIds, linkedChildDocumentTransactionIds);
    }


    /**
     * Update fiscal document.
     *
     * @param id the id
     * @param propertyId the property id
     * @param fiscalDocumentPatchRequest the fiscal document patch request
     * @return the optional
     */
    @Transactional
    public Optional<FiscalDocument> putFiscalDocument(
        Long id,
        Long propertyId,
        FiscalDocumentPatchRequest fiscalDocumentPatchRequest
    ) {
        var documentOptional = fiscalDocumentRepository.findByPropertyIdAndId(propertyId, id);
        if (documentOptional.isEmpty()) {
            return Optional.empty();
        }
        var newStatus = convert(fiscalDocumentPatchRequest.getStatus());

        if (FAILED.equals(newStatus) && !StringUtils.hasText(fiscalDocumentPatchRequest.getFailReason())) {
            throw new FiscalDocumentException(
                ErrorCode.INVALID_REQUEST,
                "Fail reason is required when status is FAILED"
            );
        }

        if (DocumentStatus.COMPLETED_INTEGRATION.equals(newStatus)
            && fiscalDocumentPatchRequest.getGovernmentIntegration() == null) {
            throw new FiscalDocumentException(
                ErrorCode.INVALID_REQUEST,
                "Government integration is required when status is COMPLETED_INTEGRATION"
            );
        }

        if (DocumentStatus.FAILED_INTEGRATION.equals(newStatus)
            && (
                fiscalDocumentPatchRequest.getGovernmentIntegration() == null
                || !StringUtils.hasText(fiscalDocumentPatchRequest.getFailReason())
            )
        ) {
            throw new FiscalDocumentException(
                ErrorCode.INVALID_REQUEST,
                "Government integration and fail reason is required when status is FAILED_INTEGRATION"
            );
        }

        var document = documentOptional.get();
        var shouldSendCrateEvent = !DocumentStatus.COMPLETED_INTEGRATION.equals(document.getStatus())
            && DocumentStatus.getIntegrationStatusForSendEvent().contains(newStatus);

        document.setStatus(newStatus);
        document.setFailReason(fiscalDocumentPatchRequest.getFailReason());

        if (fiscalDocumentPatchRequest.getGovernmentIntegration() != null) {
            document.setGovernmentIntegration(
                governmentIntegrationToEntityConverter.convert(fiscalDocumentPatchRequest.getGovernmentIntegration())
            );

            if (DocumentStatus.getIntegrationStatusForSendEvent().contains(newStatus)
                && (document.getGovernmentIntegration().getQr() == null
                    || !StringUtils.hasText(document.getGovernmentIntegration().getNumber())
                    || !StringUtils.hasText(document.getGovernmentIntegration().getUrl()))) {
                throw new FiscalDocumentException(
                    ErrorCode.INVALID_REQUEST,
                    "QR code, number and URL are required when status is any of "
                        + DocumentStatus.getIntegrationStatusForSendEvent()
                        .stream()
                        .map(Enum::name)
                        .collect(Collectors.joining(", "))
                );
            }

            if (shouldSendCrateEvent) {
                sendIntegrationCreateDocumentEvent(document);
            }
        }

        return Optional.of(save(document));
    }

    private void sendIntegrationCreateDocumentEvent(FiscalDocument document) {
        fiscalDocumentEventSyncService.saveIntegrationEvent(document);
    }

    public FiscalDocument saveAndSendEvent(FiscalDocument document) {
        fiscalDocumentEventSyncService.save(document);
        return save(document);
    }

    @Transactional
    public FiscalDocument save(FiscalDocument document) {
        fiscalDocumentSyncService.save(document);
        return fiscalDocumentRepository.save(document);
    }

    private void checkIfTransactionsAreAvailable(List<Long> transactionIds) {
        var invalidTransactions = fiscalDocumentTransactionRepository.findAllByTransactionIdInAndFiscalDocumentStatusIn(
            transactionIds,
            DocumentStatus.getBlockingTransactionsStatuses()
        );

        if (!invalidTransactions.isEmpty()) {
            throw new FiscalDocumentException(ErrorCode.INVALID_REQUEST, "Transactions are already invoiced: "
                + invalidTransactions.stream()
                .map(FiscalDocumentTransaction::getTransactionId)
                .map(String::valueOf)
                .collect(Collectors.joining(", "))
            );
        }
    }

    /**
     * Validates Spanish rectification chain rules using the invoice's linked documents.
     * In Spain, you cannot rectify an invoice that has already been rectified.
     * Users must rectify the most recent invoice in the chain.
     *
     * @param invoice the invoice to be rectified
     * @throws FiscalDocumentException if the invoice has already been rectified
     */
    private void validateSpanishRectificationChain(FiscalDocument invoice) {
        var activeStatuses = DocumentStatus.getActiveStatuses();

        // Check if this invoice has already been rectified by looking for rectifying invoices that link to it
        boolean hasBeenRectified = fiscalDocumentRepository.hasBeenRectified(
            invoice.getId(),
            DocumentKind.RECTIFY_INVOICE,
            activeStatuses
        );

        if (hasBeenRectified) {
            // Find the latest rectifiable invoice in the chain
            var latestRectifiableInvoice = findLatestRectifiableInvoice(invoice.getId(), invoice.getPropertyId());

            String errorMessage = "This invoice has already been rectified and cannot be rectified again "
                + "according to Spanish fiscal regulations.";

            if (!latestRectifiableInvoice.getId().equals(invoice.getId())) {
                errorMessage += String.format(
                    " To make corrections, you must rectify the most recent invoice in the chain "
                    + "(Invoice ID: %d, Number: %s).",
                    latestRectifiableInvoice.getId(),
                    latestRectifiableInvoice.getNumber()
                );
            } else {
                errorMessage += " Please rectify the most recent invoice in the rectification chain instead.";
            }

            throw new FiscalDocumentException(ErrorCode.INVOICE_ALREADY_RECTIFIED, errorMessage);
        }
    }

    /**
     * Finds the latest rectifiable invoice in a chain starting from a given invoice.
     * This method helps identify which invoice should be rectified next according to Spanish fiscal rules.
     *
     * @param originalInvoiceId the ID of the original invoice in the chain
     * @param propertyId the property ID for context
     * @return the latest invoice that can be rectified, or the original if no rectifications exist
     */
    @Transactional(readOnly = true)
    public FiscalDocument findLatestRectifiableInvoice(Long originalInvoiceId, Long propertyId) {
        var activeStatuses = DocumentStatus.getActiveStatuses();

        // Try to find the latest rectifying invoice
        var latestRectifyingInvoice = fiscalDocumentRepository.findLatestRectifyingInvoice(
            originalInvoiceId,
            DocumentKind.RECTIFY_INVOICE,
            activeStatuses
        );

        if (latestRectifyingInvoice.isPresent()) {
            // If there's a rectifying invoice, check if it has been rectified too
            var latestInvoice = latestRectifyingInvoice.get();
            boolean latestHasBeenRectified = fiscalDocumentRepository.hasBeenRectified(
                latestInvoice.getId(),
                DocumentKind.RECTIFY_INVOICE,
                activeStatuses
            );

            if (latestHasBeenRectified) {
                // Recursively find the latest in the chain
                return findLatestRectifiableInvoice(latestInvoice.getId(), propertyId);
            } else {
                // This is the latest rectifiable invoice
                return latestInvoice;
            }
        } else {
            // No rectifications found, return the original invoice
            return fiscalDocumentRepository.findById(originalInvoiceId)
                .orElseThrow(() -> new FiscalDocumentException(
                    ErrorCode.INVALID_REQUEST,
                    "Invoice not found: " + originalInvoiceId
                ));
        }
    }

    /**
     * Gets the latest linked document information for a fiscal document.
     * This is used to identify the most recent document in a rectification chain.
     *
     * @param fiscalDocument the fiscal document to check
     * @return the latest linked document information, or null if no linked documents exist
     */
    @Transactional(readOnly = true)
    public FiscalDocument getLatestLinkedDocument(FiscalDocument fiscalDocument) {
        if (fiscalDocument.getKind() == DocumentKind.RECTIFY_INVOICE
                || fiscalDocument.getKind() == DocumentKind.CREDIT_NOTE) {
            // For rectifying invoices and credit notes, find the latest in the chain
            return findLatestRectifiableInvoice(fiscalDocument.getId(), fiscalDocument.getPropertyId());
        } else {
            // For regular invoices, check if they have been rectified
            var activeStatuses = DocumentStatus.getActiveStatuses();
            var latestRectifyingInvoice = fiscalDocumentRepository.findLatestRectifyingInvoice(
                fiscalDocument.getId(),
                DocumentKind.RECTIFY_INVOICE,
                activeStatuses
            );

            if (latestRectifyingInvoice.isPresent()) {
                // Return the latest rectifying invoice in the chain
                return findLatestRectifiableInvoice(fiscalDocument.getId(), fiscalDocument.getPropertyId());
            }
        }

        return null;
    }

    /**
     * Enriches fiscal document responses with latest linked document information.
     *
     * @param responses the responses to enrich
     * @param documents the corresponding fiscal documents
     */
    private void enrichWithLatestLinkedDocuments(
        List<FiscalDocumentDetailedResponse> responses,
        List<FiscalDocument> documents
    ) {
        for (int i = 0; i < responses.size() && i < documents.size(); i++) {
            var response = responses.get(i);
            var document = documents.get(i);

            var latestLinkedDocument = getLatestLinkedDocument(document);
            if (latestLinkedDocument != null) {
                response.setLatestLinkedDocument(fiscalDocumentMapper.mapLatestLinkedDocument(latestLinkedDocument));
            }
        }
    }

    private void checkPropertyFeatures(SourceKind sourceKind, long propertyId) {
        PropertyFeature feature;

        switch (sourceKind) {
            case HOUSE_ACCOUNT:
                feature = PropertyFeature.HOUSE_ACCOUNT;
                break;
            case ACCOUNTS_RECEIVABLE_LEDGER:
                feature = PropertyFeature.ACCOUNTS_RECEIVABLE_LEDGER;
                break;
            case GROUP_PROFILE:
                feature = PropertyFeature.GROUP_PROFILE;
                break;
            default:
                return;
        }

        if (!propertyServiceClient.isPropertyFeatureEnabled(propertyId, feature)) {
            throw new FiscalDocumentException(
                ErrorCode.INVALID_REQUEST,
                "Property feature '" + feature + "' must be enabled to use: " + sourceKind
            );
        }
    }

    private Specification<FiscalDocument> getDocumentsForSource(
        Long propertyId,
        Long sourceId,
        SourceKind sourceKind,
        List<DocumentStatus> statuses
    ) {
        return (root, query, cb) -> cb.and(
            cb.equal(root.get(FiscalDocument_.PROPERTY_ID), propertyId),
            cb.equal(root.get(FiscalDocument_.SOURCE_ID), sourceId),
            cb.equal(root.get(FiscalDocument_.SOURCE_KIND), sourceKind),
            root.get(FiscalDocument_.STATUS).in(statuses)
        );
    }


    private void setActions(List<FiscalDocumentDetailedResponse> fiscalDocuments, Map<Long, String> propertyCountries) {
        fiscalDocuments.forEach(fiscalDocument -> {
            var status = convert(fiscalDocument.getStatus());
            var country = "";
            if (propertyCountries.containsKey(Long.valueOf(fiscalDocument.getPropertyId()))) {
                country = propertyCountries.get(Long.valueOf(fiscalDocument.getPropertyId())).toLowerCase();
            }

            var actions = getActions(country, status, fiscalDocument.getKind());

            // Apply Spanish rectification rules for invoices
            if (fiscalDocument.getKind() == FiscalDocumentKind.INVOICE && "es".equals(country)) {
                actions = applySpanishRectificationRules(fiscalDocument, actions);
            }

            fiscalDocument.setActions(actions);
        });
    }

    /**
     * Applies Spanish rectification rules to invoice actions.
     * For Spanish invoices, the RECTIFY action should be available for all documents in a rectification chain,
     * but not if the latest document in the chain is void.
     *
     * @param fiscalDocument the fiscal document response
     * @param actions the current list of actions
     * @return the modified list of actions
     */
    private List<Action> applySpanishRectificationRules(
            FiscalDocumentDetailedResponse fiscalDocument, List<Action> actions) {
        // Check if the latest linked document is void
        if (fiscalDocument.getLatestLinkedDocument() != null) {
            var latestStatus = convert(fiscalDocument.getLatestLinkedDocument().getStatus());

            // If the latest document in the chain is void, remove rectify action
            if (latestStatus == DocumentStatus.VOIDED) {
                return actions.stream()
                    .filter(action -> action.getType()
                        != com.cloudbeds.fiscaldocument.controller.model.DocumentAction.RECTIFY)
                    .collect(Collectors.toList());
            }
        }

        // For Spanish invoices, ensure RECTIFY action is available for all documents in chain
        // (UI will handle the logic of which one to actually allow)
        boolean hasRectifyAction = actions.stream()
            .anyMatch(action -> action.getType()
                == com.cloudbeds.fiscaldocument.controller.model.DocumentAction.RECTIFY);

        if (!hasRectifyAction) {
            var newActions = new ArrayList<>(actions);
            newActions.add(new Action()
                .type(com.cloudbeds.fiscaldocument.controller.model.DocumentAction.RECTIFY));
            return newActions;
        }

        return actions;
    }

    private List<Action> getActions(String country, DocumentStatus status, FiscalDocumentKind documentKind) {
        var actionsPerCounty = InvoiceActionLists.ACTIONS_PER_COUNTRY.getOrDefault(status, Map.of());
        var defaultActions = InvoiceActionLists.DEFAULT_ACTIONS;
        if (documentKind == FiscalDocumentKind.CREDIT_NOTE) {
            actionsPerCounty = CreditNoteActionLists.ACTIONS_PER_COUNTRY.getOrDefault(status, Map.of());
            defaultActions = CreditNoteActionLists.DEFAULT_ACTIONS;
        } else if (documentKind == FiscalDocumentKind.RECTIFY_INVOICE) {
            actionsPerCounty = RectifyInvoiceActionLists.ACTIONS_PER_COUNTRY.getOrDefault(status, Map.of());
            defaultActions = RectifyInvoiceActionLists.DEFAULT_ACTIONS;
        }

        if (actionsPerCounty.containsKey(country)) {
            return actionsPerCounty.get(country).stream()
                .map(action -> new Action().type(EnumConverterUtil.convert(action)))
                .collect(Collectors.toList());
        } else {
            return defaultActions.getOrDefault(status, List.of()).stream()
                .map(action -> new Action().type(EnumConverterUtil.convert(action)))
                .collect(Collectors.toList());
        }
    }
}
