package com.cloudbeds.fiscaldocument.services;

import com.cloudbeds.fiscaldocument.myfrontdesk.MyFrontDeskDataService;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.entity.CurrencySettingsDetails;
import com.cloudbeds.fiscaldocument.support.security.SecurityContextHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class CurrencyService {
    private final MyFrontDeskDataService myFrontDeskDataService;

    private final SecurityContextHelper securityContextHelper;

    /**
     * Get currency settings.
     *
     * @param propertyId Property ID
     * @return Currency settings
     */
    public CurrencySettingsDetails getCurrencySettings(Long propertyId) {
        var authenticatedUser = securityContextHelper.getAuthenticatedUser();

        return myFrontDeskDataService.getCurrencies(propertyId, authenticatedUser.getAccessToken());
    }
}
