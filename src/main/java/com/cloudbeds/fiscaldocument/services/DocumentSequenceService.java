package com.cloudbeds.fiscaldocument.services;

import com.cloudbeds.fiscaldocument.entity.DocumentSequence;
import com.cloudbeds.fiscaldocument.repositories.DocumentSequenceRepository;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@Slf4j
public class DocumentSequenceService {

    private final DocumentSequenceRepository documentSequenceRepository;

    @Transactional
    public DocumentSequence getSequenceWithLock(Long sequenceId, Long propertyId) {
        return documentSequenceRepository.findWithLockByIdAndPropertyId(sequenceId, propertyId);
    }

    @Transactional
    public List<DocumentSequence> findAllByPropertyIds(List<Long> propertyIds) {
        return documentSequenceRepository.findAllByPropertyIdIn(propertyIds);
    }

    @Transactional
    public void save(DocumentSequence documentSequence) {
        documentSequenceRepository.save(documentSequence);
    }

    @Transactional
    public DocumentSequence getOrCreate(Long propertyId) {
        return documentSequenceRepository.findByPropertyId(propertyId)
            .orElseGet(() -> createDefaultSequence(propertyId));
    }

    /**
     * Create default sequence for a property.
     *
     * @param propertyId propertyId
     * @return DocumentSequence
     */
    @Transactional
    public DocumentSequence createDefaultSequence(Long propertyId) {
        var documentSequence = new DocumentSequence();
        documentSequence.setPropertyId(propertyId);
        documentSequence.setAutoReset(false);
        documentSequence.setNumber(0L);
        documentSequence.setIncrement(1);
        return documentSequenceRepository.save(documentSequence);
    }

    @Transactional
    public void saveAll(Set<DocumentSequence> sequences) {
        documentSequenceRepository.saveAll(sequences);
    }
}
