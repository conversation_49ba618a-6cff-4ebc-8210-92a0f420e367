package com.cloudbeds.fiscaldocument.repositories;

import com.cloudbeds.fiscaldocument.entity.DocumentSequence;
import jakarta.persistence.LockModeType;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;

public interface DocumentSequenceRepository extends JpaRepository<DocumentSequence, Long> {
    @Lock(LockModeType.PESSIMISTIC_READ)
    DocumentSequence findWithLockByIdAndPropertyId(Long id, Long propertyId);

    Optional<DocumentSequence> findByPropertyId(Long propertyId);

    List<DocumentSequence> findAllByPropertyIdIn(List<Long> propertyIds);
}
