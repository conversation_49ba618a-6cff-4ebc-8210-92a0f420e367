package com.cloudbeds.fiscaldocument.repositories;

import com.cloudbeds.fiscaldocument.entity.FiscalDocumentTransaction;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface FiscalDocumentTransactionRepository extends JpaRepository<FiscalDocumentTransaction, Long> {
    List<FiscalDocumentTransaction> findAllByTransactionIdInAndFiscalDocumentStatusIn(
        List<Long> transactionIds,
        List<DocumentStatus> statuses
    );

    List<FiscalDocumentTransaction> findAllByFiscalDocumentId(Long documentId);
}
