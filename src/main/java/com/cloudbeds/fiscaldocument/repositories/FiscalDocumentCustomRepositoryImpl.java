package com.cloudbeds.fiscaldocument.repositories;

import com.cloudbeds.dynamicquerying.DefaultCursorBasedPagingAndSortingRepository;
import com.cloudbeds.dynamicquerying.model.CursorPage;
import com.cloudbeds.dynamicquerying.model.PageRequest;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.enums.FiscalDocumentSortField;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Repository;

@Repository
public class FiscalDocumentCustomRepositoryImpl
    extends DefaultCursorBasedPagingAndSortingRepository<FiscalDocument>
    implements FiscalDocumentCustomRepository {

    public FiscalDocumentCustomRepositoryImpl(ObjectMapper objectMapper, EntityManager entityManager) {
        super(objectMapper, entityManager, FiscalDocument.class, FiscalDocumentSortField.ID);
    }

    /**
     * Find all fiscal documents.
     *
     * @param specification specification
     * @param pageRequest   page request
     * @return page with transactions
     */

    public CursorPage<FiscalDocument> findFiscalDocuments(
        Specification<FiscalDocument> specification,
        PageRequest<FiscalDocument> pageRequest
    ) throws IllegalArgumentException {
        var graph = entityManager.createEntityGraph(FiscalDocument.class);
        return findAll(specification, pageRequest, graph);
    }
}