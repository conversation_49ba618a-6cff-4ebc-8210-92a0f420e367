package com.cloudbeds.fiscaldocument.repositories;

import com.cloudbeds.fiscaldocument.entity.DocumentConfig;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import jakarta.persistence.LockModeType;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Lock;

public interface DocumentConfigRepository extends JpaRepository<DocumentConfig, Long> {
    Optional<DocumentConfig> findByPropertyIdAndDocumentKind(Long propertyId, DocumentKind documentKind);

    @Lock(LockModeType.PESSIMISTIC_READ)
    DocumentConfig findWithLockByPropertyIdAndDocumentKind(Long propertyId, DocumentKind documentKind);

    @EntityGraph(attributePaths = {"documentContents"})
    List<DocumentConfig> findAllByPropertyIdIn(List<Long> propertyIds);
}
