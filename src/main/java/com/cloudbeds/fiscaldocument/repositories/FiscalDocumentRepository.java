package com.cloudbeds.fiscaldocument.repositories;

import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.DocumentStatus;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FiscalDocumentRepository extends JpaRepository<FiscalDocument, Long>,
    JpaSpecificationExecutor<FiscalDocument> {

    Optional<FiscalDocument> findByPropertyIdAndId(long propertyId, long fiscalDocumentId);

    /**
     * Checks if an invoice has been rectified by looking for rectifying invoices that link to it.
     *
     * @param invoiceId the ID of the invoice to check
     * @return true if the invoice has been rectified, false otherwise
     */
    @Query("""
        SELECT CASE WHEN COUNT(fd) > 0 THEN true ELSE false END
        FROM FiscalDocument fd
        JOIN fd.linkedDocuments ld
        WHERE ld.linkedDocumentId = :invoiceId
        AND fd.kind = :rectifyKind
        AND fd.status IN :activeStatuses
        """)
    boolean hasBeenRectified(@Param("invoiceId") Long invoiceId,
                           @Param("rectifyKind") DocumentKind rectifyKind,
                           @Param("activeStatuses") List<DocumentStatus> activeStatuses);

    /**
     * Finds the most recent rectifying invoice in a chain for a given original invoice.
     * This helps users identify which invoice they should rectify next.
     *
     * @param originalInvoiceId the ID of the original invoice
     * @return the most recent rectifying invoice in the chain, if any
     */
    @Query("""
        SELECT fd FROM FiscalDocument fd
        JOIN fd.linkedDocuments ld
        WHERE ld.linkedDocumentId = :originalInvoiceId
        AND fd.kind = :rectifyKind
        AND fd.status IN :activeStatuses
        ORDER BY fd.createdAt DESC
        """)
    Optional<FiscalDocument> findLatestRectifyingInvoice(@Param("originalInvoiceId") Long originalInvoiceId,
                                                       @Param("rectifyKind") DocumentKind rectifyKind,
                                                       @Param("activeStatuses") List<DocumentStatus> activeStatuses);
}
