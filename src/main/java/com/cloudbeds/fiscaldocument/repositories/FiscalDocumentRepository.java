package com.cloudbeds.fiscaldocument.repositories;

import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface FiscalDocumentRepository extends JpaRepository<FiscalDocument, Long>,
    JpaSpecificationExecutor<FiscalDocument> {

    Optional<FiscalDocument> findByPropertyIdAndId(long propertyId, long fiscalDocumentId);
}
