package com.cloudbeds.fiscaldocument.myfrontdesk;

import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.myfrontdesk.config.MfdSdkProperties;
import com.cloudbeds.fiscaldocument.myfrontdesk.exception.MfdResponseException;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.BasicResponse;
import com.cloudbeds.fiscaldocument.services.FiscalDocumentFileService;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class PrivateApiMfdService {
    private static final String POST_INVOICE_RESOURCE = "api/accounting/invoice";
    private final MfdSdkProperties sdkProperties;
    private final FiscalDocumentFileService fiscalDocumentFileService;
    private final RestTemplate restTemplate;

    @Value(value = "${mfd.api.user}")
    private String mfdApiUser;

    @Value(value = "${mfd.api.password}")
    private String mfdApiPassword;

    public PrivateApiMfdService(
        @Qualifier("MfdRestTemplate") RestTemplate restTemplate,
        MfdSdkProperties sdkProperties,
        FiscalDocumentFileService fiscalDocumentFileService
    ) {
        this.sdkProperties = sdkProperties;
        restTemplate.getMessageConverters().add(new FormHttpMessageConverter());
        this.restTemplate = restTemplate;
        this.fiscalDocumentFileService = fiscalDocumentFileService;

    }

    /**
     * Send a POST request to MFD to create an invoice / credit note.
     *
     * @param fiscalDocument fiscal document
     */
    public void postFiscalDocument(FiscalDocument fiscalDocument) {
        if (fiscalDocument.getSequenceNumber() == null) {
            return;
        }

        try {
            var headers = getAuthorizationHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<String, String>();
            params.add("id", Long.toString(fiscalDocument.getId()));
            params.add("propertyId", Long.toString(fiscalDocument.getPropertyId()));
            params.add("sourceId", fiscalDocument.getSourceId().toString());
            params.add("sourceKind", fiscalDocument.getSourceKind().toString());
            params.add("number", fiscalDocument.getSequenceNumber().toString());
            params.add("status", fiscalDocument.getStatus().toString());
            params.add("kind", fiscalDocument.getKind().toString());
            params.add("userId", fiscalDocument.getUserId() != null
                ? fiscalDocument.getUserId().toString()
                : null
            );
            params.add(
                "fileBase64",
                Base64.getEncoder().encodeToString(fiscalDocumentFileService.getContent(fiscalDocument))
            );
            params.add(
                "createdAt",
                fiscalDocument
                    .getCreatedAt()
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );

            if (
                fiscalDocument.getKind().equals(DocumentKind.CREDIT_NOTE)
                && !fiscalDocument.getLinkedDocuments().isEmpty()
            ) {
                params.add(
                    "parentId",
                    fiscalDocument.getLinkedDocuments().getFirst().getLinkedDocumentId().toString()
                );
            }

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(params, headers);

            var responseEntity = restTemplate.exchange(
                getApiUrl(POST_INVOICE_RESOURCE, fiscalDocument.getPropertyId()),
                HttpMethod.POST,
                entity,
                BasicResponse.class
            );

            var response = responseEntity.getBody();

            if (response == null) {
                throw new MfdResponseException("Empty response");
            }

            if (responseEntity.getStatusCode() != HttpStatus.OK
                || !response.getSuccess()
            ) {
                throw new MfdResponseException("Error message: " + response.getMessage());
            }
        } catch (Exception e) {
            log.error("MFD response error", e);
            throw new MfdResponseException(e.getMessage());
        }
    }

    private String getApiUrl(String resource, Long propertyId) {
        var apiUrl = sdkProperties.getApiUrl(propertyId);

        if (apiUrl.charAt(apiUrl.length() - 1) == '/') {
            return apiUrl + resource;
        }

        return apiUrl + '/' + resource;
    }

    private HttpHeaders getAuthorizationHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(this.mfdApiUser, this.mfdApiPassword);

        return headers;
    }
}
