package com.cloudbeds.fiscaldocument.myfrontdesk;

import com.cloudbeds.fiscaldocument.myfrontdesk.config.MfdSdkProperties;
import com.cloudbeds.fiscaldocument.myfrontdesk.exception.MfdResponseException;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.BasicResponse;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.GetBookingResponse;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.GetCurrenciesResponse;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.GetReservationRoomResponse;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.entity.CurrencySettingsDetails;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.entity.Reservation;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.entity.ReservationRoom;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Service
public class MyFrontDeskDataService {
    private static final String PROPERTY_ID_PARAM = "propertyID";
    private static final String RESERVATION_ID_PARAM = "reservationID";
    private static final String SUB_RESERVATION_ID_PARAM = "subReservationID";
    private static final String GET_RESERVATION = "api/v1.1/getReservation";

    private static final String GET_CURRENCIES = "api/v1.1/getCurrencySettings";
    private static final String GET_RESERVATION_ROOM_DETAILS = "api/v1.1/getReservationRoomDetails";
    private final MfdSdkProperties sdkProperties;
    private final RestTemplate restTemplate;

    public MyFrontDeskDataService(
            @Qualifier("MfdRestTemplate") RestTemplate restTemplate,
            MfdSdkProperties sdkProperties
    ) {
        this.sdkProperties = sdkProperties;
        this.restTemplate = restTemplate;
    }

    /**
     * Get reservation room details.
     *
     * @param propertyId propertyId
     * @param subReservationId subReservationId
     * @param accessToken accessToken
     * @return ReservationRoom
     */
    public ReservationRoom getReservationRoom(Long propertyId, String subReservationId, String accessToken) {
        var response = getRequest(
            GET_RESERVATION_ROOM_DETAILS,
            propertyId,
            accessToken,
            Map.of(PROPERTY_ID_PARAM, propertyId, SUB_RESERVATION_ID_PARAM, subReservationId),
            GetReservationRoomResponse.class
        );

        return response.getData();
    }

    /**
     * Pull booking from MFD.
     *
     * @param propertyId propertyId
     * @param reservationIdentifier reservationIdentifier
     * @param accessToken accessToken
     * @return Reservation
     */
    public Reservation getBooking(Long propertyId, String reservationIdentifier, String accessToken) {
        var response = getRequest(
            GET_RESERVATION,
            propertyId,
            accessToken,
            Map.of(PROPERTY_ID_PARAM, propertyId, RESERVATION_ID_PARAM, reservationIdentifier),
            GetBookingResponse.class
        );

        return response.getData();
    }

    /**
     * Get currencies.
     *
     * @param propertyId propertyId
     * @param accessToken accessToken
     * @return CurrencySettingsDetails
     */
    public CurrencySettingsDetails getCurrencies(Long propertyId, String accessToken) {
        var response = getRequest(
            GET_CURRENCIES,
            propertyId,
            accessToken,
            Map.of(PROPERTY_ID_PARAM, propertyId),
            GetCurrenciesResponse.class
        );

        return response.getData();
    }

    private <T extends BasicResponse> T getRequest(
            String resource,
            Long propertyId,
            String accessToken,
            Map<String, Object> params,
            Class<T> responseType
    ) {
        try {
            var responseEntity = restTemplate.exchange(
                    buildApiUrl(resource, propertyId, params),
                    HttpMethod.GET,
                    new HttpEntity<>(prepareHeaders(accessToken)),
                    responseType
            );

            var response = responseEntity.getBody();

            if (response == null) {
                throw new MfdResponseException("Empty response");
            }

            if (
                    responseEntity.getStatusCode() != HttpStatus.OK
                            || !response.getSuccess()
            ) {
                throw new MfdResponseException(
                        "Error message: " + response.getMessage()
                );
            }

            return response;
        } catch (Exception e) {
            log.error("MFD response error", e);
            throw new MfdResponseException(e.getMessage());
        }
    }

    private String buildApiUrl(String resource, Long propertyId, Map<String, Object> params) {
        var builder =  UriComponentsBuilder
                .fromUriString(getApiUrl(resource, propertyId));

        params.forEach(builder::queryParam);

        return builder
                .build()
                .toString();
    }

    private String getApiUrl(String resource, Long propertyId) {
        var apiUrl = sdkProperties.getApiUrl(propertyId);

        if (apiUrl.charAt(apiUrl.length() - 1) == '/') {
            return apiUrl + resource;
        }

        return apiUrl + '/' + resource;
    }

    private HttpHeaders prepareHeaders(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken);

        return headers;
    }
}
