package com.cloudbeds.fiscaldocument.myfrontdesk.response.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CurrencySettingsDetails {

    @JsonProperty("default")
    private String defaultCurrency;
    private CurrencyFormat format;
    private List<String> acceptable = new ArrayList<>();
    private RatesDetails rates;

    @Getter
    @Setter
    public static class CurrencyFormat {
        private String decimal;
        private String thousand;
    }

    @Getter
    @Setter
    public static class RatesDetails {
        private List<CurrencyDetails> fixed;
    }

    @Getter
    @Setter
    public static class CurrencyDetails {
        private String currency;
        private Double rate;
    }
}
