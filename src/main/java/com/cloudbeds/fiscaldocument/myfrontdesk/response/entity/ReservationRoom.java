package com.cloudbeds.fiscaldocument.myfrontdesk.response.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ReservationRoom {
    @JsonProperty("reservationID")
    private String reservationId;
    @JsonProperty("subReservationID")
    private String subReservationId;
    @JsonProperty("roomID")
    private String roomId;
    private String roomName;
    @JsonProperty("guestID")
    private Long guestId;
    private String guestName;
    @JsonProperty("reservationRoomID")
    private Long reservationRoomId;
    private String roomStatus;
    @JsonProperty("roomTypeID")
    private String roomTypeId;
    private String roomTypeName;
    private Boolean roomTypeIsVirtual;
    private Integer maxGuests;
    private Integer adults;
    private Integer children;
}
