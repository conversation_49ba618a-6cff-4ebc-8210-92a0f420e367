package com.cloudbeds.fiscaldocument.myfrontdesk.response.entity;


import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Guest {
    @JsonProperty("guestID")
    private Long guestId;
    private String guestFirstName;
    private String guestLastName;
    private String guestGender;
    private String guestEmail;
    private String guestPhone;
    private String guestCellPhone;
    private String guestAddress;
    private String guestAddress2;
    private String guestCity;
    private String guestState;
    private String guestCountry;
    private String guestZip;
    private String guestStatus;
    private LocalDateTime guestBirthdate;
    private String guestDocumentType;
    private String guestDocumentNumber;
    private LocalDateTime guestDocumentIssueDate;
    private String guestDocumentIssuingCountry;
    private LocalDateTime guestDocumentExpirationDate;
    @JsonProperty("taxID")
    private String taxId;
    @JsonProperty("companyTaxID")
    private String companyTaxId;
    private String companyName;
    private Boolean isAnonymized;
    @JsonProperty("roomID")
    private String roomId;
    private String roomName;
    private String roomTypeName;
    private Boolean roomTypeIsVirtual;
    private Boolean isMainGuest;
    private List<Reservation.CustomField> customFields;

}