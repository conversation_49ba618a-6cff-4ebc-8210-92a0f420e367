package com.cloudbeds.fiscaldocument.myfrontdesk.response.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class Reservation {
    @JsonProperty("propertyID")
    private String propertyId;
    private String guestName;
    private String guestEmail;
    private boolean isAnonymized;
    private Map<Long, Guest> guestList = new HashMap<>();
    @JsonProperty("reservationID")
    private String reservationId;
    private LocalDateTime dateCreated;
    private LocalDateTime dateModified;
    private String estimatedArrivalTime;
    private String source;
    @JsonProperty("sourceID")
    private String sourceId;
    private String thirdPartyIdentifier;
    private String status;
    private List<AssignedRoom> assigned = new ArrayList<>();
    private float total;
    private float balance;
    private List<CreditCard> cardsOnFile = new ArrayList<>();
    private List<CustomField> customFields = new ArrayList<>();
    private LocalDate startDate;
    private LocalDate endDate;
    private String allotmentBlockCode;
    private boolean channelProvidedCreditCard;
    private List<GroupInventory> groupInventory;

    /**
     * Get the main guest of the reservation.
     *
     * @return the main guest
     */
    public Guest getMainGuest() {
        for (Guest guest : guestList.values()) {
            if (guest.getIsMainGuest()) {
                return guest;
            }
        }
        return null;
    }


    @Getter
    @Setter
    static class DailyRate {
        private LocalDateTime date;
        private float rate;
    }

    @Getter
    @Setter
    static class CreditCard {

        @JsonProperty("cardID")
        private Long cardId;
        private String cardNumber;
        private String cardType;
    }

    @Getter
    @Setter
    public static class CustomField {
        private String customFieldName;
        private String customFieldValue;

    }

    @Getter
    @Setter
    static class GroupInventory {
        @JsonProperty("subReservationID")
        private String subReservationId;
        private String allotmentBlockCode;
        private LocalDateTime startDate;
        private LocalDateTime endDate;

    }
}

