package com.cloudbeds.fiscaldocument.myfrontdesk.config;

import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Getter
@Setter
@Configuration
public class MfdSdkProperties {
    private final RestTemplate restTemplate;

    @Value("${cloudbeds.mappings-service-url}")
    private String mappingsServiceUrl;

    public MfdSdkProperties(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * Get API url for property.
     *
     * @param mfdPropertyId MFD Property ID
     * @return URL
     */
    public String getApiUrl(Long mfdPropertyId) {
        String urlTemplate = UriComponentsBuilder
            .fromHttpUrl(mappingsServiceUrl)
            .path("/v1.0/mfd/property")
            .toUriString() + "?id={id}";

        try {
            var response = restTemplate.exchange(
                urlTemplate,
                HttpMethod.GET,
                new HttpEntity<>(new HttpHeaders()),
                MappingServiceResponse.class,
                Map.of("id", mfdPropertyId)
            );

            if (response.getBody() == null) {
                throw new FiscalDocumentException(ErrorCode.UNEXPECTED_ERROR, "Empty response");
            }

            return response.getBody().getIslandApiBaseUrl();
        } catch (RestClientException ex) {
            log.warn("Multi island mapping service request failed: {}", ex.getMessage());
            throw new FiscalDocumentException(ErrorCode.UNEXPECTED_ERROR, ex.getMessage());
        }
    }
}
