package com.cloudbeds.fiscaldocument.myfrontdesk.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class MappingServiceResponse {
    @JsonProperty("island_id")
    private String islandId;

    @JsonProperty("island_mfd_base_url")
    private String islandMfdBaseUrl;

    @JsonProperty("island_api_base_url")
    private String islandApiBaseUrl;

    @JsonProperty("island_crm_base_url")
    private String islandCrmBaseUrl;
}
