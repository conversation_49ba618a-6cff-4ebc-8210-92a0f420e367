package com.cloudbeds.fiscaldocument;

import com.cloudbeds.fiscaldocument.support.security.CustomRequireAuthenticationConfig;
import com.cloudbeds.shared.api.ResponseExceptionHandler;
import com.cloudbeds.shared.api.ScopePermissionEvaluator;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@EntityScan(basePackages = "com.cloudbeds")
@EnableJpaRepositories
@ConfigurationPropertiesScan
@Configuration
@EnableAutoConfiguration
@ComponentScan(
    basePackages = {
        "com.cloudbeds.fiscaldocument",
        "com.cloudbeds.dynamicquerying"
    },
    basePackageClasses = {
        ResponseExceptionHandler.class,
        ScopePermissionEvaluator.class,
        CustomRequireAuthenticationConfig.class
    }
)
@SuppressWarnings({"PMD.ClassWithOnlyPrivateConstructorsShouldBeFinal", "PMD.UseUtilityClass"})
public class FiscalDocumentApplication {
    public static void main(String[] args) {
        SpringApplication.run(FiscalDocumentApplication.class, args);
    }
}
