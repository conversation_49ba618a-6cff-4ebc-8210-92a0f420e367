package com.cloudbeds.fiscaldocument.models;

import com.cloudbeds.fiscaldocument.support.exception.ErrorCode;
import com.cloudbeds.fiscaldocument.support.exception.FiscalDocumentException;
import com.cloudbeds.fiscaldocument.support.localization.LocalizationUtil;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class InvoiceTemplateKeys {

    private static final List<String> SPECIAL_FONT_LANG = List.of("zh", "zho", "ko", "kor", "ja",
        "jpn", "ii", "iii", "lis");

    private String lang;
    private String fontFamily;
    private String invoiceImg;
    private String invoiceTitle;
    private String hotelName;
    private String legalCompanyName;
    private String hotelAddress;
    private String hotelCity;
    private String hotelState;
    private String hotelZip;
    private String hotelPhone;
    private String hotelEmail;
    private String hotelCountry;
    private String taxId1;
    private String taxId2;
    private String reservationFirstName;
    private String reservationLastName;
    private String reservationNumberLabel;
    private String reservationNumber;
    private String invoiceNumberLabel;
    private String invoiceNumber;
    private String invoiceDateLabel;
    private String invoiceDate;
    private LocalDate invoiceDateNotFormatted;
    private String dueDateLabel;
    private String dueDate;
    private String cpfLabel;
    private String cpf;
    private String transactionsTotalLabel;
    private String transactionTotal;
    private String resCreateTaxesLabel;
    private String resCreateTaxes;
    private String resCreateFeesLabel;
    private String resCreateFees;
    private String companyName;
    private String occupantTaxIdNumberLabel;
    private String occupantTaxIdNumber;
    private String occupantNameLabel;
    private String occupantName;
    private String addressLabel;
    private String address;
    private String address2Label;
    private String address2;
    private String cityLabel;
    private String city;
    private String countryNameLabel;
    private String countryName;
    private String stateLabel;
    private String state;
    private String zipLabel;
    private String zip;
    private String pdfEmailLabel;
    private String pdfEmail;
    private String phoneLabel;
    private String phone;
    private String cellPhoneLabel;
    private String cellPhone;
    private List<CustomField> customFields = new ArrayList<>();
    private List<TaxFeeDetails> specificTaxes = new ArrayList<>();
    private List<TaxFeeDetails> specificFees = new ArrayList<>();
    private String remainingAmountLabel;
    private String grandTotalLabel;
    private String grandTotal;
    private String defaultCurrency;
    private String balanceDue;
    private List<ForeignCurrency> foreignCurrencies = new ArrayList<>();
    private String checkInLabel;
    private String checkOutLabel;
    private String nightsLabel;
    private String reservationDateLabel;
    private String checkIn;
    private String checkOut;
    private String nights;
    private String reservationDate;
    private String resIdLabel;
    private String transactionDateTimeLabel;
    private String transactionNameLabel;
    private boolean hasRoomNumber;
    private String resRoomLabel;
    private String descriptionResLabel;
    private String qtyLabel;
    private String transactionCreditLabel;
    private String transactionDebitLabel;
    private List<TransactionModel> transactions = new ArrayList<>();
    private String transactionTableColSpan;
    private String creditColour;
    private String debitColour;
    private String transactionsTotalCredit;
    private String transactionsTotalDebit;
    private String customText;
    private String qrCodeFilename;
    private String groupName;
    private String groupCode;
    private String workPhoneLabel;
    private String faxLabel;
    private String workPhone;
    private String fax;
    private String creditNoteLabel;
    private String creditNoteNumber;
    private String creditNoteDateLabel;
    private String creditNoteDate;
    private String creditNoteReasonLabel;
    private String creditNoteReason;
    private String originalInvoiceNumberLabel;
    private String originalInvoiceDateLabel;
    private String transactionAmountLabel;
    private String transactionVatLabel;
    private String officialId;
    private String governmentIntegrationUrl;
    private String transactionsTotalAmount;
    private String transactionsTotalVat;
    private String rectifyInvoiceNumber;
    private String rectifyInvoiceLabel;

    public InvoiceTemplateKeys(String lang) {
        if (!SPECIAL_FONT_LANG.contains(lang)) {
            this.fontFamily = "font-family: Arial, Helvetica, sans-serif;";
        } else {
            this.fontFamily = "";
        }

        this.lang = lang;
        this.reservationNumberLabel = LocalizationUtil.getTranslation("RES_reservation_number", lang);
        this.invoiceNumberLabel = LocalizationUtil.getTranslation("invoice_number_pdf", lang);
        this.invoiceDateLabel = LocalizationUtil.getTranslation("invoice_date_pdf", lang);
        this.dueDateLabel = LocalizationUtil.getTranslation("invoice_due_pdf", lang);
        this.cpfLabel = LocalizationUtil.getTranslation("invoice_CNPJ", lang);
        this.transactionsTotalLabel = LocalizationUtil.getTranslation("invoice_transactions_total", lang);
        this.resCreateTaxesLabel = LocalizationUtil.getTranslation("ResCreate_taxes", lang);
        this.resCreateFeesLabel = LocalizationUtil.getTranslation("ResCreate_fees", lang);
        this.occupantTaxIdNumberLabel = LocalizationUtil.getTranslation("ResCreate_document_number", lang);
        this.occupantNameLabel = LocalizationUtil.getTranslation("invoice_pdf_company_name", lang);
        this.addressLabel = LocalizationUtil.getTranslation("address", lang);
        this.address2Label = LocalizationUtil.getTranslation("address2", lang);
        this.cityLabel = LocalizationUtil.getTranslation("invoice_pdf_city", lang);
        this.countryNameLabel = LocalizationUtil.getTranslation("invoice_pdf_country_name", lang);
        this.stateLabel = LocalizationUtil.getTranslation("invoice_pdf_state", lang);
        this.zipLabel = LocalizationUtil.getTranslation("invoice_pdf_zip", lang);
        this.checkInLabel = LocalizationUtil.getTranslation("invoice_pdf_checkin", lang);
        this.checkOutLabel = LocalizationUtil.getTranslation("invoice_pdf_checkout", lang);
        this.nightsLabel = LocalizationUtil.getTranslation("invoice_pdf_nights", lang);
        this.reservationDateLabel = LocalizationUtil.getTranslation("invoice_pdf_reservation_date", lang);
        this.resIdLabel = LocalizationUtil.getTranslation("invoice_pdf_RES_id", lang);
        this.transactionDateTimeLabel = LocalizationUtil.getTranslation(
            "invoice_pdf_Report_transactions_datetime",
            lang
        );
        this.transactionNameLabel = LocalizationUtil.getTranslation("invoice_pdf_Report_transactions_name", lang);
        this.descriptionResLabel = LocalizationUtil.getTranslation("invoice_pdf_RES_description_res", lang);
        this.qtyLabel = LocalizationUtil.getTranslation("ResCreate_qty", lang);
        this.transactionCreditLabel = LocalizationUtil.getTranslation("invoice_pdf_Report_transactions_credit", lang);
        this.transactionDebitLabel = LocalizationUtil.getTranslation("invoice_pdf_Report_transactions_debit", lang);
        this.grandTotalLabel = LocalizationUtil.getTranslation("invoice_res_gtotal", lang);
        this.resRoomLabel = LocalizationUtil.getTranslation("invoice_pdf_RES_room", lang);
        this.remainingAmountLabel = LocalizationUtil.getTranslation("invoice_pdf_remaining_amount", lang);
        this.cellPhoneLabel = LocalizationUtil.getTranslation("invoice_pdf_cell_phone", lang);
        this.phoneLabel = LocalizationUtil.getTranslation("invoice_pdf_phone", lang);
        this.pdfEmailLabel = LocalizationUtil.getTranslation("invoice_pdf_email", lang);
        this.faxLabel = LocalizationUtil.getTranslation("invoice_pdf_fax", lang);
        this.workPhoneLabel = LocalizationUtil.getTranslation("invoice_pdf_work_phone", lang);

        this.creditNoteLabel = LocalizationUtil.getTranslation("invoicing/creditNoteNumber", lang);
        this.creditNoteDateLabel = LocalizationUtil.getTranslation("invoicing/creditNoteDate", lang);
        this.creditNoteReasonLabel = LocalizationUtil.getTranslation("invoicing/creditReason", lang);
        this.originalInvoiceNumberLabel = LocalizationUtil.getTranslation("invoicing/originalInvoiceNumber", lang);
        this.originalInvoiceDateLabel = LocalizationUtil.getTranslation("invoicing/originalInvoiceDate", lang);
        this.transactionAmountLabel = LocalizationUtil.getTranslation("invoicing/transactionAmount", lang);
        this.transactionVatLabel = LocalizationUtil.getTranslation("invoicing/transactionVat", lang);
        this.rectifyInvoiceLabel = LocalizationUtil.getTranslation("invoicing/rectifyInvoice", lang);
    }

    /**
     * Set the total credit amount for the transactions.
     *
     * @param transactionCredit the total credit amount
     */
    public void setTransactionsTotalCredit(String transactionCredit) {
        this.transactionsTotalCredit = String.valueOf(transactionCredit);
        creditColour = "black";
        if (transactionsTotalDebit == null) {
            debitColour = "red";
        }
    }

    /**
     * Set the total debit amount for the transactions.
     *
     * @param transactionDebit the total debit amount
     */
    public void setTransactionsTotalDebit(String transactionDebit) {
        this.transactionsTotalDebit = String.valueOf(transactionDebit);
        debitColour = "black";
        if (transactionsTotalCredit == null) {
            creditColour = "red";
        }
    }


    public void setHasRoomNumber(boolean hasRoomNumber) {
        this.hasRoomNumber = hasRoomNumber;
        transactionTableColSpan = hasRoomNumber ? "6" : "5";
    }

    /**
     * Get map with all keys.
     *
     * @return map with all keys
     */
    public Map<String, Object> toMap() {
        Map<String, Object> templateKeys = new HashMap<>();
        try {
            for (Field field : this.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                Object value = field.get(this);
                if (!(value instanceof String) || !((String) value).isEmpty()) {
                    templateKeys.put(field.getName(), value);
                }
            }
        } catch (IllegalAccessException e) {
            throw new FiscalDocumentException(ErrorCode.UNEXPECTED_ERROR, "Error accessing fields", e);
        }
        return templateKeys;
    }
}
