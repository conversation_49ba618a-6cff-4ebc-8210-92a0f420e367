package com.cloudbeds.fiscaldocument.models;

import com.cloudbeds.fiscaldocument.entity.FiscalDocumentRecipient;
import com.cloudbeds.fiscaldocument.enums.DocumentKind;
import com.cloudbeds.fiscaldocument.enums.SourceKind;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FiscalDocumentCdcModel {
    private Long id;
    private Long propertyId;
    private Long number;
    private Long sourceId;
    private SourceKind sourceKind;
    private DocumentKind documentKind;
    private Long externalSettingsId;
    private Instant date;
    private String pfdUrl;
    private CdcDocumentStatus status;
    private String currency;
    private Long amount;
    private Long balance;
    private List<String> transactionIds = new ArrayList<>();
    private List<FiscalDocumentRecipient> recipients = new ArrayList<>();

    public FiscalDocumentGroupKey getGroupKey() {
        return new FiscalDocumentGroupKey(propertyId, id.toString(), documentKind);
    }

    @Getter
    public enum CdcDocumentStatus {
        OPEN("open"),
        PAID("paid"),
        VOIDED("voided"),
        REQUESTED("requested"),
        VOID_REQUESTED("void_requested"),
        FAILED("failed"),
        MANUALLY_RECONCILED("manually_reconciled"),
        CANCELED("canceled"),
        REJECTED("rejected");

        private final String value;

        CdcDocumentStatus(String value) {
            this.value = value;
        }

        /**
         * Converts a string to a CdcDocumentStatus enum.
         *
         * @param value the string value
         * @return the corresponding CdcDocumentStatus enum
         * @throws IllegalArgumentException if the value does not match any enum
         */
        public static CdcDocumentStatus fromString(String value) {
            for (CdcDocumentStatus status : CdcDocumentStatus.values()) {
                if (status.value.equalsIgnoreCase(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status: " + value);
        }

    }
}
