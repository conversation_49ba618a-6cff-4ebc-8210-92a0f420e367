package com.cloudbeds.fiscaldocument.models;

import com.cloudbeds.fiscaldocument.entity.DocumentSequence;
import com.cloudbeds.fiscaldocument.entity.FiscalDocument;
import java.util.HashSet;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FiscalDocumentResult {
    Set<DocumentSequence> sequences = new HashSet<>();
    Set<FiscalDocument> documents = new HashSet<>();

    public void addResult(FiscalDocumentResult result) {
        this.sequences.addAll(result.getSequences());
        this.documents.addAll(result.getDocuments());
    }
}
