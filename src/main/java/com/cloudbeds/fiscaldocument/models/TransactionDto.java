package com.cloudbeds.fiscaldocument.models;

import com.cloudbeds.accounting.v1.InternalCode;
import com.cloudbeds.accounting.v1.Source;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TransactionDto {
    private Long id;
    private Source source;
    private Long sourceId;
    private InternalCode internalCode;
    private String description;
    private Long subSourceId;
    private Long routedFrom;
    private Instant transactionDatetimePropertyTime;
    private BigDecimal amount;
    private BigInteger amountMinorUnits;
    private String currency;
    private int quantity;
    private Long rootId;
    private int currencyScale;
    private Long parentId;


    public boolean hasRoutedFrom() {
        return routedFrom != null && routedFrom > 0;
    }

}
