package com.cloudbeds.fiscaldocument.models;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TransactionModel {
    private String transactionReservationIdentifier = "";
    private String transactionDateTime = "";
    private String transactionName = "";
    private String transactionRoomName = "";
    private String transactionDescription = "";
    private String transactionQty = "-";
    private String transactionCreditColour = "black";
    private String transactionDebitColour = "black";
    private String transactionCredit = "-";
    private String transactionAmount = "-";
    private String transactionVat = "-";
    private String transactionDebit = "-";
    private String transactionNights = "-";

    /**
     * Set the transaction credit.
     *
     * @param transactionCredit transaction credit formatted
     * @param isNegative is negative amount
     */
    public void setTransactionCredit(String transactionCredit, boolean isNegative) {
        this.transactionCredit = transactionCredit;
        if (isNegative) {
            transactionDebitColour = "red";
        }
    }

    /**
     * Set the transaction debit.
     *
     * @param transactionDebit transaction debit formatted
     * @param isNegative is negative amount
     */
    public void setTransactionDebit(String transactionDebit, boolean isNegative) {
        this.transactionDebit = transactionDebit;
        if (isNegative) {
            transactionCreditColour = "red";
        }
    }
}
