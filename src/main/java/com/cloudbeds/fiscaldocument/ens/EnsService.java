package com.cloudbeds.fiscaldocument.ens;

import com.cloudbeds.fiscaldocument.ens.events.FiscalDocumentEnsEvent;
import com.cloudbeds.fiscaldocument.myfrontdesk.response.BasicResponse;
import com.cloudbeds.fiscaldocument.support.features.FeatureFlags;
import com.cloudbeds.fiscaldocument.support.features.ProviderInterface;
import com.cloudbeds.fiscaldocument.utils.DateTimeService;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Getter
@Component
public class EnsService {
    private final RestTemplate restTemplate;

    @Value(value = "${ens.url}")
    private String ensUrl;

    private final ProviderInterface providerInterface;

    private final DateTimeService dateTimeService;

    public EnsService(
        @Qualifier("MfdRestTemplate") RestTemplate restTemplate,
        ProviderInterface providerInterface,
        DateTimeService dateTimeService
    ) {
        this.restTemplate = restTemplate;
        this.providerInterface = providerInterface;
        this.dateTimeService = dateTimeService;
    }

    /**
     * Sending Fiscal Documents ENS events for a property.
     *
     * @param propertyId ID of property.
     * @param events List of events to send
     */
    public void sendFiscalDocumentEvents(Long propertyId, List<FiscalDocumentEnsEvent> events) {
        if (events == null || events.isEmpty()) {
            return;
        }

        if (!providerInterface.evaluatePropertyFlag(propertyId, FeatureFlags.ENS_ENABLED)) {
            return;
        }

        try {
            var eventsToSend = new ArrayList<HashMap<String, Object>>();
            for (FiscalDocumentEnsEvent event : events) {
                var data = new HashMap<String, String>();
                var keys = new ArrayList<String>();

                keys.add("property/" + propertyId);

                data.put("propertyId", event.getPropertyId());
                data.put("documentKind", event.getDocumentKind().toString());
                data.put("id", event.getFiscalDocumentId());
                data.put("event", event.getEventType().getValue());

                var timestamp = Timestamp.valueOf(dateTimeService.getCurrentDatetime());
                var eventToSend = new HashMap<String, Object>();
                eventToSend.put("keys", keys);
                eventToSend.put("event", "fiscal_document/" + event.getEventType().getValue());

                eventToSend.put("timestamp", timestamp.toInstant().getEpochSecond());
                eventToSend.put("data", data);
                eventsToSend.add(eventToSend);
            }

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            var entity = new HttpEntity<>(eventsToSend, headers);

            if (ensUrl.charAt(ensUrl.length() - 1) != '/') {
                ensUrl += "/";
            }

            var ensUrl = this.ensUrl + "events";

            var responseEntity = restTemplate.exchange(
                ensUrl,
                HttpMethod.POST,
                entity,
                BasicResponse.class
            );
            if (
                !List.of(HttpStatus.OK, HttpStatus.CREATED, HttpStatus.ACCEPTED)
                    .contains(responseEntity.getStatusCode())
            ) {
                throw new RuntimeException("HTTP status: " + responseEntity.getStatusCode());
            }
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage(), ex);
        }
    }
}
