package com.cloudbeds.fiscaldocument.ens.events;

import com.cloudbeds.FiscalDocumentService.FiscalDocumentKind;
import com.cloudbeds.fiscaldocument.ens.enums.FiscalDocumentEnsEventType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@NoArgsConstructor
public class FiscalDocumentEnsEvent {
    private String fiscalDocumentId;
    private FiscalDocumentKind documentKind;
    private String propertyId;
    private FiscalDocumentEnsEventType eventType;
}
