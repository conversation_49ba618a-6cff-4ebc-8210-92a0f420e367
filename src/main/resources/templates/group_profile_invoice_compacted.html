<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <style>
            table{
                width:100%;
                border-collapse: collapse;
            }
            table tr th, table tr td{
                text-align: left;
                font-family: Arial, Helvetica, sans-serif;
                font-size:10px;
                color:#000;
            }
            .margin_right, .invoice_data td {
                padding-right: 35px;
            }
            table.invoice_data tr th, table.reservation_data tr th {
                color: #363636;
                font-weight:normal;
            }
            table.table_reservation tr th {
                text-transform: uppercase;
                font-weight: bold;
                padding-right: 25px;
                padding-bottom:5px;
                font-size:9px;
            }
            table.table_reservation tr td {
                padding:5px 8px 5px 0;
                font-size:9px;
                margin-bottom:0;
            }
            tr.bold_transaction td {
                border-bottom: 1px solid #efefef;
            }
            tr.bold_transaction_last td {
                border-bottom: 1px solid #6B6B6B;
            }
            table.table_reservation tr.total td{
                border-bottom: 1px solid #6B6B6B;
                padding-bottom:5px;
                padding-top:5px;
            }
            table.customer_info_table tr .col-name {
                color:#363636;
            }
            table.customer_info_table tr .col-name-email {
                vertical-align: top;
            }
            .cust_email {
                margin-right: 30px;
            }
            .customer_info_table .col-name {
                padding-right: 5px;
            }
            .customer_info_table td {
                padding-bottom: 6px;
            }
            .reservation_data td, .reservation_data th {
                padding-right: 25px;
            }
            .total_info tr td{
                border-top: 1px solid #6B6B6B;
                border-bottom: 1px solid #6B6B6B;
                padding:10px 4px;
            }
            table.customer_info_table tr td {
                padding:5px 4px;
            }

            .total_info tr td.r_border{
                border-right: 1px solid #6B6B6B;
            }
            .invoice_data tr td{
                border:none;
                padding:0;
            }
            .nowrap {
                white-space: nowrap;
            }
            .lower_case {
                text-transform: lowercase;
            }
        </style>
    </head>
    <body>
        <div class="hotel_address">
            <table style="width: 100%;">
                <tr>
                    <td class="customer">
                        {{#invoiceImg}}
                        <img height="100" src="{{invoiceImg}}" alt=""/>
                        {{/invoiceImg}}
                        <br/>
                        <div style="font-size: 42px; text-transform: uppercase">
                            {{invoiceTitle}}
                        </div>
                    </td>
                    <td class="hotel-info" style="float:right;text-align: right;vertical-align: top;">
                        <p class="hotel_name">{{hotelName}}</p>
                        {{#legalCompanyName}}
                        <p class="hotel_name">{{legalCompanyName}}</p>
                        {{/legalCompanyName}}
                        <p class="street">{{#hotelAddress}}{{hotelAddress}}{{/hotelAddress}}</p>
                        <p class="city">{{#hotelCity}}{{hotelCity}}{{/hotelCity}} {{#hotelState}}{{hotelState}}{{/hotelState}} {{#hotelZip}}{{hotelZip}}{{/hotelZip}}</p>
                        <p class="phone">{{#hotelPhone}}{{hotelPhone}}{{/hotelPhone}}</p>
                        <p class="email">{{#hotelEmail}}{{hotelEmail}}{{/hotelEmail}}</p>
        <!--                <?php-->
        <!--                if ($property_id == '2582') {-->
        <!--                    if (!empty($invoice_setup['full_tax_id'])) {-->
        <!--                        echo '<p>' . $invoice_setup['full_tax_id'] . '</p>';-->
        <!--                }-->
        <!--                }-->
        <!--                ?>-->
                        <p class="country">{{#hotelCountry}}{{hotelCountry}}{{/hotelCountry}}</p>

                    </td>
                </tr>
            </table>
        </div>
        <br/>
        <div style="font-weight: 500; font-size: 24px;">{{groupName}}</div>
        <div style="font-weight: 500; font-size: 24px;">{{groupCode}}</div>
        <br/>

        <div style="clear:both"></div>

        <table class="total_info">
            <tbody>
                <tr>
                    <td class="r_border" style="width:60%">
                        <table class="invoice_data">
                            <thead>
                                <tr>
                                    <th class="margin_right" style="width:25%">{{invoiceNumberLabel}}</th>
                                    {{#fullTaxId}}
                                        <th class="margin_right" style="width:25%">{{invoiceFullTaxIdLabel}}</th>
                                    {{/fullTaxId}}
                                    <th class="margin_right" style="width:25%">{{invoiceDateLabel}}</th>
                                    <th class="margin_right" style="width:25%">
                                        {{#cpf}}
                                            {{cpfLabel}}
                                        {{/cpf}}
                                    </th>
                                </tr>
                            </thead>
                           <tbody>
                                <tr>
                                    <td>
                                        {{invoiceNumber}}
                                    </td>
                                    {{#fullTaxId}}
                                        <td>
                                            {{fullTaxId}}
                                        </td>
                                    {{/fullTaxId}}
                                    <td>
                                        {{invoiceDate}}
                                    </td>
                                    {{#cpf}}
                                        <td>
                                            <span data-hook="invoice-cpf">{{cpf}}</span>
                                        </td>
                                    {{/cpf}}
                                </tr>
                           </tbody>
                        </table>
                    </td>
                    <td style="width:40%">
                        <table class="invoice_data" style="width:100%;">
                            <tbody>
                            <tr>
                                <td>{{transactionsTotalLabel}}</td>
                                <td>{{transactionTotal}}</td>
                            </tr>
                            <tr>
                                <td>{{resCreateTaxesLabel}}</td>
                                <td>{{resCreateTaxes}}</td>
                            </tr>
                            {{#specificTaxes}}
                                <tr>
                                    <td  style="padding-left: 10px;">{{name}}</td>
                                    <td >{{value}}</td>
                                </tr>
                            {{/specificTaxes}}
                            <tr>
                                <td>{{resCreateFeesLabel}}</td>
                                <td>{{resCreateFees}}</td>
                            </tr>
                            {{#specificFees}}
                                <tr>
                                    <td style="padding-left: 10px;">{{name}}</td>
                                    <td>{{value}}</td>
                                </tr>
                            {{/specificFees}}
                            <tr>
                                <td>{{grandTotalLabel}}</td>
                                <td>{{grandTotal}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        <table cellspacing="0" cellpadding="0" border="0" style="width: 100%;" class="customer_info_table">
            <tr>
                <td style="width: 30%;" valign="top">
                    <table cellspacing="0" cellpadding="0" border="0">
                        {{#pdfEmail}}
                            <tr>
                                <td class="col-name">{{pdfEmailLabel}}</td>
                                <td><span class="cust_email" data-hook="guest-email">{{pdfEmail}}</span></td>
                            </tr>
                        {{/pdfEmail}}
                        {{#workPhone}}
                            <tr>
                                <td class="col-name nowrap">{{phoneLabel}}
                                    <span class="lower_case">({{workPhoneLabel}})</span>
                                </td>
                                <td><span class="cust_phone">{{workPhone}}</span></td>
                            </tr>
                        {{/workPhone}}
                        {{#cellPhone}}
                            <tr>
                                <td class="col-name nowrap">{{phoneLabel}}
                                    <span class="lower_case">({{cellPhoneLabel}})</span>
                                </td>
                                <td><span class="cust_cellphone" data-hook="guest-cellphone">{{cellPhone}}</span></td>
                            </tr>
                        {{/cellPhone}}
                        {{#fax}}
                            <tr>
                                <td class="col-name">{{faxLabel}}</td>
                                <td><span class="cust_fax">{{fax}}</span></td>
                            </tr>
                        {{/fax}}
                        {{#address}}
                        <tr>
                            <td class="col-name">{{addressLabel}}</td>
                            <td>
                                <span class="cust_address1" data-hook="guest-address1">{{address}}</span>
                            </td>
                        </tr>
                        {{/address}}

                        {{#address2}}
                        <tr>
                            <td class="col-name">{{address2Label}}</td>
                            <td>
                                <span class="cust_address2" data-hook="guest-address2">{{address2}}</span>
                            </td>
                        </tr>
                        {{/address2}}
                        {{#city}}
                        <tr>
                            <td class="col-name">{{cityLabel}}</td>
                            <td>
                                <span class="cust_city" data-hook="guest-city">{{city}}</span>
                            </td>
                        </tr>
                        {{/city}}
                    </table>
                </td>
                <td style="width: 30%;" valign="top">
                    <table cellspacing="0" cellpadding="0" border="0">
                        {{#countryName}}
                        <tr>
                            <td class="col-name">{{countryNameLabel}}</td>
                            <td>
                                <span class="cust_state" data-hook="guest-country">{{countryName}}</span>
                            </td>
                        </tr>
                        {{/countryName}}
                        {{#state}}
                        <tr>
                            <td class="col-name">{{stateLabel}}</td>
                            <td>
                                <span class="cust_state" data-hook="guest-state">{{state}}</span>
                            </td>
                        </tr>
                        {{/state}}
                        {{#zip}}
                        <tr>
                            <td class="col-name">{{zipLabel}}</td>
                            <td>
                                <span class="cust_zip" data-hook="guest-zip">{{zip}}</span>
                            </td>
                        </tr>
                        {{/zip}}
                    </table>
                </td>
                <td style="width: 40%;border-left: 1px solid #6B6B6B; background-color:#DCDCDC " valign="middle" align="center">
                    <table cellspacing="0" cellpadding="0" border="0">
                        <thead>
                        <tr>
                            <th style="width:33%;">{{remainingAmountLabel}}</th>
                            {{#dueDate}}
                                <th style="width:33%;">{{dueDateLabel}}</th>
                            {{/dueDate}}
                            <th style="width:33%;"></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>{{balanceDue}}</td>
                            {{#dueDate}}
                                <td>{{dueDate}}</td>
                            {{/dueDate}}
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </table>

        <hr/>
        <br/>

        <table style="width:100%;" class="table_reservation" border="0"  cellspacing="0" cellpadding="0" valign="middle">
            <thead>
                <tr>
                    <th>{{resIdLabel}}</th>
                    <th>{{transactionDateTimeLabel}}</th>
                    <th>{{transactionNameLabel}}</th>
                    {{#hasRoomNumber}}
                        <th>{{resRoomLabel}}</th>
                    {{/hasRoomNumber}}
                    <th>{{descriptionResLabel}}</th>
                    <th>{{nightsLabel}}</th>
                    <th>{{transactionCreditLabel}}</th>
                    <th>{{transactionDebitLabel}}</th>
                </tr>
            </thead>
            <tbody>
            {{#transactions}}
                <tr class="bold_transaction" data-hook="transaction">
                    <td data-hook="transaction-res-identifier">{{transactionReservationIdentifier}}</td>
                    <td data-hook="transaction-res-date">{{transactionDateTime}}</td>
                    <td data-hook="transaction-res-name">{{transactionName}}</td>
                    {{#hasRoomNumber}}
                        <td data-hook="transaction-res-room-name">{{transactionRoomName}}</td>
                    {{/hasRoomNumber}}
                    <td data-hook="transaction-description">{{transactionDescription}}</td>
                    <td data-hook="transaction-qty">{{transactionNights}}</td>
                    <td>
                        <span style="color:{{transactionCreditColour}}" data-hook="transaction-credit">
                            {{transactionCredit}}
                        </span>
                    </td>
                    <td>
                        <span style="color:{{transactionDebitColour}}" data-hook="transaction-debit">
                            {{transactionDebit}}
                        </span>
                    </td>
                </tr>
            {{/transactions}}
            <tr class="total" valign="middle">
                {{#hasRoomNumber}}
                    <td data-hook="transaction-res-room-name"></td>
                {{/hasRoomNumber}}
                <td style="text-align: right;padding-right: 20px;font-weight: bold;" colspan="5" class="t_name"> {{transactionsTotalLabel}}</td>
                <td><span style="color:{{creditColour}}" data-hook="transactions-total-credit">{{transactionsTotalCredit}}</span></td>
                <td><span style="color:{{debitColour}}" data-hook="transactions-total-debit">{{transactionsTotalDebit}}</span></td>
            </tr>
            </tbody>
        </table>

        <br/>
        {{#customText}}
            <div style="font-size:10px; padding-bottom: 10px" data-hook="invoice-custom-text">{{customText}}</div>
        {{/customText}}
    </body>
</html>
