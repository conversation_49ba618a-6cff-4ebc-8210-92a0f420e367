<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html lang="{{lang}}">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        table tr th, table tr td {
            text-align: left;
            {{fontFamily}}
            font-size: 10px;
            color: #000;
        }

        .margin_right, .invoice_data td {
            padding-right: 35px;
        }

        table.invoice_data tr th, table.reservation_data tr th, span.title {
            color: #363636;
            font-weight: normal;
        }

        table.table_reservation tr th {
            text-transform: uppercase;
            font-weight: bold;
            padding-right: 25px;
            padding-bottom: 5px;
            font-size: 9px;
        }

        table.table_reservation tr td {
            padding: 5px 8px 5px 0;
            font-size: 9px;
            margin-bottom: 0;
        }

        table.table_balance_due tr th.foreign_currency {
            font-weight: normal;
            text-align: right;
            padding-right: 10px;
            font-size: 8px;
            color: #647582;
        }

        table.table_balance_due tr td.foreign_currency {
            text-align: right;
            text-transform: uppercase;
            font-weight: bold;
            padding-right: 10px;
            font-size: 10px;
            color: #647582;
        }

        .table_balance_due_td{
            min-width: 30%;
            max-width: 50%;
        }

        .table_balance_due th, .table_balance_due td {
            padding: 5px;
            vertical-align: top;
        }

        .footer_table table.table_foreign_currency tr td {
            text-align: right;
            color: #647582;
            font-size: 9px;
        }

        .footer_table table.table_foreign_currency tr td.foreign_currency.rate {
            font-size: 8px;
            padding-bottom: 10px;
            font-weight: normal;
        }

        .footer_table table.table_foreign_currency tr th.local_currency {
            text-align: right;
            font-weight: normal;
            font-size: 9px;
            color: #2A4257;
        }

        .footer_table table.table_foreign_currency tr td.foreign_currency,
        .footer_table table.table_foreign_currency tr th.local_currency {
            font-size: 10px;
            font-weight: bold;
        }

        table.footer_table {
            background-color: #E2EAF0;
        }

        tr.bold_transaction td {
            border-bottom: 1px solid #efefef;
        }

        tr.bold_transaction_last td {
            border-bottom: 1px solid #6B6B6B;
        }

        table.table_reservation tr.total td {
            border-bottom: 1px solid #6B6B6B;
            padding-bottom: 5px;
            padding-top: 5px;
        }

        table.customer_info_table tr .col-name {
            color: #363636;
        }

        .company-col-name {
            white-space: nowrap;
        }

        .cust_email {
            margin-right: 30px;
        }

        .customer_info_table .col-name {
            padding-right: 5px;
        }

        .customer_info_table td {
            padding-bottom: 6px;
        }

        .reservation_data td, .reservation_data th {
            padding-right: 25px;
        }

        .total_info tr td {
            border-top: 1px solid #6B6B6B;
            border-bottom: 1px solid #6B6B6B;
            padding: 10px 4px;
        }

        table.customer_info_table tr td {
            padding: 5px 4px;
        }

        .total_info tr td.r_border {
            border-right: 1px solid #6B6B6B;
        }

        .invoice_data tr td {
            border: none;
            padding: 0;
            vertical-align: top;
        }

        #qr_code {
            margin: auto;
            height: 76px;
            width: 76px;
        }
    </style>
</head>
<body>
    <div style="clear:both"></div>
    <div class="hotel_address">
        <table style="width: 100%;">
            <tr>
                <td class="customer" style="width: 50%">
                    {{#invoiceImg}}
                        <img height="100" src="{{invoiceImg}}" alt=""/>
                    {{/invoiceImg}}
                    <br/>
                    <div style="font-size: 42px; text-transform: uppercase">
                        {{invoiceTitle}}
                    </div>
                </td>
                <td class="hotel-info" style="width: 50%; float:right;text-align: right;vertical-align: top;">
                    <p class="hotel_name">{{hotelName}}</p>
                    {{#legalCompanyName}}
                        <p class="hotel_name">{{legalCompanyName}}</p>
                    {{/legalCompanyName}}
                    <p class="street">{{#hotelAddress}}{{hotelAddress}}{{/hotelAddress}}</p>
                    <p class="city">{{#hotelCity}}{{hotelCity}}{{/hotelCity}} {{#hotelState}}{{hotelState}}{{/hotelState}} {{#hotelZip}}{{hotelZip}}{{/hotelZip}}</p>
                    <p class="phone">{{#hotelPhone}}{{hotelPhone}}{{/hotelPhone}}</p>
                    <p class="email">{{#hotelEmail}}{{hotelEmail}}{{/hotelEmail}}</p>
                    <p class="country">{{#hotelCountry}}{{hotelCountry}}{{/hotelCountry}}</p>
                    <p>{{#taxId1}}{{taxId1}}{{/taxId1}}</p>
                    <p>{{#taxId2}}{{taxId2}}{{/taxId2}}</p>
                </td>
            </tr>
        </table>
    </div>
    <br/>
    <div style="font-weight: 500; font-size: 24px;">{{reservationFirstName}} {{reservationLastName}}</div>
    <div style="font-weight: 500; font-size: 18px;">{{reservationNumberLabel}} {{reservationNumber}}</div>
    <br/>

    <table class="total_info">
        <tbody>
        <tr>
            <td class="r_border" style="width:60%">
                <table class="invoice_data">
                    <tbody>
                    <tr>
                        <td style="padding-bottom: 20px;">
                            <span class="title">{{rectifyInvoiceLabel}}</span><br/>
                            <span data-hook="credit-note-number">{{rectifyInvoiceNumber}}</span>
                        </td>
                        <td>
                            <span class="title">{{invoiceDateLabel}}</span><br/>
                            <span data-hook="invoice-date">
                                {{invoiceDate}}
                            </span>
                        </td>
                        <td style="padding-bottom: 20px;">
                            {{#cpf}}
                                <span class="title">{{cpfLabel}}</span><br/>
                                <span data-hook="invoice-cpf">{{cpf}}</span>
                            {{/cpf}}
                            </td>
                        </tr>
                        <tr>
                            <td>
                                {{#invoiceNumber}}
                                    <span class="title">{{originalInvoiceNumberLabel}}</span><br/>
                                    <span data-hook="invoice-number">{{invoiceNumber}}</span>
                                {{/invoiceNumber}}
                            </td>
                            <td>
                                {{#invoiceDate}}
                                    <span class="title">{{originalInvoiceDateLabel}}</span><br/>
                                    <span data-hook="invoice-date">{{invoiceDate}}</span>
                                {{/invoiceDate}}
                            </td>
                            <td>
                                {{#creditNoteReason}}
                                <span class="title">{{creditNoteReasonLabel}}</span><br/>
                                <span data-hook="credit-note-reason">{{creditNoteReason}}</span>
                                {{/creditNoteReason}}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td style="width:40%; vertical-align: top;">
                <table class="invoice_data" style="width:100%;">
                    <tbody>
                    <tr>
                        <td>{{transactionsTotalLabel}}</td>
                        <td data-hook="total">{{transactionTotal}}</td>
                    </tr>
                    <tr>
                        <td>{{resCreateTaxesLabel}}</td>
                        <td data-hook="taxes">{{resCreateTaxes}}</td>
                    </tr>
                    {{#specificTaxes}}
                        <tr data-hook="specific-taxes">
                            <td data-hook="specific-tax-name" style="padding-left: 10px;">{{name}}</td>
                            <td data-hook="specific-tax">{{value}}</td>
                        </tr>
                    {{/specificTaxes}}
                    <tr>
                        <td>{{resCreateFeesLabel}}</td>
                        <td data-hook="fees">{{resCreateFees}}</td>
                    </tr>
                    {{#specificFees}}
                    <tr data-hook="specific-fees">
                        <td data-hook="specific-fee-name" style="padding-left: 10px;">{{name}}</td>
                        <td data-hook="specific-fee">{{value}}</td>
                    </tr>
                    {{/specificFees}}
                    <tr>
                        <td>{{grandTotalLabel}}</td>
                        <td data-hook="grand-total">{{grandTotal}}</td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        </tbody>
    </table>
    <table cellspacing="0" cellpadding="0" border="0" style="width: 100%;" class="customer_info_table">
        <tr>
            <td valign="top">
                <table cellspacing="0" cellpadding="0" border="0">
                    {{#occupantTaxIdNumber}}
                        <tr>
                            <td class="col-name company-col-name">
                                {{occupantTaxIdNumberLabel}}
                            </td>
                            <td>
                                <span class="cust_email" data-hook="tax-id">{{occupantTaxIdNumber}}</span>
                            </td>
                        </tr>
                    {{/occupantTaxIdNumber}}
                    <tr>
                        <td class="col-name">
                            {{occupantNameLabel}}
                        </td>
                        <td>
                            <span class="cust_email" data-hook="company-name">{{occupantName}}</span>
                        </td>
                    </tr>
                    {{#address}}
                    <tr>
                        <td class="col-name">{{addressLabel}}</td>
                        <td>
                            <span class="cust_address1" data-hook="guest-address1">{{address}}</span>
                        </td>
                    </tr>
                    {{/address}}

                    {{#address2}}
                    <tr>
                        <td class="col-name">{{address2Label}}</td>
                        <td>
                            <span class="cust_address2" data-hook="guest-address2">{{address2}}</span>
                        </td>
                    </tr>
                    {{/address2}}

                    {{#city}}
                    <tr>
                        <td class="col-name">{{cityLabel}}</td>
                        <td>
                            <span class="cust_city" data-hook="guest-city">{{city}}</span>
                        </td>
                    </tr>
                    {{/city}}

                    {{#countryName}}
                    <tr>
                        <td class="col-name">{{countryNameLabel}}</td>
                        <td>
                            <span class="cust_state" data-hook="guest-country">{{countryName}}</span>
                        </td>
                    </tr>
                    {{/countryName}}
                    {{#state}}
                    <tr>
                        <td class="col-name">{{stateLabel}}</td>
                        <td>
                            <span class="cust_state" data-hook="guest-state">{{state}}</span>
                        </td>
                    </tr>
                    {{/state}}
                    {{#zip}}
                    <tr>
                        <td class="col-name">{{zipLabel}}</td>
                        <td>
                            <span class="cust_zip" data-hook="guest-zip">{{zip}}</span>
                        </td>
                    </tr>
                    {{/zip}}
                </table>
            </td>
            <td valign="top">
                <table cellspacing="0" cellpadding="0" border="0">
                    <tr>
                        <td class="col-name">{{pdfEmailLabel}}</td>
                        <td><span class="cust_email" data-hook="guest-email">{{pdfEmail}}</span></td>
                    </tr>
                    {{#phone}}
                    <tr>
                        <td class="col-name">{{phoneLabel}}</td>
                        <td><span class="cust_phone" data-hook="guest-phone">{{phone}}</span></td>
                    </tr>
                    {{/phone}}
                    {{#cellPhone}}
                    <tr>
                        <td class="col-name">{{cellPhoneLabel}}</td>
                        <td><span class="cust_cellphone" data-hook="guest-cellphone">{{cellPhone}}</span></td>
                    </tr>
                    {{/cellPhone}}
                    {{#customFields}}
                    <tr data-hook="guest-custom-field">
                        <td class="col-name" data-hook="guest-custom-field-label">{{customFieldLabel}}</td>
                        <td><span class="cust_zip" data-hook="guest-custom-field-value">{{customFieldValue}}</span></td>
                    </tr>
                    {{/customFields}}
                </table>
            </td>
            <td style="border-left: 1px solid #6B6B6B; background-color:#DCDCDC " valign="middle" align="center" class="table_balance_due_td">
                <table cellspacing="0" cellpadding="0" border="0" class="table_balance_due">
                    <thead>
                        <tr>
                            <th>
                                {{remainingAmountLabel}} {{#defaultCurrency}}<span data-hook="default-currency">({{defaultCurrency}})</span> {{/defaultCurrency}}
                            </th>
                            {{#foreignCurrencies}}
                            <th class="foreign_currency">
                                {{#foreignCurrencyRate}}
                                    1 {{foreignCurrencyCode}} = {{foreignCurrencyRate}}
                                {{/foreignCurrencyRate}}
                            </th>
                            {{/foreignCurrencies}}
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td data-hook="balance-due">{{balanceDue}}</td>
                            {{#foreignCurrencies}}
                                <td class="foreign_currency">
                                    {{foreignCurrencyInvoiceTotal}}
                                </td>
                            {{/foreignCurrencies}}
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </table>
    <hr style="margin-top: 0px;"/>
    <table border="0" class="reservation_data" style="width:100%;">
        <thead>
        <tr>
            <th style="width:20%">{{checkInLabel}}</th>
            <th style="width:20%">{{checkOutLabel}}</th>
            <th style="width:20%">{{nightsLabel}}</th>
            <th style="width:20%">{{reservationDateLabel}}</th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td data-hook="res-checkin">{{checkIn}}</td>
            <td data-hook="res-checkout">{{checkOut}}</td>
            <td data-hook="res-nights">{{nights}}</td>
            <td data-hook="res-booking-date">{{reservationDate}}</td>
        </tr>
        </tbody>
    </table>
    <hr/>
    <br/>

    <table style="width:100%;" class="table_reservation" border="0"  cellspacing="0" cellpadding="0" valign="middle">
        <thead>
            <tr>
                <th>{{transactionDateTimeLabel}}</th>
                <th>{{transactionNameLabel}}</th>
                {{#hasRoomNumber}}
                    <th>{{resRoomLabel}}</th>
                {{/hasRoomNumber}}
                <th>{{descriptionResLabel}}</th>
                <th>{{nightsLabel}}</th>
                <th>{{transactionAmountLabel}}</th>
                <th>{{transactionVatLabel}}</th>
            </tr>
        </thead>
        <tbody>
            {{#transactions}}
            <tr class="bold_transaction" data-hook="transaction">
                <td data-hook="transaction-res-date">{{transactionDateTime}}</td>
                <td data-hook="transaction-res-name">{{transactionName}}</td>
                {{#hasRoomNumber}}
                    <td data-hook="transaction-res-room-name">{{transactionRoomName}}</td>
                {{/hasRoomNumber}}
                <td data-hook="transaction-description">{{transactionDescription}}</td>
                <td data-hook="transaction-qty">{{transactionNights}}</td>
                <td>
                            <span style="color:{{transactionCreditColour}}" data-hook="transaction-credit">
                                {{transactionAmount}}
                            </span>
                </td>
                <td>
                            <span style="color:{{transactionDebitColour}}" data-hook="transaction-debit">
                                {{transactionVat}}
                            </span>
                </td>
            </tr>
            {{/transactions}}
            <tr class="total" valign="middle">
                {{#hasRoomNumber}}
                    <td data-hook="transaction-res-room-name"></td>
                {{/hasRoomNumber}}
                <td style="text-align: right;padding-right: 20px;font-weight: bold;" colspan="4" class="t_name"> {{transactionsTotalLabel}}</td>
                <td><span style="color:{{creditColour}}" data-hook="transactions-total-credit">{{transactionsTotalAmount}}</span></td>
                <td><span style="color:{{debitColour}}" data-hook="transactions-total-debit">{{transactionsTotalVat}}</span></td>
            </tr>
        </tbody>
    </table>

    <br/>
    {{#customText}}
    <div style="font-size:10px; padding-bottom: 10px" data-hook="invoice-custom-text">{{customText}}</div>
    {{/customText}}
    <table cellspacing="0" cellpadding="0" border="0" style="width: 100%;" class="footer_table">
        <tr><td colspan="3">&nbsp;</td></tr>
        <tr>
            <td style="width: 60%;">&nbsp;</td>
            <td valign="middle" align="center">
                <table cellspacing="0" cellpadding="0" border="0" class="table_foreign_currency">
                    <thead>
                        <tr>
                            <th style="width:200px;" class="local_currency">{{remainingAmountLabel}}:</th>
                            <th style="width:200px;" class="local_currency" data-hook="local-currency-balance-due">{{balanceDue}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td colspan="3">&nbsp;</td></tr>
                        {{#foreignCurrencies}}
                            <tr>
                                <td></td>
                                <td class="foreign_currency" data-hook="foreign-currency-balance-due">
                                   {{foreignCurrencyInvoiceTotal}}
                                </td>
                            </tr>
                            {{#foreignBalanceDueRate}}
                            <tr>
                                <td></td>
                                <td class="foreign_currency rate" data-hook="foreign-currency-balance-due">
                                    1 {{foreignCurrencyCode}} = {{foreignCurrencyRate}}
                                </td>
                            </tr>
                            {{/foreignBalanceDueRate}}
                            {{^foreignBalanceDueRate}}
                                <tr><td></td><td class="foreign_currency rate" data-hook="invoice-currency-rate"></td></tr>
                            {{/foreignBalanceDueRate}}
                            <tr><td colspan="3"></td></tr>
                        {{/foreignCurrencies}}
                    </tbody>
                </table>
            </td>
            <td style="width: 10px;"></td>
        </tr>
    </table>
    {{#qrCodeFilename}}
    <div id="qr_code">
        <img src="{{qrCodeFilename}}"/>
        <p>{{officialId}}</p>
        <p>{{governmentIntegrationUrl}}</p>
    </div>
    {{/qrCodeFilename}}
</body>
</html>
