<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <statusListener class="ch.qos.logback.core.status.NopStatusListener" />
  <springProperty scope="context" name="springAppName" source="spring.application.name"/>

  <!-- Define appender only for local profile -->
  <springProfile name="!kubernetes">
    <property name="CONSOLE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [${springAppName},%X{traceId:-}, %X{spanId:-}] %-5level %logger{36} - %msg %n}"/>

    <!-- Appender to log to console -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
      <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
        <!-- Minimum logging level to be presented in the console logs-->
        <level>INFO</level>
      </filter>
      <encoder>
        <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        <charset>utf8</charset>
      </encoder>
    </appender>
    <root level="INFO">
      <appender-ref ref="console"/>
    </root>
    <logger name="com.cloudbeds" level="DEBUG" additivity="false">
      <appender-ref ref="CONSOLE"/>
    </logger>
    <logger name="org.springframework.web.servlet.DispatcherServlet" level="DEBUG">
      <appender-ref ref="CONSOLE"/>
    </logger>
  </springProfile>

  <!-- Define appender for every profile except local -->
  <springProfile name="kubernetes">
    <appender name="jsonAppender" class="ch.qos.logback.core.ConsoleAppender">
      <filter class="ch.qos.logback.classic.filter.ThresholdFilter">-->
        <!-- Minimum logging level to be presented in the console logs-->
        <level>${LOG_LEVEL}</level>
      </filter>
      <encoder class="net.logstash.logback.encoder.LogstashEncoder">
        <includeContext>false</includeContext>
        <shortenedLoggerNameLength>36</shortenedLoggerNameLength>
        <includeMdcKeyName>traceId</includeMdcKeyName>
        <includeMdcKeyName>spanId</includeMdcKeyName>
        <fieldNames>
          <timestamp>timestamp</timestamp>
          <version>[ignore]</version>
          <levelValue>[ignore]</levelValue>
        </fieldNames>
      </encoder>
    </appender>
    <root level="INFO">
      <appender-ref ref="jsonAppender"/>
    </root>
  </springProfile>

  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

  <logger name="org.springframework.web.client.RestTemplate" level="off"/>

  <!-- log everything from the application itself -->
  <logger name="com.cloudbeds" level="debug"/>

  <!-- log every incoming request -->
  <logger name="org.springframework.web.servlet.PageNotFound" level="debug"/>
  <logger name="org.springframework.web.servlet.DispatcherServlet" level="warn"/>

  <!-- make spring quiet -->
  <logger name="org.springframework.cloud.context.scope" level="off"/>
  <logger name="org.hibernate.engine.jdbc.batch.internal.BatchingBatch" level="off"/>
  <logger name="org.springframework.scheduling.concurrent" level="off"/>
  <logger name="org.springframework.boot.actuate.endpoint.web" level="off"/>
  <logger name="org.apache.kafka.clients.NetworkClient" level="WARN"/>
  <logger name="com.cloudbeds.distributedid.pool.IdPool" level="ERROR"/>
  <logger name="org.apache.kafka" level="WARN"/>
  <logger name="org.springframework.kafka" level="WARN"/>
  <logger name="io.confluent.kafka" level="WARN"/>

  <logger name="com.openhtmltopdf.general" level="ERROR" additivity="false"/>
  <logger name="com.openhtmltopdf.match" level="ERROR" additivity="false"/>
  <logger name="com.openhtmltopdf.load" level="ERROR" additivity="false"/>
  <logger name="com.openhtmltopdf.load.xml-entities" level="ERROR" additivity="false"/>

  <!-- Silence OpenHTMLToPDF non-error logs -->
  <logger name="com.openhtmltopdf.general" level="WARN" additivity="false"/>
  <logger name="com.openhtmltopdf.match" level="WARN" additivity="false"/>
  <logger name="com.openhtmltopdf.load" level="WARN" additivity="false"/>
  <logger name="com.openhtmltopdf.load.xmlentities" level="WARN" additivity="false"/>
  <logger name="com.openhtmltopdf.cssparse" level="WARN" additivity="false"/>
  <logger name="com.openhtmltopdf.render" level="WARN" additivity="false"/>
  <logger name="com.openhtmltopdf.config" level="WARN" additivity="false"/>
  <logger name="com.openhtmltopdf.exception" level="WARN" additivity="false"/>
  <logger name="com.openhtmltopdf.layout" level="WARN" additivity="false"/>
  <logger name="com.openhtmltopdf.init" level="WARN" additivity="false"/>
  <logger name="com.openhtmltopdf.cascade" level="WARN" additivity="false"/>

  <!-- Silence PDFBox font fallback warnings -->
  <logger name="org.apache.pdfbox.pdmodel.font.PDType1Font" level="ERROR"/>
</configuration>