CREATE TABLE document_sequence
(
    id                bigint    not null primary key,
    auto_reset        boolean   not null default false,
    reset_datetime    timestamp,
    increment         int not null default 1,
    number            bigint not null default 1,
    property_id       bigint not null,
    created_at        timestamp not null default NOW(),
    updated_at        timestamp          default null
);

CREATE INDEX IF NOT EXISTS "idx-document_sequence_property_id" on document_sequence (property_id);


ALTER TABLE document_config DROP COLUMN IF EXISTS increment;
ALTER TABLE document_config DROP COLUMN IF EXISTS auto_reset;
ALTER TABLE document_config DROP COLUMN IF EXISTS reset_datetime;
ALTER TABLE document_config DROP COLUMN IF EXISTS number;

ALTER TABLE document_content DROP COLUMN IF EXISTS document_type;
ALTER TABLE fiscal_document DROP COLUMN IF EXISTS document_type;

TRUNCATE document_config CASCADE ;
TRUNCATE document_content CASCADE ;