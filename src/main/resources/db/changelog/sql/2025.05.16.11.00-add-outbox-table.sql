CREATE TABLE document_event_send_status (
    id bigint not null constraint pk_document_event_send_status primary key,
    invoice_create_event jsonb,
    credit_note_create_event jsonb,
    version int,
    created_at timestamp not null default now()

);
create index if not exists "idx-document_event_send_status-created_at" on document_event_send_status (created_at);

CREATE TABLE locked_tables (
    id bigint not null constraint pk_table_locks primary key,
    table_name varchar unique
);

INSERT INTO locked_tables (id, table_name) VALUES (1, 'document_event_send_status');

CREATE SEQUENCE seq_common_fiscal_document_id START 1 INCREMENT 10000;

ALTER TABLE fiscal_document ADD COLUMN fail_reason varchar;