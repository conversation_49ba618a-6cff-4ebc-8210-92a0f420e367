CREATE TABLE document_send_status (
    id bigint not null constraint pk_document_send_status primary key,
    payload jsonb,
    version int,
    created_at timestamp not null default now()

);
create index if not exists "idx-document_send_status-created_at" on document_send_status (created_at);

INSERT INTO locked_tables (id, table_name) VALUES (2, 'document_send_status');

ALTER TABLE fiscal_document add column version integer default 0;
