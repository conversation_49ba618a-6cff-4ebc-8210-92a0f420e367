CREATE TABLE fiscal_document
(
    id                bigint    not null primary key,
    number            text      not null,
    property_id       bigint    not null,
    source_id         bigint    not null,
    user_id           bigint    not null,
    source_kind       text      not null,
    kind              text      not null,
    status            text      not null,
    url               text,
    created_at        timestamp not null default NOW(),
    updated_at        timestamp          default null
);

CREATE TABLE fiscal_document_transaction
(
    fiscal_document_id  bigint    not null,
    transaction_id      bigint    not null,
    PRIMARY KEY (fiscal_document_id, transaction_id)
);

CREATE TABLE document_config
(
    property_id       bigint    not null,
    document_kind     varchar   not null,
    auto_reset        boolean   not null default false,
    reset_datetime    timestamp,
    prefix            varchar,
    increment         int not null default 1,
    due_days          int not null default 1,
    number            bigint not null default 0,
    img_url           varchar,
    title             varchar,
    show_legal_company_name boolean not null default false,
    show_detailed_tax_fee    boolean not null default false,
    tax_id1             varchar,
    tax_id2             varchar,
    cpf                 varchar,
    custom_text         varchar,
    include_room_number boolean not null default false,
    use_document_number boolean not null default false,
    is_compact          boolean not null default false,
    created_at          timestamp not null default NOW(),
    updated_at          timestamp          default null,
    PRIMARY KEY (property_id, document_kind)
);

CREATE TABLE document_content
(
    id                bigint    not null primary key,
    property_id       bigint    not null,
    document_kind     varchar   not null,
    source_kind       varchar,
    prefix            varchar,
    number            bigint,
    html              text not null,
    active            boolean not null default true,
    created_at        timestamp not null default NOW(),
    updated_at        timestamp          default null
);