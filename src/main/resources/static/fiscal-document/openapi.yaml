openapi: 3.0.1
info:
  title: Fiscal document service API
  version: v1

servers:
  - url: http://localhost:8700
  - url: https://api.{environment}.com
    variables:
      environment:
        default: cloudbeds-stage
        enum:
          - cloudbeds-stage   # Staging server
          - cloudbeds         # Production server
  - url: https://api.{island}.{environment}.com
    variables:
      island:
        default: us2
        enum:
          - us2
      environment:
        default: cloudbeds-stage
        enum:
          - cloudbeds-stage   # Staging server
          - cloudbeds         # Production server

paths:
  "/fiscal-document/v1/fiscal-documents":
    get:
      summary: Get list of fiscal documents
      description: Retrieves a paginated list of fiscal documents filtered by optional criteria.
      operationId: getFiscalDocuments
      tags:
        - Fiscal Documents
      security:
        - bearerAuth: [ ]
      parameters:
        - $ref: "#/components/parameters/propertyIdHeader"

        - name: pageToken
          in: query
          description: Token for fetching the next page, as per cursor-based pagination.
          required: false
          schema:
            type: string

        - name: sort
          in: query
          description: Supported fields createdAt. Supported sort modes asc:desc If not supplied default is asc.
          required: false
          schema:
            type: string
            example: createdAt:desc

        - name: limit
          in: query
          description: Number of results to return per page.
          required: false
          schema:
            type: integer
            format: int32
            default: 20
            minimum: 1
            maximum: 100

        - name: ids
          in: query
          description: Comma-separated list of IDs.
          required: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: string
            example: [ "123", "456", "789" ]

        - name: sourceIds
          in: query
          description: Comma-separated list of source IDs.
          required: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: string
            example: [ "123", "456", "789" ]

        - name: sourceIdentifiers
          in: query
          description: Comma-separated list of source-specific identifiers.
          required: false
          style: form
          explode: false
          schema:
            type: array
            items:
              type: string
            example: [ "ABC123", "XYZ789" ]

        - name: sourceKind
          in: query
          description: Filter by source kind.
          required: false
          schema:
            $ref: "#/components/schemas/SourceKind"

        - name: numberContains
          in: query
          description: Filter by document number partial match.
          required: false
          schema:
            type: string
        - name: statuses
          in: query
          description: Comma-separated list of fiscal document statuses.
          required: false
          style: form
          explode: false
          schema:
            type: array
            items:
              $ref: "#/components/schemas/FiscalDocumentStatus"
            example: [ "DRAFT", "COMPLETED" ]

        - name: kinds
          in: query
          description: Comma-separated list of fiscal document kinds.
          required: false
          style: form
          explode: false
          schema:
            type: array
            items:
              $ref: "#/components/schemas/FiscalDocumentKind"
            example: [ "INVOICE", "RECEIPT" ]
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/FiscalDocumentPaginated"
  "/fiscal-document/v1/fiscal-documents/{id}":
    put:
      summary: Update a fiscal document by id
      description: Endpoint to update a fiscal document by id. Used for integrations partners for fiscalized countries.
      operationId: putFiscalDocument
      security:
        - bearerAuth: [ ]
      tags:
        - Fiscal Documents
      parameters:
        - name: id
          in: path
          required: true
          description: Unique ID of the fiscal document to download.
          schema:
            type: integer
            format: int64
            minimum: 1
        - $ref: "#/components/parameters/propertyIdHeader"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/FiscalDocumentPatchRequest"
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/FiscalDocumentSummaryResponse"
  "/fiscal-document/v1/fiscal-documents/transactions":
    get:
      summary: Get list of fiscal documents
      description: Retrieves a paginated list of available transactions for source.
      operationId: getFiscalDocumentTransactions
      tags:
        - Fiscal Documents
      security:
        - bearerAuth: [ ]
      parameters:
        - $ref: "#/components/parameters/propertyIdHeader"
        - name: forDocumentType
          in: query
          description: Document type for which transactions are related.
          required: true
          schema:
            $ref: "#/components/schemas/FiscalDocumentKind"

        - name: pageToken
          in: query
          description: Token for fetching the next page, as per cursor-based pagination.
          required: false
          schema:
            type: string

        - name: limit
          in: query
          description: Number of results to return per page.
          required: false
          schema:
            type: integer
            format: int32
            default: 20
            minimum: 1
            maximum: 100

        - name: sourceId
          in: query
          description: source ID.
          required: true
          schema:
            type: integer
            format: int64
            minimum: 1

        - name: sourceKind
          in: query
          description: Filter by source kind.
          required: true
          schema:
            $ref: "#/components/schemas/SourceKind"

      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/FiscalDocumentTransactionsPaginated"
  "/fiscal-document/v1/fiscal-documents/{id}/transactions":
    get:
      summary: Get list of transactions for a given fiscal document id
      description: Retrieves a paginated list of available transactions for fiscal document id.
      operationId: getFiscalDocumentTransactionsById
      tags:
        - Fiscal Documents
      security:
        - bearerAuth: [ ]
      parameters:
        - $ref: "#/components/parameters/fiscalDocumentId"
        - $ref: "#/components/parameters/propertyIdHeader"

        - name: pageToken
          in: query
          description: Token for fetching the next page, as per cursor-based pagination.
          required: false
          schema:
            type: string
        - name: includeLinkedDocumentTransactions
          in: query
          description: Include transactions from linked documents.
          required: false
          schema:
            type: boolean
            default: false
        - name: limit
          in: query
          description: Number of results to return per page.
          required: false
          schema:
            type: integer
            format: int32
            default: 20
            minimum: 1
            maximum: 100

      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/FiscalDocumentTransactionsPaginated"
  "/fiscal-document/v1/fiscal-documents/{id}/recipients":
    get:
      summary: Get list of recipients associated to the fiscal document
      description: Retrieves a list of recipients associated to the transaction.
      operationId: getFiscalDocumentRecipientsById
      tags:
        - Fiscal Documents
      security:
        - bearerAuth: [ ]
      parameters:
        - $ref: "#/components/parameters/fiscalDocumentId"
        - $ref: "#/components/parameters/propertyIdHeader"
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/FiscalDocumentRecipient"
  '/fiscal-document/v1/fiscal-documents/{id}/download':
    get:
      summary: Download fiscal document
      description: Initiates the download of the fiscal document file
      operationId: downloadFiscalDocument
      tags:
        - Fiscal Documents
      security:
        - bearerAuth: []
      parameters:
        - $ref: "#/components/parameters/fiscalDocumentId"
        - $ref: "#/components/parameters/propertyIdHeader"
      responses:
        '200':
          description: Successful file download
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
          headers:
            Content-Disposition:
              description: Used to indicate if the content should be displayed inline or as an attachment.
              schema:
                type: string
  '/fiscal-document/v1/fiscal-documents/{id}/email':
    post:
      summary: Email a fiscal document
      description: Initiates the process to send the invoice to a customer
      operationId: emailFiscalDocument
      tags:
        - Fiscal Documents
      security:
        - bearerAuth: [ ]
      parameters:
        - $ref: "#/components/parameters/fiscalDocumentId"
        - $ref: "#/components/parameters/propertyIdHeader"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/FiscalDocumentEmailRequest"
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
  "/fiscal-document/v1/fiscal-documents/invoice":
    post:
      summary: Create a fiscal document of the type invoice
      description: Create a fiscal document of the type invoice.
      operationId: createInvoice
      security:
        - bearerAuth: [ ]
      tags:
        - Fiscal Documents
      parameters:
        - $ref: "#/components/parameters/propertyIdHeader"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/CreateInvoiceRequest"
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/FiscalDocumentSummaryResponse"

  "/fiscal-document/v1/fiscal-documents/credit-note":
    post:
      summary: Create a fiscal document of the type credit note
      description: Create a fiscal document of the type credit note.
      operationId: createCreditNote
      security:
        - bearerAuth: [ ]
      tags:
        - Fiscal Documents
      parameters:
        - $ref: "#/components/parameters/propertyIdHeader"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/CreateCreditNoteRequest"
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/FiscalDocumentSummaryResponse"
  "/fiscal-document/v1/fiscal-documents/rectify-invoice":
    post:
      summary: Create a fiscal document of the type rectify invoice
      description: Create a fiscal document of the type rectify invoice.
      operationId: createRectifyInvoice
      security:
        - bearerAuth: [ ]
      tags:
        - Fiscal Documents
      parameters:
        - $ref: "#/components/parameters/propertyIdHeader"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/RectifyInvoiceNoteRequest"
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/FiscalDocumentSummaryResponse"
  "/fiscal-document/v1/configs":
    get:
      summary: Get list of fiscal documents configs
      description: Retrieves a paginated list of fiscal documents filtered by optional criteria.
      operationId: getConfigs
      tags:
        - Configs
      security:
        - bearerAuth: [ ]
      parameters:
        - $ref: "#/components/parameters/propertyIdHeader"
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ConfigsResponse'
  "/fiscal-document/v1/configs/{documentKind}":
    put:
      summary: Updates a config of a specific kind
      operationId: updateConfigs
      description: Update document config.
      security:
        - bearerAuth: [ ]
      tags:
        - Configs
      parameters:
        - $ref: "#/components/parameters/propertyIdHeader"
        - name: documentKind
          in: path
          required: true
          description: The kind of the fiscal document.
          schema:
            $ref: "#/components/schemas/FiscalDocumentKind"
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/ConfigsUpdateRequest"
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ConfigsResponse"
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  parameters:
    propertyIdHeader:
      name: X-Property-ID
      in: header
      required: true
      schema:
        type: integer
        format: int64
        minimum: 1
      description: Property id
    fiscalDocumentId:
      name: id
      in: path
      required: true
      description: Unique ID of the fiscal document to download.
      schema:
        type: string
        minLength: 1
  responses:
    400:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiError'
    403:
      description: Access denied
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiError'
    404:
      description: Not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiError'
  schemas:
    AnyValue:
      description: 'Can be any of supported OpenApi types.'
    FiscalDocumentEmailRequest:
      type: object
      required:
        - emails
      properties:
        emails:
          type: array
          items:
            type: string
            format: email
          maxItems: 10
          minItems: 1
    CreateCreditNoteRequest:
      type: object
      required: [invoiceId, method]
      properties:
        sequenceId:
          type: integer
          format: int64
          minimum: 1
          nullable: true
        invoiceId:
          type: integer
          format: int64
          minimum: 1
        reason:
          type: string
          nullable: true
        userId:
          type: integer
          format: int64
          minimum: 0
          nullable: true
        method:
          $ref: "#/components/schemas/CreationMethod"
        transactionIds:
          type: array
          items:
            type: integer
            format: int64
    RectifyInvoiceNoteRequest:
      type: object
      required: [invoiceId, method]
      properties:
        invoiceId:
          type: integer
          format: int64
        reason:
          type: string
        userId:
          type: integer
          format: int64
        method:
          $ref: "#/components/schemas/CreationMethod"
        transactionIds:
          type: array
          nullable: true
          items:
            type: integer
            format: int64
            minimum: 1
    CreateInvoiceRequest:
      type: object
      required: [transactionIds, sourceId, sourceKind, guestId, recipient]
      properties:
        transactionIds:
          type: array
          minItems: 1
          items:
            type: integer
            format: int64
            minimum: 1
        sourceId:
          type: integer
          format: int64
          minimum: 1
        sequenceId:
          type: integer
          format: int64
          minimum: 1
          nullable: true
        sourceKind:
          $ref: "#/components/schemas/SourceKind"
        userId:
          type: integer
          format: int64
          minimum: 0
          nullable: true
        recipient:
          $ref: "#/components/schemas/RecipientRequest"

    RecipientRequest:
      type: object
      required:
        - type
        - id
      properties:
          type:
            type: string
            enum:
              - GUEST
              - CONTACT
              - GROUP
              - COMPANY
            description: Type of the recipient.
          id:
            type: integer
            format: int64
            description: ID of the recipient, references guestId, contactId, groupId, etc. depending on type.
            minimum: 1

    FiscalDocumentDetailedResponse:
      type: object
      properties:
        id:
          type: string
        number:
          type: string
        propertyId:
          type: string
        userId:
          type: string
        userFullName:
          type: string
        sourceId:
          type: string
        sourceKind:
          $ref: "#/components/schemas/SourceKind"
        kind:
          $ref: "#/components/schemas/FiscalDocumentKind"
        invoiceDate:
          type: string
          format: date
        fileName:
          type: string
        amount:
          type: number
        balance:
          type: number
        dueDate:
          type: string
          format: date
        recipients:
          type: array
          items:
            $ref: '#/components/schemas/RecipientDetails'
        status:
          $ref: '#/components/schemas/FiscalDocumentStatus'
        origin:
          type: string
        externalId:
          type: string
        failReason:
          type: string
        createdAt:
          type: string
          format: date-time
        parentId:
          type: string
        updatedAt:
          type: string
          format: date-time
        governmentIntegration:
          $ref: '#/components/schemas/GovernmentIntegration'
        actions:
          $ref: '#/components/schemas/Actions'

    FiscalDocumentSummaryResponse:
      type: object
      properties:
        id:
          type: string
        kind:
          $ref: '#/components/schemas/FiscalDocumentKind'
        status:
          $ref: '#/components/schemas/FiscalDocumentStatus'
        governmentIntegration:
          $ref: '#/components/schemas/GovernmentIntegration'
        linkedTo:
          type: string

    FiscalDocumentPaginated:
      type: object
      properties:
        fiscalDocuments:
          type: array
          items:
            $ref: '#/components/schemas/FiscalDocumentDetailedResponse'
        nextPageToken:
          $ref: '#/components/schemas/NextPageToken'

    FiscalDocumentTransactionResponse:
      type: object
      properties:
        id:
          type: string
        propertyId:
          type: string
        sourceId:
          type: string
        sourceKind:
          $ref: "#/components/schemas/SourceKind"
        transactionDate:
          type: string
          format: date-time
        guestName:
          type: string
        description:
          type: string
        internalCode:
          type: string
        amount:
          type: number
        folioId:
          type: string

    FiscalDocumentTransactionsPaginated:
      type: object
      properties:
        transactions:
          type: array
          items:
            $ref: '#/components/schemas/FiscalDocumentTransactionResponse'
        nextPageToken:
          $ref: '#/components/schemas/NextPageToken'
    ConfigsUpdateRequest:
      type: object
      required: [showDetailedTaxFee, chargeBreakdown, useGuestLang, showLegalCompanyName, includeRoomNumber, isCompact, useDocumentNumber]
      properties:
        showDetailedTaxFee:
          type: boolean
        chargeBreakdown:
          type: boolean
        useGuestLang:
          type: boolean
        dueDays:
          type: integer
          format: int32
          nullable: true
        lang:
          type: string
          nullable: true
        prefix:
          type: string
          nullable: true
        suffix:
          type: string
          nullable: true
        legalCompanyName:
          type: string
          nullable: true
        title:
          type: object
          nullable: true
          additionalProperties:
            type: string
        showLegalCompanyName:
          type: boolean
        includeRoomNumber:
          type: boolean
        useDocumentNumber:
          type: boolean
        isCompact:
          type: boolean
        taxId1:
          type: string
          nullable: true
        taxId2:
          type: string
          nullable: true
        cpf:
          type: string
          nullable: true
        customText:
          type: object
          nullable: true
          additionalProperties:
            type: string
    FiscalDocumentPatchRequest:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/FiscalDocumentStatus'
        failReason:
          type: string
        governmentIntegration:
          $ref: '#/components/schemas/GovernmentIntegration'

    RecipientDetails:
      type: object
      properties:
        id:
          type: string
          minLength: 1
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
          format: email
        type:
          $ref: '#/components/schemas/RecipientType'
        companyName:
          type: string
    FiscalDocumentRecipient:
      type: object
      properties:
        id:
          type: string
          minLength: 1
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
          format: email
        type:
          $ref: '#/components/schemas/RecipientType'
        address:
          $ref: '#/components/schemas/RecipientAddress'
        tax:
          $ref: '#/components/schemas/RecipientTaxInfo'
        contactDetails:
          $ref: '#/components/schemas/RecipientContactDetails'
        document:
          $ref: '#/components/schemas/RecipientDocument'
        countryData:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/AnyValue'
          description: |
            Arbitrary country-specific fields from guest requirements.

    RecipientType:
      type: string
      enum: [COMPANY, PERSON]

    RecipientAddress:
      type: object
      properties:
        address1:
          type: string
        address2:
          type: string
        city:
          type: string
        state:
          type: string
        zipCode:
          type: string
        country:
          type: string

    RecipientTaxInfo:
      type: object
      properties:
        id:
          type: string
        companyName:
          type: string

    RecipientContactDetails:
      type: object
      properties:
        phone:
          type: string
        gender:
          type: string
        cellPhone:
          type: string
        birthday:
          type: string
          format: date-time

    RecipientDocument:
      type: object
      properties:
        type:
          type: string
        number:
          type: string
        issuingCountry:
          type: string
        issueDate:
          type: string
          format: date-time
        expirationDate:
          type: string
          format: date-time
    GovernmentIntegration:
      type: object
      properties:
        number:
          type: string
        series:
          type: string
        status:
          type: string
        qr:
          type: object
          properties:
            url:
              type: string
              format: uri
            string:
              type: string
        url:
          type: string
          format: uri
        officialId:
          type: string
        externalId:
          type: string
        rectifyingInvoiceType:
          type: string
    ConfigsResponse:
      type: object
      properties:
        propertyId:
          type: string
        documentKind:
          $ref: '#/components/schemas/FiscalDocumentKind'
        showDetailedTaxFee:
          type: boolean
        chargeBreakdown:
          type: boolean
        useGuestLang:
          type: boolean
        dueDays:
          type: integer
          format: int32
        lang:
          type: string
        prefix:
          type: string
        suffix:
          type: string
        legalCompanyName:
          type: string
        title:
          type: object
          additionalProperties:
            type: string
        showLegalCompanyName:
          type: boolean
        includeRoomNumber:
          type: boolean
        useDocumentNumber:
          type: boolean
        isCompact:
          type: boolean
        taxId1:
          type: string
        taxId2:
          type: string
        cpf:
          type: string
        customText:
          type: object
          additionalProperties:
            type: string
    ApiError:
      type: object
      properties:
        id:
          type: string
        timestamp:
          type: string
          format: date-time
        errorCode:
          type: string
        errorDetails:
          type: string
    Actions:
      type: array
      description: Returns the list of actions available for the transaction
      items:
        $ref: '#/components/schemas/Action'
    Action:
      type: object
      properties:
        type:
          $ref: "#/components/schemas/DocumentAction"
    NextPageToken:
      type: string
      description: Token for fetching the next page of results
    # ENUMS
    DocumentAction:
      type: string
      description: Action that can be performed on a fiscal document
      enum:
        - CANCEL
        - RECTIFY
        - DOWNLOAD
        - CREDIT_NOTE
        - VOID
        - ADD_PAYMENT
    SourceKind:
      type: string
      description: Kind of the source entity
      enum:
        - GROUP_PROFILE
        - RESERVATION
        - HOUSE_ACCOUNT
        - ACCOUNTS_RECEIVABLE_LEDGER
    FiscalDocumentStatus:
      type: string
      description: Status of the fiscal document
      enum:
        - COMPLETED
        - VOIDED
        - PAID
        - PENDING_INTEGRATION
        - COMPLETED_INTEGRATION
        - FAILED_INTEGRATION
        - CORRECTION_NEEDED
        - CANCELED
        - OPEN
        - REQUESTED
        - VOID_REQUESTED
        - FAILED
        - MANUALLY_RECONCILED
        - REJECTED
    FiscalDocumentKind:
      type: string
      description: Kind of fiscal document
      enum:
        - INVOICE
        - CREDIT_NOTE
        - RECEIPT
        - RECTIFY_INVOICE

    CreationMethod:
      type: string
      enum:
        - VOID
        - ADJUSTMENT