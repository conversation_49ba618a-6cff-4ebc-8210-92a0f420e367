name: Pull Request

on:
  pull_request:

defaults:
  run:
    shell: bash

jobs:
  lint:
    name: Static Code Analysis 🏗️
    runs-on: default
    env:
      GITHUB_USER: cloudbeds-ci
      GITHUB_TOKEN: ${{ secrets.CB_GH_CI_TOKEN_PACKAGES_RO }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: "21"
          distribution: "corretto"
          cache: gradle

      - name: Run Checkstyle on main
        run: ./gradlew checkstyleMain

      - name: Run Checkstyle on tests
        run: ./gradlew checkstyleTest

  
  unit-tests:
    name: Unit Tests and Code Coverage 🧪
    runs-on: default
    steps:
      - name: Unit Tests
        uses: cloudbeds/composite-actions/java/unit-test-code-coverage@v2
        env:
          GITHUB_USER: cloudbeds-ci
          GITHUB_TOKEN: ${{ secrets.CB_GH_CI_TOKEN_PACKAGES_RO }}
        with:
          java_version: '21'
          java_distribution: 'corretto'
          github_token: ${{ github.token }}
          report_badge_threshold: 5%
          jacoco_min_coverage_overall: 5
          jacoco_min_coverage_changed_files: 5
      - name: Archive test results
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: test-report
          path: build/reports/tests
          retention-days: 1

  docker-build:
    strategy:
      fail-fast: false
      matrix:
        container:
          - app
          - liquibase
        include:
          - container: app
            image_name: ${{ github.event.repository.name }}
            dockerfile_path: docker/Dockerfile
          - container: liquibase
            image_name: ${{ github.event.repository.name }}/liquibase
            dockerfile_path: liquibase/Dockerfile
    name: Docker Build 🐋
    runs-on: default
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Docker Build
        uses: cloudbeds/composite-actions/docker/build-push/aws-ecr@v2
        with:
          image_name: ${{ matrix.image_name }}
          dockerfile_path: ${{ matrix.dockerfile_path }}
          docker_build_args: |
            GITHUB_TOKEN=${{ secrets.CB_GH_CI_TOKEN_PACKAGES_RO }}
          java_version: '21'
          java_distribution: 'corretto'
          push: false
