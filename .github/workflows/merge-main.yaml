name: Merge Main

on:
 push:
    branches:
      - main
    # list all files and dirs that should be watched for changes
    paths:
      - '*.gradle'
      - 'src/**'
      - 'docker/**'
      - 'gradle/**'
      - 'liquibase/**'
      - '.github/**'

defaults:
  run:
    shell: bash

env:
  GH_TOKEN_APP_NAME: CBAccounting
  GH_TOKEN_REPOSITORIES: >-
    ["argocd-fiscal-document"]

permissions:
  id-token: write
  contents: read

jobs:
  docker-build:
    strategy:
      fail-fast: false
      matrix:
        container:
          - app
          - liquibase
        include:
          - container: app
            image_name: ${{ github.event.repository.name }}
            dockerfile_path: docker/Dockerfile
          - container: liquibase
            image_name: ${{ github.event.repository.name }}/liquibase
            dockerfile_path: liquibase/Dockerfile
    name: Docker Build 🐋
    runs-on: default
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Docker Build
        uses: cloudbeds/composite-actions/docker/build-push/aws-ecr@v2
        with:
          image_name: ${{ matrix.image_name }}
          dockerfile_path: ${{ matrix.dockerfile_path }}
          docker_build_args: |
            GITHUB_TOKEN=${{ secrets.CB_GH_CI_TOKEN_PACKAGES_RO }}
          push: true
 
  deploy-stage:
    name: Deploy stage 📜
    needs: docker-build
    runs-on: x1-core
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: ${{ env.GH_TOKEN_REPOSITORIES }}
          app_name: ${{ env.GH_TOKEN_APP_NAME }}

      - name: Deploy to stage-us2
        uses: cloudbeds/trigger-workflow-and-wait@v1.6.5
        with:
          owner: cloudbeds
          repo: argocd-fiscal-document
          github_token: ${{ steps.gh-app-token.outputs.github-token }}
          workflow_file_name: update-application.yaml
          client_payload: |
            {
              "cluster": "stage-us2",
              "app_name": "${{ github.event.repository.name }}",
              "image_tag": "${{ github.sha }}",
              "remote_repo": "${{ github.repository }}",
              "remote_workflow_url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }
