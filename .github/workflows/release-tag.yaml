name: Re-tag images and Publish

on:
  push:
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+"

env:
  GH_TOKEN_APP_NAME: CBAccounting
  GH_TOKEN_REPOSITORIES: >-
    ["argocd-fiscal-document"]

permissions:
  id-token: write
  contents: read

jobs:
  re-tag:
    strategy:
      fail-fast: false
      matrix:
        container:
          - app
          - liquibase
        include:
          - container: app
            image_name: ${{ github.event.repository.name }}
          - container: liquibase
            image_name: ${{ github.event.repository.name }}/liquibase
    name: Re-tag Image ${{ matrix.container }} 🐋
    runs-on: x1-core
    steps:
      - name: Re-tag ${{ matrix.container }} image
        uses: cloudbeds/composite-actions/docker/crane-re-tag/aws-ecr@v2
        with:
          image_name: ${{ matrix.image_name }}
          src_tag: ${{ github.sha }}
          dst_tag: ${{ github.ref_name }}

  update-stage-us1:
    name: Deploy stage-us1 📜
    needs: re-tag
    environment: stage
    runs-on: x1-core
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: ${{ env.GH_TOKEN_REPOSITORIES }}
          app_name: ${{ env.GH_TOKEN_APP_NAME }}

      - name: Deploy ArgoCD
        uses: cloudbeds/trigger-workflow-and-wait@v1.6.5
        with:
          owner: cloudbeds
          repo: argocd-fiscal-document
          github_token:  ${{ steps.gh-app-token.outputs.github-token }}
          workflow_file_name: update-application.yaml
          client_payload: |
            {
              "cluster": "stage-us1",
              "app_name": "${{ github.event.repository.name }}",
              "image_tag": "${{ github.ref_name }}",
              "remote_repo": "${{ github.repository }}",
              "remote_workflow_url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }

  update-prod-us2:
    name: Update prod-us2 cluster 📜
    needs: update-stage-us1
    environment: prod
    runs-on: default
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: ${{ env.GH_TOKEN_REPOSITORIES }}
          app_name: ${{ env.GH_TOKEN_APP_NAME }}

      - name: Update deployment manifest in prod-us2
        uses: cloudbeds/trigger-workflow-and-wait@v1.6.5
        with:
          owner: cloudbeds
          repo: argocd-fiscal-document
          github_token: ${{ steps.gh-app-token.outputs.github-token }}
          ref: main
          workflow_file_name: update-application.yaml
          client_payload: |
            {
              "cluster": "prod-us2",
              "app_name": "${{ github.event.repository.name }}",
              "image_tag": "${{ github.ref_name }}",
              "remote_repo": "${{ github.repository }}",
              "remote_workflow_url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }

  update-prod-us1:
    name: Update prod-us1 cluster 📜
    needs: update-prod-us2
    environment: prod
    runs-on: default
    steps:
      - name: Get GH app token
        id: gh-app-token
        uses: cloudbeds/composite-actions/gh-app-token@v2
        with:
          repositories: ${{ env.GH_TOKEN_REPOSITORIES }}
          app_name: ${{ env.GH_TOKEN_APP_NAME }}

      - name: Update deployment manifest in prod-us1
        uses: cloudbeds/trigger-workflow-and-wait@v1.6.5
        with:
          owner: cloudbeds
          repo: argocd-fiscal-document
          github_token: ${{ steps.gh-app-token.outputs.github-token }}
          ref: main
          workflow_file_name: update-application.yaml
          client_payload: |
            {
              "cluster": "prod-us1",
              "app_name": "${{ github.event.repository.name }}",
              "image_tag": "${{ github.ref_name }}",
              "remote_repo": "${{ github.repository }}",
              "remote_workflow_url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }
