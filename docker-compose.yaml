services:
  db:
    container_name: ${COMPOSE_PROJECT_NAME}-db
    build:
      context: local/postgres
      args:
        POSTGRES_DB: ${DB_NAME}
        PG_MAJOR_VERSION: 17
        PG_MINOR_VERSION: 4
    ports:
      - ${DB_PORT}:5432
    volumes:
      - db-data:/var/lib/postgresql/data
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_HOST_AUTH_METHOD: trust
    healthcheck:
      test: pg_isready --username "$${POSTGRES_USER}" --dbname "$${POSTGRES_DB}"
      timeout: 5s
      retries: 10

  liquibase:
    container_name: ${COMPOSE_PROJECT_NAME}-liquibase
    build:
      dockerfile: liquibase/Dockerfile
    environment:
      DB_ENGINE: ${DB_ENGINE}
      DB_HOST: db
      DB_NAME: ${DB_NAME}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_PORT: 5432
      DB_URL: ${DB_URL}
      DB_USER: ${DB_USER}
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - ./src/main/resources/db/changelog/sql:/liquibase/migrations/sql
      - ./src/main/resources/db/changelog/databaseChangeLog.xml:/liquibase/migrations/databaseChangeLog.xml
    tmpfs:
      - /liquibase/changelog
      - /liquibase/classpath
    tty: true
    depends_on:
      db:
        condition: service_healthy

  datadog-agent:
    image: gcr.io/datadoghq/agent:latest
    container_name: ${COMPOSE_PROJECT_NAME}-datadog-agent
    pid: host
    cgroup_parent: host
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /proc/:/host/proc/:ro
      - /sys/fs/cgroup/:/host/sys/fs/cgroup:ro
    environment:
      DD_APM_ENABLED: 1
      DD_APM_NON_LOCAL_TRAFFIC: 1
      DD_API_KEY: ${DATADOG_API_KEY}
    ports:
      - 8126:8126
    profiles: [ "log" ]

  distributed-id-service:
    image: ${DOCKER_CLOUDBEDS_REGISTRY:-210409647864.dkr.ecr.us-west-2.amazonaws.com}/distributed-id-service:v0.11.0
    env_file:
      - .env
    environment:
      MACHINE_ID: 63
      GRPC_PORT: ${DISTRIBUTED_ID_SERVICE_PORT:-19090}
      DISTRIBUTED_ID_CHANNEL_TYPE: PLAINTEXT
      ENABLE_DEBUG: "true"
    ports:
      - ${DISTRIBUTED_ID_SERVICE_PORT:-19090}:${DISTRIBUTED_ID_SERVICE_PORT:-19090}

  kong:
    platform: linux/amd64
    container_name: kong
    image: revomatico/docker-kong-oidc:2.8.1-1
    ports:
      - "8000:8000"
      - "8001:8001"
      - "8002:8002"
      - "8443:8443"
    environment:
      KONG_DATABASE: "off"
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_PROXY_LISTEN: "0.0.0.0:8000"
      KONG_ADMIN_LISTEN: "0.0.0.0:8001"
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_DECLARATIVE_CONFIG: "/opt/kong/declarative/kong.yaml"
      KONG_CONNECT_TIMEOUT: 300000
      KONG_READ_TIMEOUT: 300000
      KONG_WRITE_TIMEOUT: 3000000
      KONG_RETRIES: 3
      KONG_PLUGINS: bundled,rate-limiting,oidc
      KONG_X_SESSION_NAME: oidc_session
      NGINX_UPSTREAM_CONNECT_TIMEOUT: 300000
    volumes:
      - ./local/kong:/opt/kong/declarative/

  redis:
    container_name: ${COMPOSE_PROJECT_NAME}-redis
    image: valkey/valkey:8.0-alpine
    restart: always
    ports:
      - ${REDIS_PORT:-6379}:6379
    command: redis-server --save 20 1 --loglevel warning --appendonly yes
    volumes:
      - redis-data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 1s
      timeout: 2s
      retries: 5

  fiscal-document-aws:
    container_name: fiscal-document-aws
    image: localstack/localstack:3.8.1
    ports:
      - "127.0.0.1:4566:4566" # LocalStack edge port
      - "127.0.0.1:4510-4559:4510-4559" # External services port range
    environment:
      - SERVICES=s3
      - DEBUG=1
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
  create-bucket:
    image: amazon/aws-cli:2.26.6
    environment:
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - AWS_DEFAULT_REGION=us-east-1
    command: >
      --endpoint-url=http://localstack:4566 s3 mb s3://fiscal-document
    depends_on:
      - fiscal-document-aws

  zookeeper:
    image: confluentinc/cp-zookeeper:7.2.1
    hostname: zookeeper
    container_name: ${COMPOSE_PROJECT_NAME}-zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  broker:
    image: confluentinc/cp-server:7.2.1
    hostname: broker
    container_name: ${COMPOSE_PROJECT_NAME}-broker
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    expose:
      - '29092'
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://broker:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_METRIC_REPORTERS: io.confluent.metrics.reporter.ConfluentMetricsReporter
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_CONFLUENT_LICENSE_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_CONFLUENT_BALANCER_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_CONFLUENT_SCHEMA_REGISTRY_URL: http://schema-registry:8081
      CONFLUENT_METRICS_REPORTER_BOOTSTRAP_SERVERS: broker:29092
      CONFLUENT_METRICS_REPORTER_TOPIC_REPLICAS: 1
      CONFLUENT_METRICS_ENABLE: 'true'
      CONFLUENT_SUPPORT_CUSTOMER_ID: 'anonymous'

  init-kafka:
    image: confluentinc/cp-kafka:7.2.1
    depends_on:
      - broker
    env_file:
      - .env
    entrypoint: [ '/bin/sh', '-c' ]
    command: |
      "
      # blocks until kafka is reachable
      kafka-topics --bootstrap-server broker:29092 --list

      echo -e 'Creating kafka topics'

      kafka-topics --bootstrap-server broker:29092 --create --if-not-exists --topic ${ISLAND}.service.fiscal-document.fiscal_document_events --replication-factor 1 --partitions 3
      kafka-topics --bootstrap-server broker:29092 --create --if-not-exists --topic ${ISLAND}.service.fiscal-document.fiscal_document --replication-factor 1 --partitions 3
  
      echo -e 'Successfully created the following topics:'
      kafka-topics --bootstrap-server broker:29092 --list
      "
  schema-registry:
    image: confluentinc/cp-schema-registry:7.2.1
    hostname: schema-registry
    container_name: ${COMPOSE_PROJECT_NAME}-schema-registry
    depends_on:
      - broker
    ports:
      - "8081:8081"

    environment:
      SCHEMA_REGISTRY_HOST_NAME: schema-registry
      SCHEMA_REGISTRY_KAFKASTORE_BOOTSTRAP_SERVERS: 'broker:29092'
      SCHEMA_REGISTRY_LISTENERS: http://0.0.0.0:8081

  connect:
    image: cnfldemos/cp-server-connect-datagen:0.5.3-7.1.0
    hostname: connect
    container_name: ${COMPOSE_PROJECT_NAME}-connect
    depends_on:
      - broker
      - schema-registry
    ports:
      - "8083:8083"
    environment:
      CONNECT_BOOTSTRAP_SERVERS: 'broker:29092'
      CONNECT_REST_ADVERTISED_HOST_NAME: connect
      CONNECT_GROUP_ID: compose-connect-group
      CONNECT_CONFIG_STORAGE_TOPIC: docker-connect-configs
      CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_OFFSET_FLUSH_INTERVAL_MS: 10000
      CONNECT_OFFSET_STORAGE_TOPIC: docker-connect-offsets
      CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_STATUS_STORAGE_TOPIC: docker-connect-status
      CONNECT_STATUS_STORAGE_REPLICATION_FACTOR: 1
      CONNECT_KEY_CONVERTER: io.confluent.connect.avro.AvroConverter
      CONNECT_KEY_CONVERTER_SCHEMA_REGISTRY_URL: http://schema-registry:8081
      CONNECT_VALUE_CONVERTER: io.confluent.connect.avro.AvroConverter
      CONNECT_VALUE_CONVERTER_SCHEMA_REGISTRY_URL: http://schema-registry:8081
      # CLASSPATH required due to CC-2422
      CLASSPATH: /usr/share/java/monitoring-interceptors/monitoring-interceptors-7.2.1.jar
      CONNECT_PRODUCER_INTERCEPTOR_CLASSES: "io.confluent.monitoring.clients.interceptor.MonitoringProducerInterceptor"
      CONNECT_CONSUMER_INTERCEPTOR_CLASSES: "io.confluent.monitoring.clients.interceptor.MonitoringConsumerInterceptor"
      CONNECT_PLUGIN_PATH: "/usr/share/java,/usr/share/confluent-hub-components"
      CONNECT_LOG4J_LOGGERS: org.apache.zookeeper=ERROR,org.I0Itec.zkclient=ERROR,org.reflections=ERROR

  control-center:
    image: confluentinc/cp-enterprise-control-center:7.2.1
    hostname: control-center
    container_name: ${COMPOSE_PROJECT_NAME}-control-center
    depends_on:
      - broker
      - schema-registry
      - connect
      - ksqldb-server
    ports:
      - "9021:9021"
    environment:
      CONTROL_CENTER_BOOTSTRAP_SERVERS: 'broker:29092'
      CONTROL_CENTER_CONNECT_CONNECT-DEFAULT_CLUSTER: 'connect:8083'
      CONTROL_CENTER_KSQL_KSQLDB1_URL: "http://ksqldb-server:8088"
      CONTROL_CENTER_KSQL_KSQLDB1_ADVERTISED_URL: "http://localhost:8088"
      CONTROL_CENTER_SCHEMA_REGISTRY_URL: "http://schema-registry:8081"
      CONTROL_CENTER_REPLICATION_FACTOR: 1
      CONTROL_CENTER_INTERNAL_TOPICS_PARTITIONS: 1
      CONTROL_CENTER_MONITORING_INTERCEPTOR_TOPIC_PARTITIONS: 1
      CONFLUENT_METRICS_TOPIC_REPLICATION: 1
      PORT: 9021

  ksqldb-server:
    image: confluentinc/cp-ksqldb-server:7.2.1
    hostname: ksqldb-server
    container_name: ${COMPOSE_PROJECT_NAME}-ksqldb-server
    depends_on:
      - broker
      - connect
    ports:
      - "8088:8088"
    environment:
      KSQL_CONFIG_DIR: "/etc/ksql"
      KSQL_BOOTSTRAP_SERVERS: "broker:29092"
      KSQL_HOST_NAME: ksqldb-server
      KSQL_LISTENERS: "http://0.0.0.0:8088"
      KSQL_CACHE_MAX_BYTES_BUFFERING: 0
      KSQL_KSQL_SCHEMA_REGISTRY_URL: "http://schema-registry:8081"
      KSQL_PRODUCER_INTERCEPTOR_CLASSES: "io.confluent.monitoring.clients.interceptor.MonitoringProducerInterceptor"
      KSQL_CONSUMER_INTERCEPTOR_CLASSES: "io.confluent.monitoring.clients.interceptor.MonitoringConsumerInterceptor"
      KSQL_KSQL_CONNECT_URL: "http://connect:8083"
      KSQL_KSQL_LOGGING_PROCESSING_TOPIC_REPLICATION_FACTOR: 1
      KSQL_KSQL_LOGGING_PROCESSING_TOPIC_AUTO_CREATE: 'true'
      KSQL_KSQL_LOGGING_PROCESSING_STREAM_AUTO_CREATE: 'true'

  ksqldb-cli:
    image: confluentinc/cp-ksqldb-cli:7.2.1
    container_name: ${COMPOSE_PROJECT_NAME}-ksqldb-cli
    depends_on:
      - broker
      - connect
      - ksqldb-server
    entrypoint: /bin/sh
    tty: true

  ksql-datagen:
    image: confluentinc/ksqldb-examples:7.2.1
    hostname: ksql-datagen
    container_name: ${COMPOSE_PROJECT_NAME}-ksql-datagen
    depends_on:
      - ksqldb-server
      - broker
      - schema-registry
      - connect
    command: "bash -c 'echo Waiting for Kafka to be ready... && \
                       cub kafka-ready -b broker:29092 1 40 && \
                       echo Waiting for Confluent Schema Registry to be ready... && \
                       cub sr-ready schema-registry 8081 40 && \
                       echo Waiting a few seconds for topic creation to finish... && \
                       sleep 11 && \
                       tail -f /dev/null'"
    environment:
      KSQL_CONFIG_DIR: "/etc/ksql"
      STREAMS_BOOTSTRAP_SERVERS: broker:29092
      STREAMS_SCHEMA_REGISTRY_HOST: schema-registry
      STREAMS_SCHEMA_REGISTRY_PORT: 8081

  rest-proxy:
    image: confluentinc/cp-kafka-rest:7.2.1
    depends_on:
      - broker
      - schema-registry
    ports:
      - 8082:8082
    hostname: rest-proxy
    container_name: ${COMPOSE_PROJECT_NAME}-rest-proxy
    environment:
      KAFKA_REST_HOST_NAME: rest-proxy
      KAFKA_REST_BOOTSTRAP_SERVERS: 'broker:29092'
      KAFKA_REST_LISTENERS: "http://0.0.0.0:8082"
      KAFKA_REST_SCHEMA_REGISTRY_URL: 'http://schema-registry:8081'

volumes:
  db-data:
  redis-data:
