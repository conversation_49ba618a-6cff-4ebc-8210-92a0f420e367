_format_version: "2.1"
_transform: true

services:
  - name: fiscal-document
    host: host.docker.internal
    port: $APP_PORT
    routes:
      - name: fiscal-document-options
        strip_path: true
        regex_priority: 1
        methods:
          - OPTIONS
        paths:
          - /fiscal-document/v1

      - name: fiscal-document-swagger
        strip_path: false
        regex_priority: 2
        methods:
          - GET
        paths:
          - /fiscal-document/swagger-ui
          - /fiscal-document/v3/api-docs/swagger-config
          - /fiscal-document/openapi.yaml

      - name: fiscal-document-oidc
        strip_path: true
        regex_priority: 3
        methods:
          - GET
          - PUT
          - DELETE
          - POST
          - PATCH
        paths:
          - /fiscal-document/v1

    plugins:
      - name: rate-limiting
        route: fiscal-document-swagger
        config:
          second: 30
          policy: local

      - name: rate-limiting
        route: fiscal-document-options
        config:
          second: 30
          policy: local

      - name: rate-limiting
        route: fiscal-document-oidc
        config:
          second: 30
          policy: local

      - name: oidc
        route: fiscal-document-oidc
        config:
          response_type: code
          token_endpoint_auth_method: client_secret_post
          discovery: $KONG_OIDC_DISCOVERY
          introspection_endpoint: $KONG_OIDC_INSTROSPECTION_ENDPOINT
          client_id: $KONG_OIDC_CLIENT_ID
          client_secret: $KONG_OIDC_CLIENT_SECRET
          session_secret: $KONG_OIDC_SESSION_SECRET
          unauth_action: deny
          bearer_jwt_auth_enable: "yes"
          bearer_only: "yes"
          ssl_verify: "no"
