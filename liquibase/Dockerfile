FROM liquibase/liquibase:4.27

WORKDIR /liquibase
RUN mkdir -p migrations/sql

COPY liquibase/liquibase.properties .
COPY src/main/resources/db/changelog/databaseChangeLog.xml migrations/
COPY src/main/resources/db/changelog/sql/*.sql migrations/sql/

CMD ["sh", "-c", "liquibase --url=jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?useSSL=true\\&currentSchema=public --username=${DB_USER} --password=${DB_PASSWORD} --defaultsFile=liquibase.properties --log-level=DEBUG update"]
