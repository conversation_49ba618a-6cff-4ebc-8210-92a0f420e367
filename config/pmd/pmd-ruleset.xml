<?xml version="1.0" ?>
<ruleset name="Project custom rules"
         xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.io/ruleset_2_0_0.xsd">

    <description>Custom rules for this project</description>

    <rule ref="category/java/bestpractices.xml">
        <exclude name="GuardLogStatement" />
        <exclude name="JUnitTestContainsTooManyAsserts" />
        <exclude name="JUnitTestsShouldIncludeAssert" />
        <exclude name="JUnitAssertionsShouldIncludeMessage" />
    </rule>

    <rule ref="category/java/codestyle.xml">
        <exclude name="AtLeastOneConstructor" />
        <exclude name="CommentDefaultAccessModifier" />
        <exclude name="LocalVariableCouldBeFinal" />
        <exclude name="LongVariable" />
        <exclude name="MethodArgumentCouldBeFinal" />
        <exclude name="OnlyOneReturn" />
        <exclude name="ShortVariable" />
        <exclude name="UnnecessaryAnnotationValueElement" />
        <exclude name="UseExplicitTypes" />
    </rule>

    <rule ref="category/java/design.xml">
        <exclude name="LawOfDemeter" />
        <exclude name="SignatureDeclareThrowsException" />
    </rule>

    <rule ref="category/java/errorprone.xml">
        <exclude name="AvoidFieldNameMatchingTypeName" />
    </rule>

    <rule ref="category/java/performance.xml">
    </rule>

</ruleset>